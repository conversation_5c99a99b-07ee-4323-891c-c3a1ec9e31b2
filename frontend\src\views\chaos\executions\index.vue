<template>
  <div class="chaos-executions art-full-height">
    <!-- 搜索栏 -->
    <ExecutionSearch v-model:filter="searchForm" @reset="handleReset" @search="handleSearch" />

    <ElCard class="art-table-card" shadow="never">

      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton
            v-if="selectedExecutions.length > 0"
            @click="handleBatchRetry"
            :loading="loading"
          >
            批量重试
          </ElButton>
        </template>
        <template #right>
          <ElButton @click="handleExportRecords" type="success" style="margin-left: 5px;">导出记录</ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        :loading="loading"
        :data="executionList"
        :columns="columns"
        :pagination="paginationState"
        @selection-change="handleSelectionChange"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >

        <!-- 状态插槽 -->
        <template #status="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ getStatusLabel(row.status) }}
          </el-tag>
        </template>



        <!-- 操作插槽 -->
        <template #operation="{ row }">
          <el-button size="small" type="primary" @click="handleViewExecution(row)">
            详情
          </el-button>
          <el-button
            size="small"
            type="success"
            @click="handleViewMonitor(row)"
            style="margin-left: 8px;"
          >
            监控
          </el-button>
        </template>
      </ArtTable>
    </ElCard>

    <!-- 执行日志对话框 -->
    <el-dialog
      v-model="logDialogVisible"
      title="执行日志"
      width="800px"
      destroy-on-close
    >
      <div class="log-dialog">
        <div class="log-tabs">
          <el-tabs v-model="activeLogTab" @tab-change="handleLogTabChange">
            <el-tab-pane label="执行输出" name="output" />
            <el-tab-pane label="错误信息" name="error" />
            <el-tab-pane label="执行命令" name="command" />
            <el-tab-pane label="销毁输出" name="destroy" />
          </el-tabs>
        </div>
        <div class="log-content">
          <el-input
            v-model="logContent"
            type="textarea"
            :rows="15"
            readonly
            placeholder="暂无日志内容"
          />
        </div>
      </div>
      <template #footer>
        <el-button @click="logDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleCopyLog">复制日志</el-button>
      </template>
    </el-dialog>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElButton, ElCard } from 'element-plus'
import ArtTable from '@/components/core/tables/art-table/index.vue'
import ArtTableHeader from '@/components/core/tables/art-table-header/index.vue'
import ExecutionSearch from './modules/execution-search.vue'
import { useTable } from '@/composables/useTable'
import { useChaosExecutionsStore } from '@/store/business/chaos/executions'
import ChaosService from '@/api/chaosApi'
import type { ChaosExecution, ChaosExecutionSearchParams } from '@/types/api/chaos'

defineOptions({ name: 'ChaosExecutions' })

const router = useRouter()
const route = useRoute()
const chaosExecutionsStore = useChaosExecutionsStore()

// 选中的执行记录
const selectedExecutions = ref<ChaosExecution[]>([])
const statistics = ref<any>(null)

// 对话框状态
const logDialogVisible = ref(false)
const activeLogTab = ref('output')
const logContent = ref('')
const currentExecutionId = ref<number | null>(null)

// 搜索表单
const searchForm = reactive<ChaosExecutionSearchParams>({
  task_id: undefined,
  task_name: '',
  status: '',
  page: 1,
  size: 20,
  order_by: 'created_at',
  desc: true
})

// 表格配置
const {
  tableData: executionList,
  isLoading: loading,
  paginationState,
  columns,
  columnChecks,
  onPageSizeChange,
  onCurrentPageChange,
  loadData: refreshData
} = useTable<ChaosExecution>({
  core: {
    apiFn: async (params) => {
      const result = await ChaosService.getExecutionList({ ...searchForm, ...params })
      return result
    },
    immediate: true,
    paginationKey: { current: 'page' }, // 使用 'page' 作为分页字段名
    columnsFactory: () => [
      { type: 'selection' as const, width: 55 },
      { prop: 'id', label: 'ID', width: 100 },
      { prop: 'task_name', label: '任务名称', minWidth: 300, showOverflowTooltip: true },
      { prop: 'status', label: '状态', width: 100, useSlot: true },
      { prop: 'chaos_uid', label: '故障ID', width: 200, showOverflowTooltip: true },
      { prop: 'created_at', label: '创建时间', width: 200, formatter: (row: any) => formatDateTime(row.created_at) },
      { prop: 'operation', label: '操作', width: 300, fixed: 'right' as const, useSlot: true }
    ]
  }
})

// 生命周期
onMounted(() => {
  // 检查URL参数
  if (route.query.task_id) {
    searchForm.task_id = Number(route.query.task_id)
  }

  loadStatistics()
})

// 方法
const refreshAll = () => {
  refreshData()
  loadStatistics()
}



const loadStatistics = async () => {
  try {
    statistics.value = await chaosExecutionsStore.getExecutionStatistics(searchForm.task_id)
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const handleSearch = () => {
  refreshData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    task_id: undefined,
    host_id: undefined,
    status: '',
    chaos_uid: '',
    start_time_from: '',
    start_time_to: '',
    page: 1,
    size: 20,
    order_by: 'created_at',
    desc: true
  })
  refreshData()
}

const handleExportRecords = () => {
  // 导出当前筛选的执行记录
  const exportData = executionList.value.map(execution => ({
    id: execution.id,
    task_id: execution.task_id,
    task_name: execution.task_name,
    host_id: execution.host_id,
    host_name: execution.host_name,
    status: execution.status,
    chaos_uid: execution.chaos_uid,
    start_time: execution.start_time,
    end_time: execution.end_time,
    duration_seconds: execution.duration_seconds,
    retry_count: execution.retry_count,
    exit_code: execution.exit_code,
    created_at: execution.created_at
  }))
  
  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `chaos-executions-${new Date().toISOString().slice(0, 10)}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('执行记录已导出')
}



const handleViewExecution = (execution: ChaosExecution) => {
  router.push(`/chaos/executions/${execution.id}`)
}

const handleViewMonitor = (execution: ChaosExecution) => {
  // 跳转到监控页面，传递任务关联的环境信息
  if (execution.task && execution.task.env_ids && execution.task.env_ids.length > 0) {
    // 使用任务关联的第一个环境
    const environmentId = execution.task.env_ids[0]
    router.push({
      path: '/chaos/monitor',
      query: {
        environmentId: environmentId
      }
    })
  } else {
    // 没有环境信息，直接跳转到监控页面
    router.push('/chaos/monitor')
  }
}











const handleLogTabChange = async (tabName: string | number) => {
  await loadLogContent(String(tabName))
}

const loadLogContent = async (logType: string) => {
  if (!currentExecutionId.value) return
  
  try {
    const response = await chaosExecutionsStore.getExecutionLog(currentExecutionId.value, logType)
    logContent.value = response.content || '暂无日志内容'
  } catch (error) {
    logContent.value = '加载日志失败'
  }
}



const handleCopyLog = () => {
  if (logContent.value) {
    navigator.clipboard.writeText(logContent.value)
    ElMessage.success('日志已复制到剪贴板')
  }
}

const handleSelectionChange = (selection: ChaosExecution[]) => {
  selectedExecutions.value = selection
}

const handleBatchRetry = async () => {
  if (!selectedExecutions.value || selectedExecutions.value.length === 0) {
    ElMessage.warning('请选择要重试的执行记录')
    return
  }

  const executionIds = selectedExecutions.value.map(e => e.id)
  await chaosExecutionsStore.batchExecutionOperation(executionIds, 'retry')
  ElMessage.success('批量重试成功')
  refreshData()
}







// 工具方法
const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待执行',
    running: '运行中',
    success: '成功',
    failed: '失败',
    cancelled: '已取消'
  }
  return labels[status as keyof typeof labels] || status
}

const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    running: 'primary',
    success: 'success',
    failed: 'danger',
    cancelled: ''
  }
  return (types[status as keyof typeof types] || '') as any
}

const formatDateTime = (dateTime: string | undefined) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}




</script>

<style scoped>
.host-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.host-address {
  font-weight: 500;
  color: #303133;
}

.host-user {
  font-size: 12px;
  color: #909399;
}

.text-gray-400 {
  color: #909399;
}

.log-dialog {
  padding: 10px 0;
}

.log-tabs {
  margin-bottom: 15px;
}

.log-content {
  max-height: 400px;
}

.monitor-dialog {
  padding: 10px 0;
}

.monitor-progress {
  margin-bottom: 20px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.progress-label {
  font-weight: 500;
  color: #333;
}

.progress-value {
  font-weight: 600;
  color: #409eff;
}

.monitor-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-row {
  display: flex;
  align-items: center;
}

.detail-row .label {
  width: 100px;
  color: #666;
  flex-shrink: 0;
}

.detail-row .value {
  color: #333;
  flex: 1;
}
</style>
