/**
 * 混沌测试相关常量定义
 * 统一管理场景分类和故障类型，确保整个系统的一致性
 */

// 场景分类定义
export const CHAOS_CATEGORIES = [
  { key: '系统资源', label: '系统资源', faultTypes: ['cpu', 'memory', 'disk'] },
  { key: '网络故障', label: '网络故障', faultTypes: ['network'] },
  { key: '应用故障', label: '应用故障', faultTypes: ['jvm', 'process'] },
  { key: '容器故障', label: '容器故障', faultTypes: ['docker', 'kubernetes'] }
] as const

// 故障类型定义
export const FAULT_TYPES = [
  // 系统资源
  { label: 'CPU故障', value: 'cpu', category: '系统资源' },
  { label: '内存故障', value: 'memory', category: '系统资源' },
  { label: '磁盘故障', value: 'disk', category: '系统资源' },
  // 网络故障
  { label: '网络故障', value: 'network', category: '网络故障' },
  // 应用故障
  { label: 'JVM故障', value: 'jvm', category: '应用故障' },
  { label: '进程故障', value: 'process', category: '应用故障' },
  // 容器故障
  { label: 'Docker故障', value: 'docker', category: '容器故障' },
  { label: 'Kubernetes故障', value: 'kubernetes', category: '容器故障' }
] as const

// 分类选项（用于下拉选择）
export const CATEGORY_OPTIONS = CHAOS_CATEGORIES.map(cat => ({
  label: cat.label,
  value: cat.key
}))

// 故障类型选项（用于下拉选择）
export const FAULT_TYPE_OPTIONS = FAULT_TYPES.map(type => ({
  label: type.label,
  value: type.value
}))

// 根据分类获取对应的故障类型
export const getFaultTypesByCategory = (category: string) => {
  const categoryData = CHAOS_CATEGORIES.find(cat => cat.key === category)
  return categoryData ? categoryData.faultTypes : []
}

// 根据故障类型获取对应的分类
export const getCategoryByFaultType = (faultType: string) => {
  const typeData = FAULT_TYPES.find(type => type.value === faultType)
  return typeData ? typeData.category : ''
}

// 获取故障类型的显示标签
export const getFaultTypeLabel = (faultType: string) => {
  const typeData = FAULT_TYPES.find(type => type.value === faultType)
  return typeData ? typeData.label : faultType
}

// 获取分类的显示标签
export const getCategoryLabel = (category: string) => {
  const categoryData = CHAOS_CATEGORIES.find(cat => cat.key === category)
  return categoryData ? categoryData.label : category
}

// 故障类型标签颜色映射
export const FAULT_TYPE_TAG_COLORS = {
  // 系统资源
  cpu: 'danger',
  memory: 'warning', 
  disk: 'info',
  // 网络故障
  network: 'primary',
  // 应用故障
  jvm: 'success',
  process: 'warning',
  // 容器故障
  docker: 'info',
  kubernetes: 'primary'
} as const

// 获取故障类型标签颜色
export const getFaultTypeTagColor = (faultType: string) => {
  return FAULT_TYPE_TAG_COLORS[faultType as keyof typeof FAULT_TYPE_TAG_COLORS] || 'default'
}
