"""
环境管理数据模型
"""
from sqlalchemy import Column, String, Integer, Text, JSON
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Environment(BaseModel):
    """
    环境配置模型
    支持多种环境资源类型的统一管理
    """
    __tablename__ = "environment"

    # 基础信息
    name = Column(String(100), unique=True, index=True, nullable=False, comment="环境名称")
    type = Column(String(50), nullable=False, index=True, comment="环境类型(database/redis/ssh/k8s/api/kafka等)")
    description = Column(Text, nullable=True, comment="环境描述")
    
    # 连接信息
    host = Column(String(255), nullable=True, comment="主机地址")
    port = Column(Integer, nullable=True, comment="端口号")
    
    # 扩展配置 - 使用JSON字段存储特定类型的配置
    config = Column(JSON, nullable=True, comment="环境特定配置(JSON格式)")
    
    # 状态信息
    status = Column(String(20), default="unknown", comment="连接状态: unknown/connected/failed")
    last_test_time = Column(String(50), nullable=True, comment="最后测试时间")
    
    # 环境标签 - 用于分组和筛选
    tags = Column(String(500), nullable=True, comment="环境标签，逗号分隔")

    # 关系映射
    # monitor_data = relationship("ChaosMonitorData", back_populates="environment", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        return f"<Environment(id={self.id}, name={self.name}, type={self.type})>"

    @property
    def is_connected(self) -> bool:
        """检查环境是否连接成功"""
        return self.status == "connected"

    @property
    def tag_list(self) -> list:
        """获取标签列表"""
        if not self.tags:
            return []
        return [tag.strip() for tag in self.tags.split(",") if tag.strip()]

    def add_tag(self, tag: str) -> None:
        """添加标签"""
        current_tags = self.tag_list
        if tag not in current_tags:
            current_tags.append(tag)
            self.tags = ",".join(current_tags)

    def remove_tag(self, tag: str) -> None:
        """移除标签"""
        current_tags = self.tag_list
        if tag in current_tags:
            current_tags.remove(tag)
            self.tags = ",".join(current_tags)

    def get_config_value(self, key: str, default=None):
        """获取配置值"""
        if not self.config:
            return default
        return self.config.get(key, default)

    def set_config_value(self, key: str, value) -> None:
        """设置配置值"""
        if not self.config:
            self.config = {}
        self.config[key] = value 