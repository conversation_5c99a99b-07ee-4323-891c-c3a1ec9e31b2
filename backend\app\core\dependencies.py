"""
统一依赖注入系统
提供服务层依赖管理和数据库会话注入
优化版本：支持Service实例缓存，减少重复创建开销
"""
import weakref
from typing import Annotated, Type, Callable, Any, Dict, Optional
from fastapi import Depends, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db
from app.schemas.common import PaginationParams, SearchParams


class ServiceDependency:
    """
    服务依赖基类
    所有服务类都应继承此类以获得统一的依赖注入能力
    """

    def __init__(self, db: AsyncSession):
        self.db = db


# 数据库会话依赖注解
DatabaseDep = Annotated[AsyncSession, Depends(get_db)]


# Service实例缓存 - 使用弱引用避免内存泄漏
_service_cache: Dict[str, weakref.WeakValueDictionary] = {}


def _get_service_cache_key(service_class: Type, db_session_id: str) -> str:
    """生成服务缓存键"""
    return f"{service_class.__name__}_{db_session_id}"


def _get_cached_service(service_class: Type, db: AsyncSession) -> Optional[Any]:
    """从缓存获取服务实例"""
    cache_key = _get_service_cache_key(service_class, str(id(db)))
    service_cache = _service_cache.get(service_class.__name__)
    if service_cache:
        return service_cache.get(cache_key)
    return None


def _cache_service(service_class: Type, db: AsyncSession, service_instance: Any) -> None:
    """缓存服务实例"""
    cache_key = _get_service_cache_key(service_class, str(id(db)))
    if service_class.__name__ not in _service_cache:
        _service_cache[service_class.__name__] = weakref.WeakValueDictionary()
    _service_cache[service_class.__name__][cache_key] = service_instance


# 分页参数依赖注入
def get_pagination_params(
    current: int = Query(1, ge=1, description="当前页码"),
    size: int = Query(20, ge=1, le=100, description="每页条数")
) -> PaginationParams:
    """获取分页参数"""
    return PaginationParams(current=current, size=size)


def get_search_params(
    current: int = Query(1, ge=1, description="当前页码"),
    size: int = Query(20, ge=1, le=100, description="每页条数"),
    keyword: str = Query(None, description="搜索关键词")
) -> SearchParams:
    """获取搜索参数"""
    return SearchParams(current=current, size=size, keyword=keyword)


# 通用服务依赖注入工厂函数
def create_service_dependency(service_class: Type[ServiceDependency]) -> Callable:
    """
    创建服务依赖注入函数的工厂函数
    """
    def get_service(db: DatabaseDep) -> service_class: # type: ignore
        return service_class(db)

    return get_service


# ==================== 具体服务依赖注入（优化版本） ====================

# 服务依赖注入函数（延迟导入避免循环依赖，支持缓存）
def get_user_service(db: DatabaseDep):
    """获取用户服务（支持缓存优化）"""
    from app.services.user.user import UserService

    # 尝试从缓存获取
    cached_service = _get_cached_service(UserService, db)
    if cached_service:
        return cached_service

    # 创建新实例并缓存
    service = UserService(db)
    _cache_service(UserService, db, service)
    return service

def get_environment_service(db: DatabaseDep):
    """获取环境服务（支持缓存优化）"""
    from app.services.env.env import EnvironmentService

    cached_service = _get_cached_service(EnvironmentService, db)
    if cached_service:
        return cached_service

    service = EnvironmentService(db)
    _cache_service(EnvironmentService, db, service)
    return service

def get_model_config_service(db: DatabaseDep):
    """获取模型配置服务（支持缓存优化）"""
    from app.services.model.model_config import ModelConfigService

    cached_service = _get_cached_service(ModelConfigService, db)
    if cached_service:
        return cached_service

    service = ModelConfigService(db)
    _cache_service(ModelConfigService, db, service)
    return service

def get_chaosblade_service():
    """获取ChaosBlade服务"""
    from app.services.chaos.chaosblade_service import ChaosBladeService
    return ChaosBladeService()

def get_chaos_task_service(db: DatabaseDep):
    """获取混沌任务服务（支持缓存优化）"""
    from app.services.chaos.chaos_task_service import ChaosTaskService

    cached_service = _get_cached_service(ChaosTaskService, db)
    if cached_service:
        return cached_service

    service = ChaosTaskService(db)
    _cache_service(ChaosTaskService, db, service)
    return service

def get_chaos_batch_task_service(db: DatabaseDep):
    """获取混沌批次任务服务（支持缓存优化）"""
    from app.services.chaos.chaos_batch_task_service import ChaosBatchTaskService

    cached_service = _get_cached_service(ChaosBatchTaskService, db)
    if cached_service:
        return cached_service

    service = ChaosBatchTaskService(db)
    _cache_service(ChaosBatchTaskService, db, service)
    return service

def get_chaos_scenario_service(db: DatabaseDep):
    """获取混沌场景服务（支持缓存优化）"""
    from app.services.chaos.chaos_scenario_service import ChaosScenarioService

    cached_service = _get_cached_service(ChaosScenarioService, db)
    if cached_service:
        return cached_service

    service = ChaosScenarioService(db)
    _cache_service(ChaosScenarioService, db, service)
    return service

def get_chaos_execution_service(db: DatabaseDep):
    """获取混沌执行服务（支持缓存优化）"""
    from app.services.chaos.chaos_execution_service import ChaosExecutionService

    cached_service = _get_cached_service(ChaosExecutionService, db)
    if cached_service:
        return cached_service

    service = ChaosExecutionService(db)
    _cache_service(ChaosExecutionService, db, service)
    return service



# 服务类型注解（API层直接使用）
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from app.services.user.user import UserService
    from app.services.env.env import EnvironmentService
    from app.services.model.model_config import ModelConfigService
    from app.services.chaos.chaosblade_service import ChaosBladeService
    from app.services.chaos.chaos_task_service import ChaosTaskService
    from app.services.chaos.chaos_scenario_service import ChaosScenarioService
    from app.services.chaos.chaos_execution_service import ChaosExecutionService

UserServiceType = Annotated[Any, Depends(get_user_service)]
EnvironmentServiceType = Annotated[Any, Depends(get_environment_service)]
ModelConfigServiceType = Annotated[Any, Depends(get_model_config_service)]
ChaosBladeServiceType = Annotated[Any, Depends(get_chaosblade_service)]
ChaosTaskServiceType = Annotated[Any, Depends(get_chaos_task_service)]
ChaosBatchTaskServiceType = Annotated[Any, Depends(get_chaos_batch_task_service)]
ChaosScenarioServiceType = Annotated[Any, Depends(get_chaos_scenario_service)]
ChaosExecutionServiceType = Annotated[Any, Depends(get_chaos_execution_service)]

