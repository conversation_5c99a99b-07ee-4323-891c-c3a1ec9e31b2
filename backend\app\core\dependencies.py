"""
统一依赖注入系统
提供服务层依赖管理和数据库会话注入
"""
from typing import Annotated, Type, Callable, Any
from fastapi import Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db
from app.schemas.common import PaginationParams, SearchParams


class ServiceDependency:
    """
    服务依赖基类
    所有服务类都应继承此类以获得统一的依赖注入能力
    """

    def __init__(self, db: AsyncSession):
        self.db = db


# 数据库会话依赖注解
DatabaseDep = Annotated[AsyncSession, Depends(get_db)]


# 分页参数依赖注入
def get_pagination_params(
    current: int = Query(1, ge=1, description="当前页码"),
    size: int = Query(20, ge=1, le=100, description="每页条数")
) -> PaginationParams:
    """获取分页参数"""
    return PaginationParams(current=current, size=size)


def get_search_params(
    current: int = Query(1, ge=1, description="当前页码"),
    size: int = Query(20, ge=1, le=100, description="每页条数"),
    keyword: str = Query(None, description="搜索关键词")
) -> SearchParams:
    """获取搜索参数"""
    return SearchParams(current=current, size=size, keyword=keyword)


# 通用服务依赖注入工厂函数
def create_service_dependency(service_class: Type[ServiceDependency]) -> Callable:
    """
    创建服务依赖注入函数的工厂函数
    """
    def get_service(db: DatabaseDep) -> service_class: # type: ignore
        return service_class(db)

    return get_service


# ==================== 具体服务依赖注入 ====================

# 服务依赖注入函数（延迟导入避免循环依赖）
def get_user_service(db: DatabaseDep):
    """获取用户服务"""
    from app.services.user.user import UserService
    return UserService(db)

def get_environment_service(db: DatabaseDep):
    """获取环境服务"""
    from app.services.env.env import EnvironmentService
    return EnvironmentService(db)

def get_model_config_service(db: DatabaseDep):
    """获取模型配置服务"""
    from app.services.model.model_config import ModelConfigService
    return ModelConfigService(db)

def get_chaosblade_service():
    """获取ChaosBlade服务"""
    from app.services.chaos.chaosblade_service import ChaosBladeService
    return ChaosBladeService()

def get_chaos_task_service(db: DatabaseDep):
    """获取混沌任务服务"""
    from app.services.chaos.chaos_task_service import ChaosTaskService
    return ChaosTaskService(db)

def get_chaos_batch_task_service(db: DatabaseDep):
    """获取混沌批次任务服务"""
    from app.services.chaos.chaos_batch_task_service import ChaosBatchTaskService
    return ChaosBatchTaskService(db)

def get_chaos_scenario_service(db: DatabaseDep):
    """获取混沌场景服务"""
    from app.services.chaos.chaos_scenario_service import ChaosScenarioService
    return ChaosScenarioService(db)

def get_chaos_execution_service(db: DatabaseDep):
    """获取混沌执行服务"""
    from app.services.chaos.chaos_execution_service import ChaosExecutionService
    return ChaosExecutionService(db)



# 服务类型注解（API层直接使用）
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from app.services.user.user import UserService
    from app.services.env.env import EnvironmentService
    from app.services.model.model_config import ModelConfigService
    from app.services.chaos.chaosblade_service import ChaosBladeService
    from app.services.chaos.chaos_task_service import ChaosTaskService
    from app.services.chaos.chaos_scenario_service import ChaosScenarioService
    from app.services.chaos.chaos_execution_service import ChaosExecutionService

UserServiceType = Annotated[Any, Depends(get_user_service)]
EnvironmentServiceType = Annotated[Any, Depends(get_environment_service)]
ModelConfigServiceType = Annotated[Any, Depends(get_model_config_service)]
ChaosBladeServiceType = Annotated[Any, Depends(get_chaosblade_service)]
ChaosTaskServiceType = Annotated[Any, Depends(get_chaos_task_service)]
ChaosBatchTaskServiceType = Annotated[Any, Depends(get_chaos_batch_task_service)]
ChaosScenarioServiceType = Annotated[Any, Depends(get_chaos_scenario_service)]
ChaosExecutionServiceType = Annotated[Any, Depends(get_chaos_execution_service)]

