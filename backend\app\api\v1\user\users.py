"""
用户管理API路由
"""
from fastapi import APIRouter, Depends

from app.api.deps import get_current_user, get_current_superuser
from app.core.dependencies import UserServiceType
from app.core.responses import response_builder
from app.schemas.user.user import UserCreate, UserUpdate, UserResponse, UserQuery, UserPageResponse
from app.models.user.user import User

router = APIRouter()


@router.get("/info", response_model=UserResponse, summary="获取当前用户信息")
async def get_user_info(
    service: UserServiceType,
    current_user: User = Depends(get_current_user)
):
    """获取当前用户信息"""
    user_info = await service.get_current_user_info(current_user)
    return response_builder.success(user_info)


@router.get("", response_model=UserPageResponse, summary="查询用户列表")
async def list_users(
    service: UserServiceType,
    query: UserQuery = Depends(), 
    _current_user: User = Depends(get_current_superuser)
):
    """查询用户列表，支持关键词搜索、状态筛选和分页"""
    result = await service.list_users(query)
    return response_builder.success(result)


@router.get("/{user_id}", response_model=UserResponse, summary="获取用户详情")
async def get_user(
    service: UserServiceType,
    user_id: int,
    _current_user: User = Depends(get_current_superuser)
):
    """获取指定ID的用户详情"""
    user = await service.get_by_id(user_id)
    return response_builder.success(user)


@router.post("", response_model=UserResponse, status_code=201, summary="创建用户")
async def create_user(
    service: UserServiceType,
    user_in: UserCreate,
    current_user: User = Depends(get_current_superuser)
):
    """创建新用户，需要管理员权限"""
    user = await service.create(user_in, str(current_user.id))
    return response_builder.created(user)


@router.put("/{user_id}", response_model=UserResponse, summary="更新用户")
async def update_user(
    service: UserServiceType,
    user_id: int,
    user_in: UserUpdate,
    current_user: User = Depends(get_current_superuser)
):
    """更新用户信息，支持修改基本信息和密码"""
    user = await service.update(user_id, user_in, str(current_user.id))
    return response_builder.success(user)


@router.delete("/{user_id}", status_code=204, summary="删除用户")
async def delete_user(
    service: UserServiceType,
    user_id: int,
    current_user: User = Depends(get_current_superuser)
):
    """删除用户，不允许删除超级管理员和自己"""
    await service.delete(user_id, str(current_user.id))
