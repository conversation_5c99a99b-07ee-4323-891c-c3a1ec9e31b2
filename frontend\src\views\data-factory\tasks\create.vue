<template>
  <div class="task-create-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="handleBack" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div class="title-section">
          <h2 class="page-title">创建生成任务</h2>
          <p class="page-description">基于数据模型创建数据生成任务</p>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="handlePreview" :disabled="!canPreview">
          <el-icon><View /></el-icon>
          预览数据
        </el-button>
        <el-button type="primary" :loading="creating" @click="handleCreate">
          <el-icon><Check /></el-icon>
          创建任务
        </el-button>
      </div>
    </div>

    <!-- 步骤指示器 -->
    <div class="steps-section">
      <el-steps :active="currentStep" align-center>
        <el-step title="选择模型" description="选择数据模型" />
        <el-step title="配置任务" description="设置生成参数" />
        <el-step title="确认创建" description="预览并创建任务" />
      </el-steps>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <el-card class="form-card">
        <!-- 步骤1：选择模型 -->
        <div v-show="currentStep === 0" class="step-content">
          <h3 class="step-title">选择数据模型</h3>
          
          <!-- 模型搜索 -->
          <div class="model-search">
            <el-input
              v-model="modelSearch"
              placeholder="搜索模型名称或描述"
              clearable
              style="width: 300px"
              @input="handleModelSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select
              v-model="modelCategoryFilter"
              placeholder="筛选分类"
              clearable
              style="width: 150px; margin-left: 12px"
              @change="handleModelSearch"
            >
              <el-option
                v-for="category in modelCategories"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </div>

          <!-- 模型列表 -->
          <div class="models-section">
            <div v-if="filteredModels.length === 0" class="empty-models">
              <el-empty description="暂无可用模型">
                <el-button type="primary" @click="handleCreateModel">创建数据模型</el-button>
              </el-empty>
            </div>

            <div v-else class="models-grid">
              <div
                v-for="model in filteredModels"
                :key="model.id"
                class="model-card"
                :class="{ 'model-card--selected': selectedModel?.id === model.id }"
                @click="handleSelectModel(model)"
              >
                <div class="model-header">
                  <div class="model-info">
                    <h4 class="model-name">{{ model.name }}</h4>
                    <el-tag size="small" type="info">{{ model.version }}</el-tag>
                  </div>
                  <div class="model-status">
                    <el-tag :type="model.status === '1' ? 'success' : 'danger'" size="small">
                      {{ model.status === '1' ? '启用' : '禁用' }}
                    </el-tag>
                  </div>
                </div>
                
                <p v-if="model.description" class="model-description">
                  {{ model.description }}
                </p>
                
                <div class="model-meta">
                  <div class="meta-item">
                    <el-icon><Grid /></el-icon>
                    <span>{{ model.fields_config?.length || 0 }} 个字段</span>
                  </div>
                  <div class="meta-item">
                    <el-icon><TrendCharts /></el-icon>
                    <span>使用 {{ model.usage_count || 0 }} 次</span>
                  </div>
                  <div v-if="model.category" class="meta-item">
                    <el-icon><Folder /></el-icon>
                    <span>{{ model.category }}</span>
                  </div>
                </div>

                <div class="model-actions">
                  <el-button size="small" @click.stop="handlePreviewModel(model)">
                    <el-icon><View /></el-icon>
                    预览
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤2：配置任务 -->
        <div v-show="currentStep === 1" class="step-content">
          <h3 class="step-title">任务配置</h3>
          
          <el-form
            ref="taskFormRef"
            :model="taskForm"
            :rules="taskRules"
            label-width="120px"
            class="task-form"
          >
            <!-- 基本信息 -->
            <div class="form-section">
              <h4 class="section-title">基本信息</h4>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="任务名称" prop="name" required>
                    <el-input
                      v-model="taskForm.name"
                      placeholder="请输入任务名称"
                      clearable
                      maxlength="100"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="数据模型" prop="model_id">
                    <el-input
                      :value="selectedModel?.name || ''"
                      readonly
                      placeholder="请先选择数据模型"
                    >
                      <template #append>
                        <el-button @click="currentStep = 0">重新选择</el-button>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="任务描述" prop="description">
                <el-input
                  v-model="taskForm.description"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入任务描述（可选）"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </div>

            <!-- 生成配置 -->
            <div class="form-section">
              <h4 class="section-title">生成配置</h4>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="生成数量" prop="record_count" required>
                    <el-input-number
                      v-model="taskForm.record_count"
                      :min="1"
                      :max="1000000"
                      :step="100"
                      style="width: 100%"
                      controls-position="right"
                    />
                    <div class="form-tip">
                      建议单次生成不超过10万条数据
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="导出格式" prop="export_format" required>
                    <el-select
                      v-model="taskForm.export_format"
                      placeholder="选择导出格式"
                      style="width: 100%"
                      @change="handleFormatChange"
                    >
                      <el-option
                        v-for="format in exportFormats"
                        :key="format.format"
                        :label="format.name"
                        :value="format.format"
                      >
                        <div class="format-option">
                          <span class="format-name">{{ format.name }}</span>
                          <span class="format-desc">{{ format.description }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 导出配置 -->
            <div v-if="taskForm.export_format" class="form-section">
              <h4 class="section-title">导出配置</h4>
              
              <!-- JSON格式配置 -->
              <div v-if="taskForm.export_format === 'json'" class="export-config">
                <el-form-item label="格式化输出">
                  <el-switch
                    v-model="taskForm.export_config.pretty"
                    active-text="是"
                    inactive-text="否"
                  />
                  <div class="form-tip">是否格式化JSON输出（美化显示）</div>
                </el-form-item>
              </div>

              <!-- CSV格式配置 -->
              <div v-else-if="taskForm.export_format === 'csv'" class="export-config">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="分隔符">
                      <el-select v-model="taskForm.export_config.delimiter" style="width: 100%">
                        <el-option label="逗号 (,)" value="," />
                        <el-option label="分号 (;)" value=";" />
                        <el-option label="制表符" value="\t" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="包含表头">
                      <el-switch
                        v-model="taskForm.export_config.header"
                        active-text="是"
                        inactive-text="否"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <!-- Excel格式配置 -->
              <div v-else-if="taskForm.export_format === 'excel'" class="export-config">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="工作表名称">
                      <el-input
                        v-model="taskForm.export_config.sheet_name"
                        placeholder="默认：Sheet1"
                        clearable
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="包含表头">
                      <el-switch
                        v-model="taskForm.export_config.header"
                        active-text="是"
                        inactive-text="否"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-form>
        </div>

        <!-- 步骤3：确认创建 -->
        <div v-show="currentStep === 2" class="step-content">
          <h3 class="step-title">确认创建</h3>
          
          <!-- 任务信息预览 -->
          <div class="task-preview">
            <el-descriptions title="任务信息" :column="2" border>
              <el-descriptions-item label="任务名称">{{ taskForm.name }}</el-descriptions-item>
              <el-descriptions-item label="数据模型">{{ selectedModel?.name }}</el-descriptions-item>
              <el-descriptions-item label="生成数量">{{ formatNumber(taskForm.record_count) }} 条</el-descriptions-item>
              <el-descriptions-item label="导出格式">
                <el-tag :type="getFormatType(taskForm.export_format)">
                  {{ taskForm.export_format.toUpperCase() }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="预估文件大小">{{ estimatedFileSize }}</el-descriptions-item>
              <el-descriptions-item label="预估执行时间">{{ estimatedTime }}</el-descriptions-item>
              <el-descriptions-item v-if="taskForm.description" label="任务描述" :span="2">
                {{ taskForm.description }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 模型字段预览 -->
          <div class="fields-preview">
            <h4>字段配置 ({{ selectedModel?.fields_config?.length || 0 }} 个字段)</h4>
            <el-table :data="selectedModel?.fields_config" border style="width: 100%" max-height="300">
              <el-table-column prop="name" label="字段名称" width="150" />
              <el-table-column prop="type" label="数据类型" width="120">
                <template #default="{ row }">
                  <el-tag size="small" type="info">{{ row.type }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="generator" label="生成器" width="120">
                <template #default="{ row }">
                  <el-tag size="small" type="success">{{ row.generator }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="required" label="必填" width="80">
                <template #default="{ row }">
                  <el-tag :type="row.required ? 'success' : 'info'" size="small">
                    {{ row.required ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
            </el-table>
          </div>

          <!-- 数据预览 -->
          <div v-if="previewData.length > 0" class="data-preview">
            <h4>数据预览 (前5条记录)</h4>
            <el-table :data="previewData" border style="width: 100%" max-height="300">
              <el-table-column
                v-for="field in selectedModel?.fields_config"
                :key="field.name"
                :prop="field.name"
                :label="field.name"
                min-width="120"
                show-overflow-tooltip
              />
            </el-table>
          </div>
        </div>

        <!-- 步骤导航 -->
        <div class="step-navigation">
          <el-button v-if="currentStep > 0" @click="handlePrevStep">上一步</el-button>
          <el-button v-if="currentStep < 2" type="primary" @click="handleNextStep">下一步</el-button>
        </div>
      </el-card>
    </div>

    <!-- 模型预览对话框 -->
    <DataPreviewDialog
      v-model:visible="modelPreviewDialog.visible"
      :model-info="modelPreviewDialog.modelInfo"
      :preview-data="modelPreviewDialog.previewData"
      :loading="modelPreviewDialog.loading"
      @refresh="handleRefreshModelPreview"
    />

    <!-- 数据预览对话框 -->
    <DataPreviewDialog
      v-model:visible="dataPreviewDialog.visible"
      :model-info="selectedModel"
      :preview-data="previewData"
      :loading="previewLoading"
      @refresh="handleRefreshDataPreview"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  ArrowLeft,
  View,
  Check,
  Search,
  Grid,
  TrendCharts,
  Folder
} from '@element-plus/icons-vue'
import { DataFactoryService } from '@/api/dataFactoryApi'
import { formatNumber } from '@/utils'
import DataPreviewDialog from '../models/components/DataPreviewDialog.vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const currentStep = ref(0)
const creating = ref(false)
const taskFormRef = ref<FormInstance>()
const models = ref<Api.DataFactory.ModelInfo[]>([])
const modelCategories = ref<string[]>([])
const exportFormats = ref<any[]>([])
const selectedModel = ref<Api.DataFactory.ModelInfo | null>(null)
const modelSearch = ref('')
const modelCategoryFilter = ref('')
const previewData = ref<any[]>([])
const previewLoading = ref(false)

// 表单数据
const taskForm = reactive<Api.DataFactory.TaskForm>({
  name: '',
  description: '',
  model_id: 0,
  record_count: 1000,
  export_format: 'json',
  export_config: {
    pretty: true,
    delimiter: ',',
    header: true,
    sheet_name: 'Sheet1'
  }
})

// 对话框
const modelPreviewDialog = reactive({
  visible: false,
  modelInfo: null as Api.DataFactory.ModelInfo | null,
  previewData: [] as any[],
  loading: false
})

const dataPreviewDialog = reactive({
  visible: false
})

// 表单验证规则
const taskRules: FormRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 1, max: 100, message: '名称长度应在 1 到 100 个字符', trigger: 'blur' }
  ],
  model_id: [
    { required: true, message: '请选择数据模型', trigger: 'change' }
  ],
  record_count: [
    { required: true, message: '请输入生成数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 1000000, message: '生成数量应在 1 到 1000000 之间', trigger: 'blur' }
  ],
  export_format: [
    { required: true, message: '请选择导出格式', trigger: 'change' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const filteredModels = computed(() => {
  let filtered = models.value.filter(model => model.status === '1') // 只显示启用的模型

  if (modelSearch.value) {
    const keyword = modelSearch.value.toLowerCase()
    filtered = filtered.filter(model =>
      model.name.toLowerCase().includes(keyword) ||
      (model.description && model.description.toLowerCase().includes(keyword))
    )
  }

  if (modelCategoryFilter.value) {
    filtered = filtered.filter(model => model.category === modelCategoryFilter.value)
  }

  return filtered
})

const canPreview = computed(() => {
  return selectedModel.value && taskForm.record_count > 0
})

const estimatedFileSize = computed(() => {
  if (!selectedModel.value || !taskForm.record_count) return '-'
  
  // 简单估算：每个字段平均20字节，每条记录额外10字节开销
  const avgFieldSize = 20
  const recordOverhead = 10
  const fieldCount = selectedModel.value.fields_config?.length || 0
  const estimatedBytes = taskForm.record_count * (fieldCount * avgFieldSize + recordOverhead)
  
  return formatFileSize(estimatedBytes)
})

const estimatedTime = computed(() => {
  if (!taskForm.record_count) return '-'
  
  // 简单估算：每秒生成1000条记录
  const recordsPerSecond = 1000
  const seconds = Math.ceil(taskForm.record_count / recordsPerSecond)
  
  if (seconds < 60) {
    return `约 ${seconds} 秒`
  } else if (seconds < 3600) {
    return `约 ${Math.ceil(seconds / 60)} 分钟`
  } else {
    return `约 ${Math.ceil(seconds / 3600)} 小时`
  }
})

// 监听器
watch(
  () => selectedModel.value,
  (newModel) => {
    if (newModel) {
      taskForm.model_id = newModel.id
      if (!taskForm.name) {
        taskForm.name = `${newModel.name}_生成任务_${new Date().getTime()}`
      }
    }
  }
)

// 页面初始化
onMounted(() => {
  loadModels()
  loadCategories()
  loadExportFormats()
  
  // 检查URL参数中是否有模型ID
  const modelId = route.query.modelId
  if (modelId) {
    const id = Number(modelId)
    setTimeout(() => {
      const model = models.value.find(m => m.id === id)
      if (model) {
        handleSelectModel(model)
      }
    }, 500)
  }
})

// 加载数据
const loadModels = async () => {
  try {
    const response = await DataFactoryService.getDataModels({ skip: 0, limit: 1000 })
    if (response.success) {
      models.value = response.data.records
    }
  } catch (error) {
    ElMessage.error('加载模型列表失败')
  }
}

const loadCategories = async () => {
  try {
    const response = await DataFactoryService.getModelCategories()
    if (response.success) {
      modelCategories.value = response.data
    }
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const loadExportFormats = async () => {
  try {
    const response = await DataFactoryService.getExportFormats()
    if (response.success) {
      exportFormats.value = response.data
    } else {
      // 默认格式
      exportFormats.value = [
        { format: 'json', name: 'JSON', description: 'JavaScript对象表示法' },
        { format: 'csv', name: 'CSV', description: '逗号分隔值文件' },
        { format: 'excel', name: 'Excel', description: 'Microsoft Excel文件' }
      ]
    }
  } catch (error) {
    // 使用默认格式
    exportFormats.value = [
      { format: 'json', name: 'JSON', description: 'JavaScript对象表示法' },
      { format: 'csv', name: 'CSV', description: '逗号分隔值文件' },
      { format: 'excel', name: 'Excel', description: 'Microsoft Excel文件' }
    ]
  }
}

// 事件处理
const handleBack = () => {
  router.push('/data-factory/tasks')
}

const handleModelSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const handleSelectModel = (model: Api.DataFactory.ModelInfo) => {
  selectedModel.value = model
}

const handlePreviewModel = async (model: Api.DataFactory.ModelInfo) => {
  modelPreviewDialog.modelInfo = model
  modelPreviewDialog.visible = true
  await handleRefreshModelPreview()
}

const handleRefreshModelPreview = async () => {
  if (!modelPreviewDialog.modelInfo) return
  
  try {
    modelPreviewDialog.loading = true
    const response = await DataFactoryService.previewDataModel(modelPreviewDialog.modelInfo.id, 5)
    if (response.success) {
      modelPreviewDialog.previewData = response.data.preview_data
    }
  } catch (error) {
    ElMessage.error('预览数据失败')
  } finally {
    modelPreviewDialog.loading = false
  }
}

const handleCreateModel = () => {
  router.push('/data-factory/models/create')
}

const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const handleNextStep = async () => {
  if (currentStep.value === 0) {
    // 验证模型选择
    if (!selectedModel.value) {
      ElMessage.error('请选择数据模型')
      return
    }
    currentStep.value++
  } else if (currentStep.value === 1) {
    // 验证任务配置
    if (!taskFormRef.value) return
    try {
      await taskFormRef.value.validate()
      // 生成预览数据
      await generatePreviewData()
      currentStep.value++
    } catch (error) {
      ElMessage.error('请检查任务配置')
    }
  }
}

const handleFormatChange = () => {
  // 重置导出配置
  taskForm.export_config = {
    pretty: true,
    delimiter: ',',
    header: true,
    sheet_name: 'Sheet1'
  }
}

const handlePreview = () => {
  if (!canPreview.value) return
  
  dataPreviewDialog.visible = true
  generatePreviewData()
}

const handleRefreshDataPreview = () => {
  generatePreviewData()
}

const generatePreviewData = async () => {
  if (!selectedModel.value) return
  
  try {
    previewLoading.value = true
    const response = await DataFactoryService.previewDataModel(selectedModel.value.id, 5)
    if (response.success) {
      previewData.value = response.data.preview_data
    }
  } catch (error) {
    ElMessage.error('生成预览数据失败')
  } finally {
    previewLoading.value = false
  }
}

const handleCreate = async () => {
  if (!selectedModel.value) {
    ElMessage.error('请选择数据模型')
    return
  }

  try {
    // 最终验证
    if (!taskFormRef.value) return
    await taskFormRef.value.validate()
    
    creating.value = true
    
    await DataFactoryService.createGenerationTask(taskForm)
    ElMessage.success('任务创建成功')
    router.push('/data-factory/tasks')
  } catch (error) {
    ElMessage.error('创建失败，请检查输入信息')
  } finally {
    creating.value = false
  }
}

// 工具函数
const getFormatType = (format: string) => {
  const types: Record<string, string> = {
    json: 'primary',
    csv: 'success',
    excel: 'warning'
  }
  return types[format] || 'info'
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped lang="scss">
.task-create-page {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .back-button {
        padding: 8px 12px;
      }

      .title-section {
        .page-title {
          margin: 0 0 4px 0;
          font-size: 24px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .page-description {
          margin: 0;
          color: var(--el-text-color-regular);
          font-size: 14px;
        }
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .steps-section {
    margin-bottom: 30px;
  }

  .form-content {
    .form-card {
      min-height: 600px;

      .step-content {
        .step-title {
          margin: 0 0 20px 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .model-search {
          margin-bottom: 20px;
          display: flex;
          align-items: center;
        }

        .models-section {
          .empty-models {
            text-align: center;
            padding: 40px 0;
          }

          .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;

            .model-card {
              border: 2px solid var(--el-border-color-light);
              border-radius: 8px;
              padding: 20px;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                border-color: var(--el-color-primary);
                box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
              }

              &--selected {
                border-color: var(--el-color-primary);
                background: var(--el-fill-color-extra-light);
              }

              .model-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 12px;

                .model-info {
                  display: flex;
                  align-items: center;
                  gap: 8px;

                  .model-name {
                    margin: 0;
                    font-size: 16px;
                    font-weight: 600;
                    color: var(--el-text-color-primary);
                  }
                }
              }

              .model-description {
                margin: 0 0 16px 0;
                color: var(--el-text-color-regular);
                font-size: 14px;
                line-height: 1.5;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }

              .model-meta {
                display: flex;
                flex-wrap: wrap;
                gap: 12px;
                margin-bottom: 16px;

                .meta-item {
                  display: flex;
                  align-items: center;
                  gap: 4px;
                  font-size: 13px;
                  color: var(--el-text-color-regular);

                  .el-icon {
                    font-size: 14px;
                  }
                }
              }

              .model-actions {
                display: flex;
                justify-content: flex-end;
              }
            }
          }
        }

        .task-form {
          max-width: 800px;

          .form-section {
            margin-bottom: 30px;

            .section-title {
              margin: 0 0 16px 0;
              font-size: 16px;
              font-weight: 600;
              color: var(--el-text-color-primary);
              border-bottom: 1px solid var(--el-border-color-lighter);
              padding-bottom: 8px;
            }

            .form-tip {
              font-size: 12px;
              color: var(--el-text-color-placeholder);
              margin-top: 4px;
              line-height: 1.4;
            }

            .export-config {
              background: var(--el-fill-color-extra-light);
              padding: 16px;
              border-radius: 6px;
            }

            .format-option {
              display: flex;
              flex-direction: column;

              .format-name {
                font-weight: 500;
              }

              .format-desc {
                font-size: 12px;
                color: var(--el-text-color-placeholder);
              }
            }
          }
        }

        .task-preview {
          margin-bottom: 30px;
        }

        .fields-preview,
        .data-preview {
          margin-bottom: 30px;

          h4 {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
          }
        }
      }

      .step-navigation {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid var(--el-border-color-light);
        display: flex;
        justify-content: center;
        gap: 12px;
      }
    }
  }
}
</style>
