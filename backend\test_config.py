#!/usr/bin/env python3
"""
配置测试脚本
用于验证应用配置是否正确加载
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config():
    """测试配置加载"""
    try:
        print("🔧 测试配置加载...")
        
        # 测试基础配置
        from app.core.config import settings
        print(f"✅ 配置加载成功")
        print(f"   - 应用名称: {settings.app_name}")
        print(f"   - 调试模式: {settings.debug}")
        print(f"   - 数据库URL: {settings.DATABASE_URL[:50]}...")
        print(f"   - JWT密钥: {settings.SECRET_KEY[:20]}...")
        
        # 测试数据库引擎
        print("\n🗄️ 测试数据库引擎...")
        from app.core.database import async_engine
        print(f"✅ 数据库引擎创建成功")
        
        # 测试时区工具
        print("\n⏰ 测试时区工具...")
        from app.utils.timezone import now
        current_time = now()
        print(f"✅ 时区工具正常")
        print(f"   - 当前时间: {current_time}")
        
        print("\n🎉 配置测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_imports():
    """测试关键模块导入"""
    try:
        print("\n📦 测试关键模块导入...")
        
        # 测试数据库连接
        from app.database import get_db
        print("✅ 数据库连接模块导入成功")
        
        # 测试模型层
        from app.models.user.user import User
        print("✅ 用户模型模块导入成功")
        
        print("✅ 关键模块导入成功！")
        return True
        
    except Exception as e:
        print(f"❌ 模块导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 DpTestPlatform 配置测试")
    print("=" * 50)
    
    # 运行测试
    config_ok = test_config()
    imports_ok = test_imports()
    
    print("\n" + "=" * 50)
    if config_ok and imports_ok:
        print("🎉 所有测试通过！应用可以正常启动。")
        print("💡 启动命令: python run.py")
        sys.exit(0)
    else:
        print("❌ 测试失败！请检查配置和依赖。")
        sys.exit(1)
