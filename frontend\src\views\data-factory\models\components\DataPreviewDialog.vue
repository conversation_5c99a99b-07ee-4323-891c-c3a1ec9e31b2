<template>
  <el-dialog
    v-model="dialogVisible"
    title="数据预览"
    width="80%"
    :before-close="handleClose"
    class="data-preview-dialog"
  >
    <div v-if="modelInfo" class="dialog-content">
      <!-- 模型信息 -->
      <div class="model-info">
        <div class="info-header">
          <h3>{{ modelInfo.name }}</h3>
          <el-tag type="info" size="small">{{ modelInfo.version }}</el-tag>
        </div>
        <p v-if="modelInfo.description" class="model-description">
          {{ modelInfo.description }}
        </p>
        <div class="model-stats">
          <el-tag size="small">字段数: {{ modelInfo.fields_config?.length || 0 }}</el-tag>
          <el-tag size="small" type="success">使用次数: {{ modelInfo.usage_count || 0 }}</el-tag>
        </div>
      </div>

      <!-- 视图切换 -->
      <div class="view-tabs">
        <el-radio-group v-model="currentView" size="small">
          <el-radio-button label="table">表格视图</el-radio-button>
          <el-radio-button label="json">JSON视图</el-radio-button>
          <el-radio-button label="fields">字段配置</el-radio-button>
        </el-radio-group>
        <div class="view-actions">
          <el-button size="small" @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button size="small" type="primary" @click="handleExportSample">
            <el-icon><Download /></el-icon>
            导出样例
          </el-button>
        </div>
      </div>

      <!-- 表格视图 -->
      <div v-if="currentView === 'table'" class="table-view">
        <el-table
          v-loading="loading"
          :data="previewData"
          stripe
          border
          style="width: 100%"
          max-height="400"
        >
          <el-table-column
            v-for="field in modelInfo.fields_config"
            :key="field.name"
            :prop="field.name"
            :label="field.name"
            :min-width="120"
            show-overflow-tooltip
          >
            <template #header>
              <div class="field-header">
                <span class="field-name">{{ field.name }}</span>
                <el-tag size="small" type="info">{{ field.type }}</el-tag>
              </div>
            </template>
            <template #default="{ row }">
              <div class="field-value">
                <span v-if="field.type === 'boolean'">
                  <el-tag :type="row[field.name] ? 'success' : 'danger'" size="small">
                    {{ row[field.name] ? '是' : '否' }}
                  </el-tag>
                </span>
                <span v-else-if="field.type === 'date' || field.type === 'datetime'">
                  {{ formatFieldValue(row[field.name], field.type) }}
                </span>
                <span v-else>{{ row[field.name] }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- JSON视图 -->
      <div v-if="currentView === 'json'" class="json-view">
        <div class="json-header">
          <span class="json-title">预览数据 ({{ previewData.length }} 条记录)</span>
          <el-button size="small" @click="handleCopyJson">
            <el-icon><CopyDocument /></el-icon>
            复制JSON
          </el-button>
        </div>
        <el-scrollbar height="400px">
          <pre class="json-content">{{ formatJsonData }}</pre>
        </el-scrollbar>
      </div>

      <!-- 字段配置视图 -->
      <div v-if="currentView === 'fields'" class="fields-view">
        <el-table
          :data="modelInfo.fields_config"
          stripe
          border
          style="width: 100%"
          max-height="400"
        >
          <el-table-column prop="name" label="字段名称" width="150" />
          <el-table-column prop="type" label="数据类型" width="120">
            <template #default="{ row }">
              <el-tag size="small" type="info">{{ row.type }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="generator" label="生成器" width="120">
            <template #default="{ row }">
              <el-tag size="small" type="success">{{ row.generator }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="required" label="必填" width="80">
            <template #default="{ row }">
              <el-tag :type="row.required ? 'success' : 'info'" size="small">
                {{ row.required ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="options" label="生成器选项" min-width="200">
            <template #default="{ row }">
              <div v-if="row.options && Object.keys(row.options).length > 0" class="options-content">
                <el-tag
                  v-for="(value, key) in row.options"
                  :key="key"
                  size="small"
                  class="option-tag"
                >
                  {{ key }}: {{ value }}
                </el-tag>
              </div>
              <span v-else class="text-gray">无</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 数据质量统计 -->
      <div class="quality-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-label">记录数量</div>
              <div class="stat-value">{{ previewData.length }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-label">字段数量</div>
              <div class="stat-value">{{ modelInfo.fields_config?.length || 0 }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-label">空值率</div>
              <div class="stat-value">{{ calculateNullRate() }}%</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-label">数据完整性</div>
              <div class="stat-value">{{ calculateCompleteness() }}%</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleCreateTask">
          基于此模型创建任务
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh, Download, CopyDocument } from '@element-plus/icons-vue'

interface Props {
  visible: boolean
  modelInfo: Api.DataFactory.ModelInfo | null
  previewData: any[]
  loading: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const router = useRouter()

// 响应式数据
const dialogVisible = ref(false)
const currentView = ref<'table' | 'json' | 'fields'>('table')

// 计算属性
const formatJsonData = computed(() => {
  return JSON.stringify(props.previewData, null, 2)
})

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
  },
  { immediate: true }
)

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const handleRefresh = () => {
  emit('refresh')
}

const handleExportSample = () => {
  if (!props.previewData.length) {
    ElMessage.warning('暂无数据可导出')
    return
  }

  // 创建并下载JSON文件
  const jsonStr = JSON.stringify(props.previewData, null, 2)
  const blob = new Blob([jsonStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${props.modelInfo?.name || 'sample'}_preview.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  
  ElMessage.success('样例数据导出成功')
}

const handleCopyJson = async () => {
  try {
    await navigator.clipboard.writeText(formatJsonData.value)
    ElMessage.success('JSON数据已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const handleCreateTask = () => {
  if (!props.modelInfo) return
  
  router.push({
    path: '/data-factory/tasks/create',
    query: { modelId: props.modelInfo.id }
  })
  handleClose()
}

const formatFieldValue = (value: any, type: string) => {
  if (value === null || value === undefined) return '-'
  
  if (type === 'date') {
    return new Date(value).toLocaleDateString()
  } else if (type === 'datetime') {
    return new Date(value).toLocaleString()
  }
  
  return value
}

const calculateNullRate = () => {
  if (!props.previewData.length || !props.modelInfo?.fields_config) return 0
  
  const totalFields = props.previewData.length * props.modelInfo.fields_config.length
  let nullCount = 0
  
  props.previewData.forEach(record => {
    props.modelInfo!.fields_config.forEach(field => {
      if (record[field.name] === null || record[field.name] === undefined || record[field.name] === '') {
        nullCount++
      }
    })
  })
  
  return Math.round((nullCount / totalFields) * 100)
}

const calculateCompleteness = () => {
  return 100 - calculateNullRate()
}
</script>

<style scoped lang="scss">
.data-preview-dialog {
  .dialog-content {
    .model-info {
      background: var(--el-bg-color-page);
      padding: 16px;
      border-radius: 8px;
      margin-bottom: 20px;

      .info-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
        }
      }

      .model-description {
        margin: 8px 0;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }

      .model-stats {
        display: flex;
        gap: 8px;
      }
    }

    .view-tabs {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .view-actions {
        display: flex;
        gap: 8px;
      }
    }

    .table-view {
      .field-header {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .field-name {
          font-weight: 500;
        }
      }

      .field-value {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
      }
    }

    .json-view {
      .json-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .json-title {
          font-weight: 500;
          color: var(--el-text-color-primary);
        }
      }

      .json-content {
        background: var(--el-bg-color-page);
        padding: 16px;
        border-radius: 6px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.5;
        color: var(--el-text-color-primary);
        margin: 0;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }

    .fields-view {
      .options-content {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .option-tag {
          font-size: 12px;
        }
      }

      .text-gray {
        color: var(--el-text-color-placeholder);
      }
    }

    .quality-stats {
      background: var(--el-bg-color-page);
      padding: 16px;
      border-radius: 8px;
      margin-top: 20px;

      .stat-item {
        text-align: center;

        .stat-label {
          font-size: 14px;
          color: var(--el-text-color-regular);
          margin-bottom: 4px;
        }

        .stat-value {
          font-size: 20px;
          font-weight: 600;
          color: var(--el-color-primary);
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>
