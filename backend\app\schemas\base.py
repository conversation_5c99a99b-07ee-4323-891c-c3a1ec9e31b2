"""
基础 Pydantic 模式
"""
from typing import Any, Generic, List, Optional, TypeVar
from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field

T = TypeVar('T')


class BaseSchema(BaseModel):
    """
    基础模式类
    """
    model_config = ConfigDict(
        from_attributes=True,
        protected_namespaces=()  # 禁用保护命名空间，允许使用 model_ 前缀的字段
        )



class BaseCreateSchema(BaseSchema):
    """
    创建模式基类
    """
    created_by: Optional[str] = Field(default=None, description="创建人")


class BaseUpdateSchema(BaseSchema):
    """
    更新模式基类
    """
    updated_by: Optional[str] = Field(default=None, description="更新人")


class BaseResponseSchema(BaseSchema):
    """
    响应模式基类
    """
    id: int = Field(description="主键ID")
    created_at: Optional[datetime] = Field(default=None, description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")
    created_by: Optional[str] = Field(default=None, description="创建人")
    updated_by: Optional[str] = Field(default=None, description="更新人")


class ErrorResponse(BaseModel):
    """
    统一错误响应格式
    """
    message: str = Field(description="错误消息")
    error_code: int = Field(description="业务错误码")
    http_code: int = Field(description="HTTP状态码")
    details: Optional[Any] = Field(default=None, description="错误详情")
    timestamp: str = Field(description="错误时间戳")
    request_id: Optional[str] = Field(default=None, description="请求ID")


class PaginationData(BaseModel, Generic[T]):
    """
    分页数据结构
    """
    records: List[T] = Field(description="数据列表")
    total: int = Field(description="总数")
    current: int = Field(description="当前页")
    size: int = Field(description="页大小")


class PaginationResponse(BaseModel, Generic[T]):
    """
    分页响应格式 - 直接返回分页数据
    """
    records: List[T] = Field(description="数据列表")
    total: int = Field(description="总数")
    current: int = Field(description="当前页")
    size: int = Field(description="页大小")


# ==================== 新增通用分页模型 ====================

class BaseQuery(BaseModel):
    """通用查询参数基类"""
    # 分页参数
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页大小")

    # 搜索参数
    keyword: Optional[str] = Field(None, description="搜索关键词")

    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.size


class BasePageResponse(BaseModel, Generic[T]):
    """通用分页响应模型"""
    items: List[T] = Field(..., description="当前页数据列表")
    total: int = Field(..., description="总条数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页条数")
    pages: int = Field(..., description="总页数")