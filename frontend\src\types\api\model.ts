/**
 * 模型管理相关类型定义
 * 统一管理模型模块的所有类型
 */

// ==================== 基础类型 ====================

/** 模型平台类型枚举 */
export type ModelPlatform =
  // 本地模型
  | 'local'       // 本地模型
  // 国内平台
  | 'doubao'      // 豆包 - 字节跳动
  | 'deepseek'    // DeepSeek
  | 'qwen'        // 通义千问 - 阿里巴巴
  | 'ernie'       // 文心一言 - 百度
  | 'chatglm'     // 智谱AI


/** 模型状态枚举 */
export type ModelStatus = 'enabled' | 'disabled'

/** 模型健康状态枚举 */
export type ModelHealthStatus = 'unknown' | 'healthy' | 'unhealthy'

/** 模型基础信息 */
export interface ModelConfigBase {
  /** 模型名称 */
  name: string
  /** 所属平台 */
  platform: ModelPlatform
  /** 模型描述 */
  description?: string
  /** API地址 */
  api_url: string
  /** API Key */
  api_key?: string
  /** 请求超时时间（秒） */
  timeout_seconds?: number
  /** 实际模型名称 */
  model_name: string
  /** 最大令牌数 */
  max_tokens?: number
  /** 系统提示词 */
  prompt?: string
}

// ==================== 请求类型 ====================

/** 创建模型配置请求 */
export interface ModelConfigCreateRequest extends ModelConfigBase {
  // 创建时的特定字段可以在这里添加
}

/** 更新模型配置请求 */
export interface ModelConfigUpdateRequest extends Partial<ModelConfigBase> {
  /** 模型状态 */
  status?: ModelStatus
}

/** 模型列表查询参数 */
export interface ModelConfigListParams {
  /** 当前页码 */
  current?: number
  /** 每页条数 */
  size?: number
  /** 搜索关键词 */
  name?: string
  /** 平台类型 */
  platform?: ModelPlatform
  /** 状态 */
  status?: ModelStatus
  /** 健康状态 */
  health_status?: ModelHealthStatus
}

// ==================== 响应类型 ====================

/** 模型配置响应 */
export interface ModelConfigResponse extends ModelConfigBase {
  /** 模型ID */
  id: number
  /** 状态 */
  status: ModelStatus
  /** 健康状态 */
  health_status: ModelHealthStatus
  /** 最后健康检查时间 */
  last_health_check?: string
  /** API Key（已脱敏） */
  api_key: string
  /** 创建时间 */
  created_at: string
  /** 更新时间 */
  updated_at: string
  /** 创建者 */
  created_by?: string
  /** 更新者 */
  updated_by?: string
}

/** 模型列表响应 - 与后端ModelConfigPageResponse匹配 */
export interface ModelConfigListData {
  /** 当前页模型配置列表 */
  items: ModelConfigResponse[]
  /** 总条数 */
  total: number
  /** 当前页码 */
  page: number
  /** 每页条数 */
  size: number
  /** 总页数 */
  pages: number
}

/** 模型列表响应（兼容旧格式） */
export interface ModelConfigListResponse {
  /** 模型列表 */
  records: ModelConfigResponse[]
  /** 总数 */
  total: number
  /** 当前页 */
  current: number
  /** 每页条数 */
  size: number
}

// ==================== 健康检查相关 ====================

/** 健康检查请求 */
export interface ModelHealthCheckRequest {
  /** 模型ID（可选，为空时检查所有启用的模型） */
  model_id?: number
  /** 检查超时时间（秒） */
  timeout_seconds?: number
}

/** 健康检查响应 */
export interface ModelHealthCheckResponse {
  /** 模型ID */
  model_id: number
  /** 模型名称 */
  model_name: string
  /** 是否健康 */
  is_healthy: boolean
  /** 响应时间（毫秒） */
  response_time_ms?: number
  /** 错误信息 */
  error_message?: string
  /** 检查时间 */
  check_time: string
}

// ==================== 模型调用相关 ====================

/** 模型调用请求 */
export interface ModelCallRequest {
  /** 模型名称 */
  model_name: string
  /** 提示词 */
  prompt: string
  /** 调用参数 */
  parameters?: Record<string, any>
}

/** 模型调用响应 */
export interface ModelCallResponse {
  /** 模型名称 */
  model_name: string
  /** 模型响应（原始格式） */
  response: any
  /** 响应时间（毫秒） */
  response_time_ms: number
  /** 是否成功 */
  success: boolean
  /** 错误信息 */
  error_message?: string
}

// ==================== 统计信息 ====================

/** 模型统计信息 */
export interface ModelStatsResponse {
  /** 总模型数 */
  total_models: number
  /** 启用模型数 */
  total_enabled: number
  /** 健康模型数 */
  total_healthy: number
  /** 平台统计 */
  platform_stats: PlatformStats[]
}

/** 平台统计信息 */
export interface PlatformStats {
  /** 平台名称 */
  platform: string
  /** 总数 */
  total: number
  /** 启用数 */
  enabled: number
  /** 健康数 */
  healthy: number
}

// ==================== 表单类型 ====================

/** 模型表单数据 */
export interface ModelConfigFormData extends ModelConfigBase {
  /** 模型ID（编辑时） */
  id?: number
}

// ==================== 类型守卫 ====================

/** 检查是否为有效的模型平台 */
export function isValidModelPlatform(platform: string): platform is ModelPlatform {
  const validPlatforms: ModelPlatform[] = [
    // 本地模型
    'local',
    // 国内平台
    'doubao', 'deepseek', 'qwen', 'ernie', 'chatglm'
  ]
  return validPlatforms.includes(platform as ModelPlatform)
}

/** 检查是否为有效的模型状态 */
export function isValidModelStatus(status: string): status is ModelStatus {
  const validStatuses: ModelStatus[] = ['enabled', 'disabled']
  return validStatuses.includes(status as ModelStatus)
}

/** 检查是否为有效的健康状态 */
export function isValidModelHealthStatus(status: string): status is ModelHealthStatus {
  const validStatuses: ModelHealthStatus[] = ['unknown', 'healthy', 'unhealthy']
  return validStatuses.includes(status as ModelHealthStatus)
}

// ==================== 默认值 ====================

/** 默认模型表单数据 */
export const defaultModelConfigFormData: ModelConfigFormData = {
  name: '',
  platform: 'local',
  description: '',
  api_url: 'https://',
  api_key: '',
  timeout_seconds: 60,
  model_name: '',
  max_tokens: 2048,
  prompt: ''
}

/** 默认查询参数 */
export const defaultModelConfigListParams: ModelConfigListParams = {
  current: 1,
  size: 20,
  name: '',
  platform: undefined,
  status: undefined,
  health_status: undefined
}

// ==================== 常量配置 ====================

/** 模型平台配置 */
export const MODEL_PLATFORM_CONFIG = {
  // 本地模型
  local: { type: 'info' as const, text: '本地模型', icon: 'local' },
  // 国内平台
  doubao: { type: 'primary' as const, text: '豆包', icon: 'doubao' },
  deepseek: { type: 'success' as const, text: 'DeepSeek', icon: 'deepseek' },
  qwen: { type: 'warning' as const, text: '通义千问', icon: 'qwen' },
  ernie: { type: 'danger' as const, text: '文心一言', icon: 'ernie' },
  chatglm: { type: 'info' as const, text: '智谱AI', icon: 'chatglm' },
} as const

/** 模型状态配置 */
export const MODEL_STATUS_CONFIG = {
  enabled: { type: 'success' as const, text: '启用' },
  disabled: { type: 'info' as const, text: '停用' }
} as const

/** 模型健康状态配置 */
export const MODEL_HEALTH_STATUS_CONFIG = {
  healthy: { type: 'success' as const, text: '健康' },
  unhealthy: { type: 'danger' as const, text: '不健康' },
  unknown: { type: 'info' as const, text: '未知' }
} as const
