"""
统一路由管理器
"""
from fastapi import APIRouter
from app.api.v1.user import auth, users, role, upload
from app.api.v1.env import env
from app.api.v1.model import model_config
from app.api.v1.chaos import chaos_task, chaos_batch_task, chaos_scenario, chaos_execution, chaosblade
from app.api.v1.data_factory import data_models, tasks
class RouterManager:
    """
    路由管理器
    """
    
    def __init__(self):
        self.api_router = APIRouter()
        self._register_routes()
    
    def _register_routes(self):
        """
        注册所有路由
        """
        # API v1 路由
        api_v1_router = APIRouter(prefix="/api")
        
        # 认证相关路由
        api_v1_router.include_router(auth.router,prefix="/auth",tags=["认证管理"])
        
        # 用户管理路由
        api_v1_router.include_router(users.router,prefix="/user",tags=["用户管理"])        
        
        # 角色管理路由
        api_v1_router.include_router(role.router,prefix="/role",tags=["角色管理"])
        
        # 文件上传路由
        api_v1_router.include_router(upload.router,prefix="/upload",tags=["文件上传"])
        
        # 环境管理路由
        api_v1_router.include_router(env.router,prefix="/env",tags=["环境管理"])

        # 模型管理路由
        api_v1_router.include_router(model_config.router,prefix="/model",tags=["模型管理"])

        # 混沌测试路由
        api_v1_router.include_router(chaos_task.router,prefix="/chaos/tasks",tags=["混沌测试-任务管理"])
        api_v1_router.include_router(chaos_batch_task.router,prefix="/chaos/batch-tasks",tags=["混沌测试-批次任务"])
        api_v1_router.include_router(chaos_scenario.router,prefix="/chaos/scenarios",tags=["混沌测试-故障模板"])
        api_v1_router.include_router(chaos_execution.router,prefix="/chaos/executions",tags=["混沌测试-执行记录"])
        api_v1_router.include_router(chaosblade.router,prefix="/chaos/blade",tags=["混沌测试-ChaosBlade管理"])

        # 数据工厂路由
        api_v1_router.include_router(data_models.router,prefix="/data-factory/models",tags=["数据工厂-模型管理"])
        api_v1_router.include_router(tasks.router,prefix="/data-factory/tasks",tags=["数据工厂-任务管理"])

        # 注册到主路由
        self.api_router.include_router(api_v1_router)
    
    def get_router(self) -> APIRouter:
        """
        获取路由器
        """
        return self.api_router


# 创建路由管理器实例
router_manager = RouterManager() 