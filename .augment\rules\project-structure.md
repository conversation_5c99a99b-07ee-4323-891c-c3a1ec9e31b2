---
type: "always_apply"
---

# 项目结构规范

## 前端项目结构：frontend
```
frontend/
├── src/
│   ├── api/                  # API 接口定义
│   │   ├── usersApi.ts       # 用户相关接口
│   │   ├── envApi.ts         # 环境相关接口
│   │   └── modules/          # API 模块化目录
│   ├── assets/               # 静态资源
│   │   ├── styles/          # 样式文件
│   │   │   ├── variables.scss    # CSS 变量定义
│   │   │   ├── el-light.scss     # Element Plus 亮色主题
│   │   │   ├── el-dark.scss      # Element Plus 暗色主题
│   │   │   ├── dark.scss         # 系统暗色主题
│   │   │   └── theme-animation.scss # 主题切换动画
│   │   └── icons/           # 图标资源
│   ├── components/          # 组件库
│   │   ├── core/            # 核心组件
│   │   │   ├── forms/       # 表单组件
│   │   │   │   ├── art-search-bar/    # 搜索栏组件
│   │   │   │   ├── art-table/         # 表格组件
│   │   │   │   └── art-table-header/  # 表格头部组件
│   │   │   └── base/        # 基础组件
│   │   └── business/        # 业务组件
│   ├── composables/         # 组合式函数
│   │   ├── useTable.ts      # 表格状态管理
│   │   ├── useTheme.ts      # 主题管理
│   │   └── useLoading.ts    # 加载状态管理
│   ├── router/              # 路由配置
│   │   ├── index.ts         # 路由主文件
│   │   ├── routes/          # 路由定义
│   │   │   ├── staticRoutes.ts  # 静态路由
│   │   │   └── asyncRoutes.ts   # 动态路由
│   │   └── guards/          # 路由守卫
│   ├── store/               # 状态管理
│   │   ├── core/            # 核心状态
│   │   │   ├── user.ts      # 用户状态
│   │   │   └── setting.ts   # 设置状态
│   │   └── business/        # 业务状态
│   │       ├── environment/ # 环境管理状态
│   │       └── chaos/       # 混沌测试状态
│   ├── types/               # 类型定义
│   │   ├── api/             # API 类型
│   │   ├── component/       # 组件类型
│   │   └── store/           # 状态类型
│   ├── utils/               # 工具函数
│   │   ├── http/            # HTTP 请求工具
│   │   ├── theme/           # 主题工具
│   │   └── ui/              # UI 工具
│   └── views/               # 页面组件
│       ├── system/          # 系统管理
│       │   └── user/        # 用户管理
│       │       ├── index.vue           # 主页面
│       │       └── modules/            # 子组件
│       │           ├── user-search.vue # 搜索组件
│       │           └── user-dialog.vue # 对话框组件
│       └── config/          # 配置管理
│           └── environment/ # 环境管理
│               ├── index.vue           # 主页面
│               └── modules/            # 子组件
│                   ├── environment-search.vue # 搜索组件
│                   └── environment-dialog.vue # 对话框组件
├── public/                  # 公共静态资源
├── index.html              # HTML 模板
├── vite.config.ts          # Vite 配置
└── package.json            # 项目依赖
```

## 后端项目结构
```
backend/
├── app/                     # 应用核心代码
│   ├── main.py             # 应用主入口，生命周期管理
│   ├── api/                # API路由层
│   │   ├── deps.py         # 依赖注入定义
│   │   ├── router.py       # 路由管理器
│   │   └── v1/             # API版本1
│   │       ├── user/       # 用户相关API
│   │       └── env/        # 环境相关API
│   ├── core/               # 核心功能模块
│   │   ├── config.py       # 配置管理
│   │   ├── database.py     # 数据库连接
│   │   ├── security.py     # 安全工具
│   │   ├── exceptions.py   # 异常定义
│   │   └── responses.py    # 统一响应处理
│   ├── database/           # 数据库模块
│   │   ├── base.py         # 数据库基础配置
│   │   ├── connection.py   # 连接管理
│   │   └── session.py      # 会话管理
│   ├── models/             # 数据模型层
│   │   ├── base.py         # 基础模型类
│   │   ├── user/           # 用户相关模型
│   │   └── env/            # 环境相关模型
│   ├── repositories/       # 数据访问层
│   │   ├── base.py         # 基础仓库类
│   │   ├── user/           # 用户相关仓库
│   │   └── env/            # 环境相关仓库
│   ├── schemas/            # 数据传输对象
│   │   ├── base.py         # 基础Schema和响应格式
│   │   ├── common.py       # 通用Schema
│   │   ├── user/           # 用户相关Schema
│   │   └── env/            # 环境相关Schema
│   ├── services/           # 业务逻辑层
│   │   ├── user/           # 用户相关服务
│   │   └── env/            # 环境相关服务
│   ├── middleware/         # 中间件
│   │   ├── performance.py  # 性能监控中间件
│   │   └── request_logging.py # 请求日志中间件
│   └── utils/              # 工具函数
│       ├── clients/        # 外部客户端
│       ├── converters.py   # 数据转换工具
│       ├── file_handler.py # 文件处理工具
│       ├── logger.py       # 日志配置
│       └── validators.py   # 验证工具
├── alembic/                # 数据库迁移
├── tests/                  # 测试文件
├── uploads/                # 文件上传目录
├── .env                    # 环境变量配置
├── requirements.txt        # Python依赖
└── run.py                  # 生产环境启动脚本
```