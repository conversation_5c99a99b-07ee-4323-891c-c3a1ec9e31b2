
# DpTestPlatform 后端环境变量配置

# 应用配置
APP_NAME=DpTestPlatform API
APP_VERSION=1.0.0
DEBUG=false
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=mysql+aiomysql://username:password@localhost:3306/dp_test?charset=UTF8MB4

# 数据库连接池配置
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# JWT 配置 - 请在生产环境中更改这些值
SECRET_KEY=your-super-secret-key-change-this-in-production-must-be-at-least-32-characters
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS 配置 - 生产环境请限制具体域名
CORS_ORIGINS=["http://localhost:3000","http://localhost:5173","http://127.0.0.1:3000","http://127.0.0.1:5173"]

# 时区配置
TIMEZONE=Asia/Shanghai

# 日志配置
LOG_LEVEL=INFO

# Redis 配置 (可选)
REDIS_URL=redis://localhost:6379/0

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads/

# 分页配置
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100
