<template>
  <div class="page-container" v-loading="loading">
    <div v-if="execution">
      <!-- 简洁页面头部 -->
      <div class="page-header">
        <el-button @click="handleBack" :icon="ArrowLeft" class="back-btn">返回</el-button>
        <div class="header-info">
          <h1 class="page-title">执行记录详情</h1>
        </div>
        <div class="header-actions">
          <el-button
            v-if="execution.status === 'failed'"
            type="primary"
            @click="handleRetryExecution"
            :loading="retrying"
          >
            <el-icon><Refresh /></el-icon>
            重试执行
          </el-button>
          <el-button
            v-if="execution.is_running"
            type="warning"
            @click="handleCancelExecution"
          >
            <el-icon><Close /></el-icon>
            取消执行
          </el-button>
          <!-- 任务操作按钮 -->
          <template v-if="relatedTask">
            <el-button
              v-if="relatedTask.can_execute"
              type="primary"
              @click="handleExecuteTask"
            >
              <el-icon><VideoPlay /></el-icon>
              执行任务
            </el-button>
            <el-button
              v-if="relatedTask.can_stop"
              type="warning"
              @click="handleStopTask"
            >
              <el-icon><VideoPause /></el-icon>
              停止任务
            </el-button>
            <el-button
              v-if="relatedTask.can_enable"
              type="success"
              @click="handleEnableTask"
            >
              <el-icon><Check /></el-icon>
              启用任务
            </el-button>
            <el-button
              v-if="relatedTask.can_disable"
              type="danger"
              @click="handleDisableTask"
            >
              <el-icon><Close /></el-icon>
              禁用任务
            </el-button>
          </template>

          <el-dropdown @command="handleDropdownCommand" trigger="click">
            <el-button>
              更多操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="export">导出详情</el-dropdown-item>
                <el-dropdown-item command="delete" divided>删除记录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 概览卡片 - 使用自定义ArtStatsCard组件 -->
        <el-row :gutter="20" class="overview-section">
          <el-col :span="8">
            <ArtStatsCard
              icon="&#xe6c1;"
              title="关联任务"
              :description="execution.task_name || `Task-${execution.task_id}`"
              icon-color="#fff"
              icon-bg-color="rgb(var(--art-primary))"
              @click="handleViewTask"
            />
          </el-col>
          <el-col :span="8">
            <ArtStatsCard
              icon="&#xe6c2;"
              title="执行主机"
              :description="execution.host_name || `Host-${execution.host_id}`"
              icon-color="#fff"
              icon-bg-color="var(--el-color-warning)"
            />
          </el-col>
          <el-col :span="8">
            <ArtStatsCard
              icon="&#xe6c3;"
              title="执行时长"
              :description="execution.duration_seconds ? formatDuration(execution.duration_seconds) : '计算中...'"
              icon-color="#fff"
              icon-bg-color="var(--el-color-info)"
            />
          </el-col>
        </el-row>

        <!-- 详细信息区域 -->
        <el-row :gutter="20" class="details-section">
          <!-- 基本信息 -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <h3 class="card-title">
                    <el-icon><InfoFilled /></el-icon>
                    基本信息
                  </h3>
                </div>
              </template>
              <div class="info-list">
                <div class="info-item">
                  <span class="label">执行ID：</span>
                  <span class="value">{{ execution.id }}</span>
                </div>

                <div class="info-item">
                  <span class="label">开始时间：</span>
                  <span class="value">{{ formatDateTime(execution.start_time) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">结束时间：</span>
                  <span class="value">{{ execution.end_time ? formatDateTime(execution.end_time) : '未结束' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">执行状态：</span>
                  <span class="value">
                    <el-tag :type="getStatusTagType(execution.status)">
                      {{ getStatusLabel(execution.status) }}
                    </el-tag>
                  </span>
                </div>
              </div>
            </el-card>
          </el-col>

          <!-- 故障配置 -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <h3 class="card-title">
                    <el-icon><Setting /></el-icon>
                    故障配置
                  </h3>
                </div>
              </template>
              <div class="config-content">
                <pre v-if="execution.fault_config">{{ JSON.stringify(execution.fault_config, null, 2) }}</pre>
                <el-text type="info">无配置信息</el-text>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 执行日志 -->
        <el-card shadow="never" class="logs-section">
          <template #header>
            <div class="card-header">
              <h3 class="card-title">
                <el-icon><Document /></el-icon>
                执行日志
              </h3>
              <div class="header-actions">
                <el-tag v-if="execution.exit_code !== null" :type="execution.exit_code === 0 ? 'success' : 'danger'" size="small">
                  退出码: {{ execution.exit_code }}
                </el-tag>
                <el-button size="small" @click="refreshLogs">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>
          </template>
          <div class="logs-content">
            <!-- 日志类型选择 -->
            <el-tabs v-model="activeLogType" @tab-change="handleLogTypeChange" class="log-tabs">
              <el-tab-pane label="执行输出" name="output">
                <pre v-if="execution.output" class="log-content">{{ execution.output }}</pre>
                <el-empty v-else description="暂无执行输出" />
              </el-tab-pane>
              <el-tab-pane label="错误信息" name="error">
                <pre v-if="execution.error_message" class="log-content error-log">{{ execution.error_message }}</pre>
                <el-empty v-else description="暂无错误信息" />
              </el-tab-pane>
              <el-tab-pane label="执行命令" name="command">
                <pre v-if="execution.command" class="log-content command-log">{{ execution.command }}</pre>
                <el-empty v-else description="暂无执行命令" />
              </el-tab-pane>
              <el-tab-pane label="销毁输出" name="destroy" v-if="execution.destroy_output">
                <pre class="log-content">{{ execution.destroy_output }}</pre>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 加载失败状态 -->
    <div v-else-if="!loading" class="error-state">
      <el-result
        icon="error"
        title="加载失败"
        sub-title="无法获取执行记录详情，请检查记录是否存在"
      >
        <template #extra>
          <el-button type="primary" @click="handleBack">返回列表</el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  Close,
  ArrowDown,
  InfoFilled,
  Setting,
  Document,
  Timer,
  VideoPlay,
  VideoPause,
  Check
} from '@element-plus/icons-vue'
import { ChaosService } from '@/api/chaosApi'
import { useChaosTasksStore } from '@/store/business/chaos/tasks'
import type { ChaosTask } from '@/types/api/chaos'

defineOptions({ name: 'ExecutionDetail' })

const route = useRoute()
const router = useRouter()
const chaosTasksStore = useChaosTasksStore()

// 响应式数据
const loading = ref(true)
const retrying = ref(false)
const execution = ref<any>(null)
const activeLogType = ref('output')
const relatedTask = ref<ChaosTask | null>(null)

// 获取执行记录详情
const fetchExecutionDetail = async () => {
  try {
    loading.value = true
    const id = Number(route.params.id)
    const response = await ChaosService.getExecutionDetail(id)
    execution.value = response

    // 获取相关任务信息
    if (response.task_id) {
      try {
        relatedTask.value = await chaosTasksStore.fetchTask(response.task_id)
      } catch (error) {
        console.error('获取相关任务信息失败:', error)
      }
    }
  } catch (error) {
    console.error('获取执行记录详情失败:', error)
    ElMessage.error('获取执行记录详情失败')
  } finally {
    loading.value = false
  }
}

// 返回列表
const handleBack = () => {
  router.back()
}

// 重试执行
const handleRetryExecution = async () => {
  try {
    retrying.value = true
    await ChaosService.retryExecution(execution.value.id, {})
    ElMessage.success('重试执行成功')
    await fetchExecutionDetail()
  } catch (error) {
    console.error('重试执行失败:', error)
    ElMessage.error('重试执行失败')
  } finally {
    retrying.value = false
  }
}

// 取消执行
const handleCancelExecution = async () => {
  try {
    await ElMessageBox.confirm('确定要取消当前执行吗？', '确认取消', {
      type: 'warning'
    })
    // 注意：ChaosService中没有cancelExecution方法，这里需要实现或使用其他方式
    ElMessage.info('取消执行功能开发中...')
    // await ChaosService.cancelExecution(execution.value.id)
    // ElMessage.success('取消执行成功')
    // await fetchExecutionDetail()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消执行失败:', error)
      ElMessage.error('取消执行失败')
    }
  }
}

// 下拉菜单操作
const handleDropdownCommand = async (command: string) => {
  switch (command) {
    case 'export':
      ElMessage.info('导出功能开发中...')
      break
    case 'delete':
      try {
        await ElMessageBox.confirm('确定要删除这条执行记录吗？', '确认删除', {
          type: 'warning'
        })
        await ChaosService.deleteExecution(execution.value.id)
        ElMessage.success('删除成功')
        handleBack()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          ElMessage.error('删除失败')
        }
      }
      break
  }
}

// 查看任务
const handleViewTask = () => {
  router.push(`/chaos/tasks/${execution.value.task_id}`)
}

// 执行任务
const handleExecuteTask = async () => {
  if (!relatedTask.value) return

  try {
    await chaosTasksStore.executeTask(relatedTask.value.id, { force: false })
    ElMessage.success('任务执行成功')
    // 刷新相关任务信息
    relatedTask.value = await chaosTasksStore.fetchTask(relatedTask.value.id)
  } catch (error) {
    ElMessage.error('任务执行失败')
  }
}

// 停止任务
const handleStopTask = async () => {
  if (!relatedTask.value) return

  try {
    await ElMessageBox.confirm(
      `确定要停止任务 "${relatedTask.value.name}" 吗？`,
      '确认停止',
      { type: 'warning' }
    )

    await chaosTasksStore.stopTask(relatedTask.value.id)
    ElMessage.success('任务已停止')
    // 刷新相关任务信息
    relatedTask.value = await chaosTasksStore.fetchTask(relatedTask.value.id)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('停止任务失败')
    }
  }
}

// 启用任务
const handleEnableTask = async () => {
  if (!relatedTask.value) return

  try {
    await chaosTasksStore.enableTask(relatedTask.value.id)
    ElMessage.success('任务已启用')
    // 刷新相关任务信息
    relatedTask.value = await chaosTasksStore.fetchTask(relatedTask.value.id)
  } catch (error) {
    ElMessage.error('启用任务失败')
  }
}

// 禁用任务
const handleDisableTask = async () => {
  if (!relatedTask.value) return

  try {
    await ElMessageBox.confirm(
      `确定要禁用任务 "${relatedTask.value.name}" 吗？禁用后循环任务将停止调度。`,
      '确认禁用',
      { type: 'warning' }
    )

    await chaosTasksStore.disableTask(relatedTask.value.id)
    ElMessage.success('任务已禁用')
    // 刷新相关任务信息
    relatedTask.value = await chaosTasksStore.fetchTask(relatedTask.value.id)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('禁用任务失败')
    }
  }
}

// 刷新日志
const refreshLogs = async () => {
  if (!execution.value) return

  try {
    // 获取当前选中的日志类型的详细信息
    const logResponse = await ChaosService.getExecutionLog(execution.value.id, activeLogType.value)

    // 更新对应的日志内容
    if (activeLogType.value === 'output') {
      execution.value.output = logResponse.content
    } else if (activeLogType.value === 'error') {
      execution.value.error_message = logResponse.content
    } else if (activeLogType.value === 'command') {
      execution.value.command = logResponse.content
    } else if (activeLogType.value === 'destroy') {
      execution.value.destroy_output = logResponse.content
    }

    ElMessage.success('日志刷新成功')
  } catch (error) {
    console.error('刷新日志失败:', error)
    ElMessage.error('刷新日志失败')
  }
}

// 处理日志类型切换
const handleLogTypeChange = (logType: string | number) => {
  activeLogType.value = String(logType)
  // 这里可以添加切换日志类型时的额外逻辑
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    running: 'warning',
    success: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态图标
const getStatusIcon = (status: string) => {
  const iconMap: Record<string, any> = {
    running: Timer,
    success: 'SuccessFilled',
    failed: 'CircleCloseFilled',
    cancelled: 'WarningFilled'
  }
  return iconMap[status] || 'InfoFilled'
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    running: '执行中',
    success: '成功',
    failed: '失败',
    cancelled: '已取消'
  }
  return labelMap[status] || '未知'
}

// 格式化时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 格式化持续时间
const formatDuration = (seconds: number) => {
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  return `${hours}时${minutes}分${secs}秒`
}

onMounted(() => {
  fetchExecutionDetail()
})
</script>

<style lang="scss" scoped>
.page-container {
  background: var(--art-bg-color);
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;

  .back-btn {
    margin-top: 4px; /* 与标题对齐 */
  }

  .header-info {
    flex: 1;

    .page-title {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .header-meta {
      display: flex;
      align-items: center;
      gap: 12px;

      .execution-id {
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
    flex-shrink: 0;
  }
}

.main-content {
  .overview-section {
    margin-bottom: 20px;
  }

  .details-section {
    margin-bottom: 20px;
  }

  .logs-section {
    margin-bottom: 20px;
  }
}

/* 概览卡片样式已由ArtStatsCard组件处理 */

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }
}

.info-list {
  .info-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--art-root-card-border-color);

    &:last-child {
      border-bottom: none;
    }

    .label {
      width: 100px;
      color: var(--el-text-color-regular);
      flex-shrink: 0;
    }

    .value {
      color: var(--el-text-color-primary);
      flex: 1;
    }
  }
}

.config-content {
  pre {
    background: var(--el-fill-color-light);
    padding: 12px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.5;
    overflow-x: auto;
    margin: 0;
  }
}

.logs-content {
  .log-tabs {
    margin-top: 16px;
  }

  .log-content {
    background: var(--el-fill-color-light);
    padding: 16px;
    border-radius: 6px;
    font-size: 13px;
    line-height: 1.6;
    overflow-x: auto;
    margin: 0;
    max-height: 400px;
    overflow-y: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    white-space: pre-wrap;
    word-break: break-word;

    &.error-log {
      background: var(--el-color-error-light-9);
      color: var(--el-color-error);
      border: 1px solid var(--el-color-error-light-7);
    }

    &.command-log {
      background: var(--el-color-info-light-9);
      color: var(--el-color-info);
      border: 1px solid var(--el-color-info-light-7);
    }
  }
}

/* 移除不必要的样式类，使用Element Plus内置样式 */

.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;

    .header-actions {
      justify-content: flex-end;
    }
  }

  .page-container {
    padding: 12px;
  }
}
</style>
