/**
 * 环境管理API接口
 */
import request from '@/utils/http'
import type {
  EnvironmentCreateRequest,
  EnvironmentUpdateRequest,
  EnvironmentResponse,
  EnvironmentListResponse,
  EnvironmentListParams,
  EnvironmentStats,
  ConnectionTestRequest as EnvConnectionTestRequest,
  ConnectionTestResponse as EnvConnectionTestResponse,
  SupportedEnvironmentType
} from '@/types/api/environment'

// 重新导出类型以保持向后兼容
export type {
  EnvironmentCreateRequest as EnvironmentCreate,
  EnvironmentUpdateRequest as EnvironmentUpdate,
  EnvironmentResponse,
  EnvironmentListResponse,
  EnvironmentListParams,
  SupportedEnvironmentType
}

// 连接测试相关类型（避免命名冲突）
export type ConnectionTestRequest = EnvConnectionTestRequest
export type ConnectionTestResponse = EnvConnectionTestResponse

export interface EnvironmentListRequest {
  page?: number
  size?: number
  keyword?: string
  env_type?: string
  status?: string
  tags?: string
}

// 导入统一的API类型
import type { PaginationData } from '@/types/api/common'


export interface SupportedType {
  type: string
  name: string
  description: string
}

export class EnvironmentService {
  /**
   * 获取环境列表
   */
  static async getEnvironmentList(params: EnvironmentListRequest = {}): Promise<EnvironmentListData> {
    // 转换参数格式：current -> page
    const queryParams = {
      page: (params as any).current || params.page || 1,
      size: params.size || 10,
      keyword: params.keyword,
      env_type: params.env_type,
      status: params.status,
      tags: params.tags
    }

    const response = await request.get<EnvironmentListData>({
      url: '/api/env',
      params: queryParams,
      useRestfulFormat: true  // 使用RESTful格式响应
    })
    return response
  }

  /**
   * 获取环境详情
   */
  static async getEnvironmentDetail(envId: number): Promise<EnvironmentResponse> {
    const response = await request.get<EnvironmentResponse>({
      url: `/api/env/${envId}`
    })
    return response
  }

  /**
   * 创建环境
   */
  static async createEnvironment(data: EnvironmentCreateRequest): Promise<EnvironmentResponse> {
    const response = await request.post<EnvironmentResponse>({
      url: '/api/env',
      data
    })
    return response
  }

  /**
   * 更新环境
   */
  static async updateEnvironment(envId: number, data: EnvironmentUpdateRequest): Promise<EnvironmentResponse> {
    const response = await request.put<EnvironmentResponse>({
      url: `/api/env/${envId}`,
      data: data  // PUT请求应该使用data
    })
    return response
  }

  /**
   * 删除环境
   */
  static async deleteEnvironment(envId: number): Promise<boolean> {
    const response = await request.del<boolean>({
      url: `/api/env/${envId}`
    })
    return response
  }

  /**
   * 测试环境连接（会更新环境状态）
   */
  static async testEnvironmentConnection(envId: number, data: Partial<EnvConnectionTestRequest> = {}): Promise<EnvConnectionTestResponse> {
    const response = await request.post<ConnectionTestResponse>({
      url: `/api/env/${envId}/test`,
      data: data  // POST请求应该使用data
    })
    return response
  }

  /**
   * 测试环境配置连接（不需要先保存环境）
   */
  static async testConfigConnection(configData: {
    type: string
    host?: string
    port?: number
    config?: Record<string, any>
    timeout?: number
  }): Promise<ConnectionTestResponse> {
    // 直接调用通用测试接口
    const testRequest = {
      type: configData.type,
      config: {
        host: configData.host,
        port: configData.port,
        ...configData.config
      },
      timeout: configData.timeout || 10
    }

    const response = await request.post<ConnectionTestResponse>({
      url: '/api/env/test',
      params: testRequest
    })
    return response
  }

  /**
   * 批量测试环境连接
   */
  static async batchTestConnections(envIds: number[], timeout: number = 10): Promise<any[]> {
    const response = await request.post<any[]>({
      url: '/api/env/batch-test',
      params: { env_ids: envIds, timeout }
    })
    return response
  }

  /**
   * 获取环境统计信息
   */
  static async getEnvironmentStats(): Promise<EnvironmentStats> {
    const response = await request.get<EnvironmentStats>({
      url: '/api/env/stats/overview'
    })
    return response
  }

  /**
   * 获取支持的环境类型
   */
  static async getSupportedTypes(): Promise<SupportedType[]> {
    const response = await request.get<SupportedType[]>({
      url: '/api/env/types/supported'
    })
    return response || []
  }

  /**
   * 根据类型获取环境列表
   */
  static async getEnvironmentsByType(envType: string): Promise<EnvironmentResponse[]> {
    const response = await request.get<EnvironmentResponse[]>({
      url: `/api/env/types/${envType}`
    })
    return response
  }

  /**
   * 获取可用环境列表（简化版，只包含基本信息）
   */
  static async getAvailableEnvironments(envType: string = 'ssh'): Promise<EnvironmentResponse[]> {
    // 复用列表接口，只获取已连接的环境
    const queryParams = {
      page: 1,
      size: 100,  // 获取大量数据
      env_type: envType,
      status: 'connected'  // 只获取已连接的环境
    }

    const response = await request.get<EnvironmentListData>({
      url: '/api/env',
      params: queryParams
    })
    return response.items  // 返回环境列表数组
  }
}