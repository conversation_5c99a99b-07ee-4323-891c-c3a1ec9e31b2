/**
 * 模型管理状态管理
 * 迁移自 src/stores/model/index.ts
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { usePagination } from '@/store/shared/pagination'
import { useLoading } from '@/store/shared/loading'
import { globalCache } from '@/store/shared/cache'
import { ModelConfigService } from '@/api/modelApi'
import type {
  ModelConfigResponse,
  ModelConfigCreateRequest,
  ModelConfigUpdateRequest,
  ModelConfigListParams,
  ModelHealthCheckResponse,
  ModelCallRequest,
  ModelCallResponse,
  ModelStatsResponse
} from '@/types/api/model'

export const useModelStore = defineStore('model', () => {
  // ==================== 状态定义 ====================
  
  /** 模型配置列表 */
  const modelConfigs = ref<ModelConfigResponse[]>([])
  
  /** 当前选中的模型配置 */
  const currentModelConfig = ref<ModelConfigResponse | null>(null)
  
  /** 可用模型列表 */
  const availableModels = ref<ModelConfigResponse[]>([])
  
  /** 模型统计信息 */
  const modelStats = ref<ModelStatsResponse | null>(null)
  
  /** 健康检查结果 */
  const healthCheckResults = ref<ModelHealthCheckResponse[]>([])
  
  // 分页和加载状态
  const pagination = usePagination({ defaultSize: 20 })
  const loading = useLoading()
  const statsLoading = useLoading()
  const healthLoading = useLoading()
  const callLoading = useLoading()

  // ==================== 计算属性 ====================
  
  const totalModels = computed(() => pagination.pagination.total)
  
  const activeModels = computed(() =>
    modelConfigs.value.filter(model => model.status === 'enabled')
  )

  const inactiveModels = computed(() =>
    modelConfigs.value.filter(model => model.status === 'disabled')
  )

  const modelsByProvider = computed(() => {
    const grouped: Record<string, ModelConfigResponse[]> = {}
    modelConfigs.value.forEach(model => {
      if (!grouped[model.platform]) {
        grouped[model.platform] = []
      }
      grouped[model.platform].push(model)
    })
    return grouped
  })

  const healthyModels = computed(() =>
    modelConfigs.value.filter(model => {
      const healthResult = healthCheckResults.value.find(result => result.model_id === model.id)
      return healthResult?.is_healthy === true
    })
  )

  // ==================== 模型管理方法 ====================
  
  /**
   * 获取模型配置列表
   */
  const fetchModelConfigs = async (params: ModelConfigListParams = {}) => {
    const queryParams = {
      ...params,
      ...pagination.params.value
    }

    return await loading.withLoading(async () => {
      const response = await ModelConfigService.getModelConfigList(queryParams)

      modelConfigs.value = response.items  // 使用新的items字段
      pagination.updateFromResponse({
        total: response.total,
        current: response.page,  // 使用新的page字段
        size: response.size
      })

      return response
    })
  }

  /**
   * 创建模型配置
   */
  const createModelConfig = async (data: ModelConfigCreateRequest) => {
    return await loading.withLoading(async () => {
      const response = await ModelConfigService.createModelConfig(data)
      
      // 添加到列表开头
      modelConfigs.value.unshift(response)
      pagination.setTotal(pagination.pagination.total + 1)
      
      // 清除相关缓存
      globalCache.remove('model_stats')
      globalCache.remove('available_models')
      
      return response
    })
  }

  /**
   * 获取模型配置详情
   */
  const fetchModelConfig = async (id: number) => {
    // 先尝试从缓存获取
    const cacheKey = `model_config_detail_${id}`
    const cached = globalCache.get(cacheKey)
    if (cached) {
      currentModelConfig.value = cached
      return cached
    }

    return await loading.withLoading(async () => {
      const response = await ModelConfigService.getModelConfigDetail(id)
      
      currentModelConfig.value = response
      
      // 缓存模型配置详情
      globalCache.set(cacheKey, response, 5 * 60 * 1000) // 5分钟缓存
      
      return response
    })
  }

  /**
   * 更新模型配置
   */
  const updateModelConfig = async (id: number, data: ModelConfigUpdateRequest) => {
    return await loading.withLoading(async () => {
      const response = await ModelConfigService.updateModelConfig(id, data)
      
      // 更新本地状态
      const index = modelConfigs.value.findIndex(model => model.id === id)
      if (index !== -1) {
        modelConfigs.value[index] = response
      }
      
      if (currentModelConfig.value?.id === id) {
        currentModelConfig.value = response
      }
      
      // 清除相关缓存
      globalCache.remove(`model_config_detail_${id}`)
      globalCache.remove('available_models')
      
      return response
    })
  }

  /**
   * 删除模型配置
   */
  const deleteModelConfig = async (id: number) => {
    return await loading.withLoading(async () => {
      await ModelConfigService.deleteModelConfig(id)
      
      // 从本地状态中移除
      const index = modelConfigs.value.findIndex(model => model.id === id)
      if (index !== -1) {
        modelConfigs.value.splice(index, 1)
        pagination.setTotal(pagination.pagination.total - 1)
      }
      
      if (currentModelConfig.value?.id === id) {
        currentModelConfig.value = null
      }
      
      // 清除相关缓存
      globalCache.remove(`model_config_detail_${id}`)
      globalCache.remove('model_stats')
      globalCache.remove('available_models')
      
      // 清除健康检查结果
      healthCheckResults.value = healthCheckResults.value.filter(result => result.model_id !== id)
    })
  }

  /**
   * 获取可用模型列表
   */
  const fetchAvailableModels = async () => {
    // 先尝试从缓存获取
    const cached = globalCache.get('available_models')
    if (cached) {
      availableModels.value = cached
      return cached
    }

    return await loading.withLoading(async () => {
      const response = await ModelConfigService.getAvailableModels()
      
      availableModels.value = response
      
      // 缓存可用模型列表
      globalCache.set('available_models', response, 10 * 60 * 1000) // 10分钟缓存
      
      return response
    })
  }

  /**
   * 获取模型统计信息
   */
  const fetchModelStats = async () => {
    // 先尝试从缓存获取
    const cached = globalCache.get('model_stats')
    if (cached) {
      modelStats.value = cached
      return cached
    }

    return await statsLoading.withLoading(async () => {
      const response = await ModelConfigService.getModelStats()
      
      modelStats.value = response
      
      // 缓存统计信息
      globalCache.set('model_stats', response, 2 * 60 * 1000) // 2分钟缓存
      
      return response
    })
  }

  /**
   * 健康检查
   */
  const healthCheck = async (id: number) => {
    return await healthLoading.withLoading(async () => {
      const response = await ModelConfigService.healthCheckModel(id)

      // 更新健康检查结果
      const existingIndex = healthCheckResults.value.findIndex(result => result.model_id === id)
      if (existingIndex !== -1) {
        healthCheckResults.value[existingIndex] = response
      } else {
        healthCheckResults.value.push(response)
      }

      return response
    })
  }

  /**
   * 批量健康检查
   */
  const batchHealthCheck = async (ids: number[]) => {
    return await healthLoading.withLoading(async () => {
      const results = await ModelConfigService.batchHealthCheck(ids)

      // 更新健康检查结果
      results.forEach((result) => {
        const existingIndex = healthCheckResults.value.findIndex(r => r.model_id === result.model_id)
        if (existingIndex !== -1) {
          healthCheckResults.value[existingIndex] = result
        } else {
          healthCheckResults.value.push(result)
        }
      })

      return results
    })
  }

  /**
   * 调用模型
   */
  const callModel = async (data: ModelCallRequest) => {
    return await callLoading.withLoading(async () => {
      const response = await ModelConfigService.callModel(data)
      return response
    })
  }

  /**
   * 启用模型
   */
  const enableModel = async (id: number) => {
    return await loading.withLoading(async () => {
      const response = await ModelConfigService.updateModelConfig(id, { status: 'enabled' })

      // 更新本地状态
      const index = modelConfigs.value.findIndex(model => model.id === id)
      if (index !== -1) {
        modelConfigs.value[index] = response
      }

      if (currentModelConfig.value?.id === id) {
        currentModelConfig.value = response
      }

      return response
    })
  }

  /**
   * 禁用模型
   */
  const disableModel = async (id: number) => {
    return await loading.withLoading(async () => {
      const response = await ModelConfigService.updateModelConfig(id, { status: 'disabled' })

      // 更新本地状态
      const index = modelConfigs.value.findIndex(model => model.id === id)
      if (index !== -1) {
        modelConfigs.value[index] = response
      }

      if (currentModelConfig.value?.id === id) {
        currentModelConfig.value = response
      }

      return response
    })
  }

  // ==================== 辅助方法 ====================
  
  /**
   * 重置状态
   */
  const resetState = () => {
    modelConfigs.value = []
    currentModelConfig.value = null
    availableModels.value = []
    modelStats.value = null
    healthCheckResults.value = []
    pagination.reset()
    loading.reset()
    statsLoading.reset()
    healthLoading.reset()
    callLoading.reset()
  }

  /**
   * 清除健康检查结果
   */
  const clearHealthResults = () => {
    healthCheckResults.value = []
  }

  return {
    // 状态
    modelConfigs: readonly(modelConfigs),
    currentModelConfig: readonly(currentModelConfig),
    availableModels: readonly(availableModels),
    modelStats: readonly(modelStats),
    healthCheckResults: readonly(healthCheckResults),
    pagination,
    loading,
    statsLoading,
    healthLoading,
    callLoading,
    
    // 计算属性
    totalModels,
    activeModels,
    inactiveModels,
    modelsByProvider,
    healthyModels,
    
    // 方法
    fetchModelConfigs,
    createModelConfig,
    fetchModelConfig,
    updateModelConfig,
    deleteModelConfig,
    fetchAvailableModels,
    fetchModelStats,
    healthCheck,
    batchHealthCheck,
    callModel,
    enableModel,
    disableModel,
    resetState,
    clearHealthResults
  }
})
