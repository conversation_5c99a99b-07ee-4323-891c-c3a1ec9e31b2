"""
数据库连接管理
"""
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import AsyncSessionLocal, async_engine
from .base import Base


async def get_db() -> AsyncSession: # type: ignore
    """
    获取数据库会话的依赖注入函数
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db():
    """
    初始化数据库，创建所有表
    """
    async with async_engine.begin() as conn:
        # 导入所有模型以确保它们被注册到Base.metadata
        from app.models.user.user import User  # noqa
        from app.models.user.role import Role  # noqa
        
        # 创建所有表
        await conn.run_sync(Base.metadata.create_all)


async def close_db():
    """
    关闭数据库连接
    """
    await async_engine.dispose() 