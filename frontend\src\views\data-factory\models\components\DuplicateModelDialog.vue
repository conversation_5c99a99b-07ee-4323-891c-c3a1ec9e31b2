<template>
  <el-dialog
    v-model="dialogVisible"
    title="复制数据模型"
    width="500px"
    :before-close="handleClose"
    class="duplicate-model-dialog"
  >
    <div v-if="sourceModel" class="dialog-content">
      <!-- 源模型信息 -->
      <div class="source-model-info">
        <h4>源模型信息</h4>
        <div class="model-card">
          <div class="model-header">
            <span class="model-name">{{ sourceModel.name }}</span>
            <el-tag type="info" size="small">{{ sourceModel.version }}</el-tag>
          </div>
          <p v-if="sourceModel.description" class="model-description">
            {{ sourceModel.description }}
          </p>
          <div class="model-meta">
            <el-tag size="small">{{ sourceModel.fields_config?.length || 0 }} 个字段</el-tag>
            <el-tag v-if="sourceModel.category" size="small" type="success">
              {{ sourceModel.category }}
            </el-tag>
            <el-tag size="small" type="warning">使用 {{ sourceModel.usage_count || 0 }} 次</el-tag>
          </div>
        </div>
      </div>

      <!-- 复制配置表单 -->
      <div class="duplicate-form">
        <h4>复制配置</h4>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
          label-position="left"
        >
          <el-form-item label="新模型名称" prop="name" required>
            <el-input
              v-model="form.name"
              placeholder="请输入新模型名称"
              clearable
              maxlength="100"
              show-word-limit
            />
            <div class="form-tip">
              新模型将继承源模型的所有字段配置和设置
            </div>
          </el-form-item>

          <el-form-item label="模型描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="3"
              placeholder="请输入模型描述（可选）"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="模型分类" prop="category">
            <el-input
              v-model="form.category"
              placeholder="请输入分类名称（可选）"
              clearable
              maxlength="50"
            />
          </el-form-item>

          <el-form-item label="版本号" prop="version">
            <el-input
              v-model="form.version"
              placeholder="版本号（默认：1.0.0）"
              clearable
              maxlength="20"
            />
            <div class="form-tip">
              建议使用语义化版本号，如：1.0.0
            </div>
          </el-form-item>

          <el-form-item label="复制选项">
            <el-checkbox-group v-model="form.options">
              <el-checkbox label="copyTags">复制标签</el-checkbox>
              <el-checkbox label="copyCategory">复制分类</el-checkbox>
              <el-checkbox label="resetUsage">重置使用次数</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- 预览信息 -->
      <div class="preview-info">
        <h4>复制预览</h4>
        <div class="preview-card">
          <div class="preview-item">
            <span class="label">新模型名称：</span>
            <span class="value">{{ form.name || '未设置' }}</span>
          </div>
          <div class="preview-item">
            <span class="label">版本号：</span>
            <span class="value">{{ form.version || '1.0.0' }}</span>
          </div>
          <div class="preview-item">
            <span class="label">字段数量：</span>
            <span class="value">{{ sourceModel.fields_config?.length || 0 }} 个</span>
          </div>
          <div class="preview-item">
            <span class="label">分类：</span>
            <span class="value">
              {{ getPreviewCategory() }}
            </span>
          </div>
          <div class="preview-item">
            <span class="label">标签：</span>
            <span class="value">
              {{ getPreviewTags() }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">
          确认复制
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

interface Props {
  visible: boolean
  sourceModel: Api.DataFactory.ModelInfo | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm', newName: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  name: '',
  description: '',
  category: '',
  version: '1.0.0',
  options: ['resetUsage'] as string[]
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入新模型名称', trigger: 'blur' },
    { min: 1, max: 100, message: '名称长度应在 1 到 100 个字符', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/,
      message: '名称只能包含字母、数字、下划线和中文字符',
      trigger: 'blur'
    }
  ],
  description: [
    { max: 1000, message: '描述长度不能超过 1000 个字符', trigger: 'blur' }
  ],
  category: [
    { max: 50, message: '分类长度不能超过 50 个字符', trigger: 'blur' }
  ],
  version: [
    { max: 20, message: '版本号长度不能超过 20 个字符', trigger: 'blur' },
    {
      pattern: /^\d+\.\d+\.\d+$/,
      message: '版本号格式应为 x.y.z（如：1.0.0）',
      trigger: 'blur'
    }
  ]
}

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal && props.sourceModel) {
      initForm()
    }
  },
  { immediate: true }
)

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 方法
const initForm = () => {
  if (!props.sourceModel) return

  // 重置表单
  Object.assign(form, {
    name: `${props.sourceModel.name}_copy`,
    description: props.sourceModel.description ? `复制自：${props.sourceModel.description}` : '',
    category: props.sourceModel.category || '',
    version: '1.0.0',
    options: ['resetUsage']
  })

  // 如果源模型有分类和标签，默认选中复制选项
  if (props.sourceModel.category) {
    form.options.push('copyCategory')
  }
  if (props.sourceModel.tags && props.sourceModel.tags.length > 0) {
    form.options.push('copyTags')
  }

  // 清除表单验证
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const handleClose = () => {
  dialogVisible.value = false
  // 重置表单
  Object.assign(form, {
    name: '',
    description: '',
    category: '',
    version: '1.0.0',
    options: ['resetUsage']
  })
}

const handleConfirm = async () => {
  if (!formRef.value || !props.sourceModel) return

  try {
    // 表单验证
    await formRef.value.validate()
    
    loading.value = true
    
    // 发送确认事件
    emit('confirm', form.name)
    
  } catch (error) {
    ElMessage.error('请检查表单输入')
  } finally {
    loading.value = false
  }
}

const getPreviewCategory = () => {
  if (form.options.includes('copyCategory') && props.sourceModel?.category) {
    return form.category || props.sourceModel.category
  }
  return form.category || '无'
}

const getPreviewTags = () => {
  if (form.options.includes('copyTags') && props.sourceModel?.tags?.length) {
    return props.sourceModel.tags.join(', ')
  }
  return '无'
}
</script>

<style scoped lang="scss">
.duplicate-model-dialog {
  .dialog-content {
    .source-model-info {
      margin-bottom: 24px;

      h4 {
        margin: 0 0 12px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }

      .model-card {
        background: var(--el-bg-color-page);
        padding: 16px;
        border-radius: 8px;
        border: 1px solid var(--el-border-color-light);

        .model-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          .model-name {
            font-weight: 500;
            font-size: 16px;
            color: var(--el-text-color-primary);
          }
        }

        .model-description {
          margin: 8px 0;
          color: var(--el-text-color-regular);
          font-size: 14px;
          line-height: 1.5;
        }

        .model-meta {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
        }
      }
    }

    .duplicate-form {
      margin-bottom: 24px;

      h4 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }

      .form-tip {
        font-size: 12px;
        color: var(--el-text-color-placeholder);
        margin-top: 4px;
        line-height: 1.4;
      }
    }

    .preview-info {
      h4 {
        margin: 0 0 12px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }

      .preview-card {
        background: var(--el-fill-color-light);
        padding: 16px;
        border-radius: 8px;

        .preview-item {
          display: flex;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            min-width: 100px;
            color: var(--el-text-color-regular);
            font-size: 14px;
          }

          .value {
            color: var(--el-text-color-primary);
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>
