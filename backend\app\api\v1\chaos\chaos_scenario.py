"""
混沌测试故障场景API路由
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, Path, Query, Body

from app.api.deps import get_current_user
from app.core.dependencies import get_chaos_scenario_service
from app.core.responses import response_builder
from app.models.user.user import User
from app.services.chaos.chaos_scenario_service import ChaosScenarioService
from app.schemas.base import PaginationResponse
from app.schemas.chaos.chaos_scenario import (
    ChaosScenarioCreate, ChaosScenarioUpdate, ChaosScenarioResponse, ChaosScenarioListResponse,
    ChaosScenarioForTaskResponse, ChaosScenarioStatistics, ChaosScenarioValidateRequest,
    ChaosScenarioValidateResponse, ChaosScenarioQuery, ChaosScenarioPageResponse
)

router = APIRouter()


@router.get("", response_model=ChaosScenarioPageResponse, summary="查询场景列表")
async def list_scenarios(
    service: ChaosScenarioService = Depends(get_chaos_scenario_service),
    query: ChaosScenarioQuery = Depends(),  # 自动接收并校验查询参数
    _current_user: User = Depends(get_current_user)
):
    """查询场景列表，支持关键词搜索、类型筛选和分页"""
    result = await service.list_scenarios(query)
    return response_builder.success(result)


@router.get("/for-task", response_model=List[ChaosScenarioForTaskResponse], summary="获取任务创建用的场景列表")
async def list_scenarios_for_task(
    service: ChaosScenarioService = Depends(get_chaos_scenario_service),
    _current_user: User = Depends(get_current_user)
):
    """获取任务创建时使用的场景列表，包含参数信息"""
    result = await service.list_scenarios_for_task()
    return response_builder.success(result)


@router.post("", response_model=ChaosScenarioResponse, status_code=201, summary="创建场景")
async def create_scenario(
    scenario_data: ChaosScenarioCreate,
    service: ChaosScenarioService = Depends(get_chaos_scenario_service),
    current_user: User = Depends(get_current_user)
):
    """创建故障场景"""
    scenario = await service.create(scenario_data, str(current_user.id))
    return response_builder.created(scenario)


@router.get("/{scenario_id}", response_model=ChaosScenarioResponse, summary="获取场景详情")
async def get_scenario(
    scenario_id: int = Path(..., description="场景ID"),
    service: ChaosScenarioService = Depends(get_chaos_scenario_service),
    _current_user: User = Depends(get_current_user)
):
    """获取场景详情"""
    scenario = await service.get_by_id(scenario_id)
    return response_builder.success(scenario)


@router.put("/{scenario_id}", response_model=ChaosScenarioResponse, summary="更新场景")
async def update_scenario(
    scenario_id: int = Path(..., description="场景ID"),
    scenario_data: ChaosScenarioUpdate = Body(...),
    service: ChaosScenarioService = Depends(get_chaos_scenario_service),
    current_user: User = Depends(get_current_user)
):
    """更新故障场景"""
    scenario = await service.update(scenario_id, scenario_data, str(current_user.id))
    return response_builder.success(scenario)


@router.delete("/{scenario_id}", status_code=204, summary="删除场景")
async def delete_scenario(
    scenario_id: int = Path(..., description="场景ID"),
    service: ChaosScenarioService = Depends(get_chaos_scenario_service),
    current_user: User = Depends(get_current_user)
):
    """删除故障场景"""
    await service.delete(scenario_id)


@router.post("/validate", response_model=ChaosScenarioValidateResponse, summary="验证场景参数")
async def validate_scenario_params(
    request: ChaosScenarioValidateRequest,
    service: ChaosScenarioService = Depends(get_chaos_scenario_service),
    current_user: User = Depends(get_current_user)
):
    """验证场景参数"""
    result = await service.validate_scenario_params(request)
    return response_builder.success(result)


@router.post("/{scenario_id}/use", status_code=204, summary="使用场景")
async def use_scenario(
    scenario_id: int = Path(..., description="场景ID"),
    service: ChaosScenarioService = Depends(get_chaos_scenario_service),
    current_user: User = Depends(get_current_user)
):
    """使用场景（增加使用次数）"""
    await service.increment_usage(scenario_id)


@router.get("/for-task", response_model=List[ChaosScenarioForTaskResponse], summary="获取任务创建用的场景列表")
async def list_scenarios_for_task(
    service: ChaosScenarioService = Depends(get_chaos_scenario_service),
    _current_user: User = Depends(get_current_user)
):
    """获取任务创建时使用的场景列表，包含参数信息"""
    result = await service.list_scenarios_for_task()
    return response_builder.success(result)