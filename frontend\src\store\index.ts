import type { App } from 'vue'
import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'
import { StorageKeyManager } from '@/utils/storage/storage-key-manager'

export const store = createPinia()

// 禁用开发模式的 store 安装提示
if (import.meta.env.DEV) {
  // 重写 console.log 来过滤 Pinia 的安装提示
  const originalLog = console.log
  console.log = (...args: any[]) => {
    const message = args.join(' ')
    if (message.includes('🍍') && message.includes('store installed')) {
      return // 忽略 Pinia store 安装提示
    }
    originalLog.apply(console, args)
  }
}

// 创建存储键管理器实例
const storageKeyManager = new StorageKeyManager()

// 配置持久化插件
store.use(
  createPersistedState({
    key: (storeId: string) => storageKeyManager.getStorageKey(storeId),
    storage: localStorage,
    serializer: {
      serialize: JSON.stringify,
      deserialize: JSON.parse
    }
  })
)

// ==================== 核心状态管理 ====================
export { useUserStore } from './core/user'
export { useSettingStore } from './core/setting'

// ==================== 业务状态管理 ====================
// 混沌测试模块
export { useChaosStore } from './business/chaos/index'
export { useChaosTasksStore } from './business/chaos/tasks'
export { useChaosScenariosStore } from './business/chaos/scenarios'
export { useChaosExecutionsStore } from './business/chaos/executions'

// 环境管理模块
export { useEnvironmentStore } from './business/environment/index'

// 模型管理模块
export { useModelStore } from './business/model/index'

// ==================== 共享工具 ====================
export { usePagination, createPaginationHandlers } from './shared/pagination'
export { useLoading, useMultipleLoading, globalLoading } from './shared/loading'
export { useCache, globalCache } from './shared/cache'

// ==================== 兼容性导出 ====================
// 为了向后兼容，保留一些旧的导出
export { useUserStore as useUserStoreOld } from './modules/user'
export { useSettingStore as useSettingStoreOld } from './modules/setting'

/**
 * 初始化 Store
 */
export function initStore(app: App<Element>): void {
  app.use(store)
}
