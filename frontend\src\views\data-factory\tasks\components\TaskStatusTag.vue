<template>
  <div class="task-status-tag">
    <el-tag
      :type="statusConfig.type"
      :effect="statusConfig.effect"
      size="small"
      class="status-tag"
    >
      <el-icon class="status-icon">
        <component :is="statusConfig.icon" />
      </el-icon>
      <span class="status-text">{{ statusConfig.text }}</span>
    </el-tag>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Clock,
  Loading,
  CircleCheck,
  CircleClose,
  WarningFilled
} from '@element-plus/icons-vue'

interface Props {
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
}

const props = defineProps<Props>()

// 状态配置
const statusConfig = computed(() => {
  const configs = {
    pending: {
      type: 'info' as const,
      effect: 'light' as const,
      icon: Clock,
      text: '等待执行',
      color: '#909399'
    },
    running: {
      type: 'primary' as const,
      effect: 'light' as const,
      icon: Loading,
      text: '正在执行',
      color: '#409eff'
    },
    completed: {
      type: 'success' as const,
      effect: 'light' as const,
      icon: CircleCheck,
      text: '执行完成',
      color: '#67c23a'
    },
    failed: {
      type: 'danger' as const,
      effect: 'light' as const,
      icon: CircleClose,
      text: '执行失败',
      color: '#f56c6c'
    },
    cancelled: {
      type: 'warning' as const,
      effect: 'light' as const,
      icon: WarningFilled,
      text: '已取消',
      color: '#e6a23c'
    }
  }
  
  return configs[props.status] || configs.pending
})
</script>

<style scoped lang="scss">
.task-status-tag {
  .status-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;

    .status-icon {
      font-size: 14px;
    }

    .status-text {
      font-size: 12px;
    }
  }
}
</style>
