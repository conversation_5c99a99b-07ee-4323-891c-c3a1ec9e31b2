"""
定时任务执行历史模型
"""
from datetime import datetime
from typing import Optional, Dict, Any

from sqlalchemy import Column, BigInteger, String, JSON, TIMESTAMP, Integer, Text, Index, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import declarative_base

Base = declarative_base()


class ScheduleTaskHistory(Base):
    """定时任务执行历史模型"""
    __tablename__ = "schedule_task_history"
    __table_args__ = (
        Index('idx_schedule_task_history_task_id', 'task_id'),
        Index('idx_schedule_task_history_status', 'status'),
        Index('idx_schedule_task_history_scheduled_time', 'scheduled_time'),
        Index('idx_schedule_task_history_created_at', 'created_at'),
        {'comment': '定时任务执行历史表'}
    )

    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="主键ID")
    
    # 任务信息
    task_id = Column(String(191), nullable=False, comment="任务ID")
    task_name = Column(String(255), nullable=False, comment="任务名称")
    task_type = Column(String(50), nullable=False, comment="任务类型")
    
    # 执行信息
    function_path = Column(String(500), nullable=False, comment="执行函数路径")
    function_args = Column(JSON, nullable=True, comment="函数参数")
    function_kwargs = Column(JSON, nullable=True, comment="函数关键字参数")
    
    # 调度信息
    trigger_type = Column(String(50), nullable=False, comment="触发器类型：date/interval/cron")
    scheduled_time = Column(TIMESTAMP, nullable=True, comment="计划执行时间")
    
    # 执行时间
    actual_start_time = Column(TIMESTAMP, nullable=True, comment="实际开始时间")
    actual_end_time = Column(TIMESTAMP, nullable=True, comment="实际结束时间")
    duration_seconds = Column(Integer, nullable=True, comment="执行耗时（秒）")
    
    # 执行结果
    status = Column(String(20), nullable=False, default="running", comment="执行状态：running/success/failed/timeout")
    result = Column(JSON, nullable=True, comment="执行结果")
    error_message = Column(Text, nullable=True, comment="错误信息")
    exception_traceback = Column(Text, nullable=True, comment="异常堆栈")

    # 时间戳
    created_at = Column(TIMESTAMP, nullable=False, server_default=text('CURRENT_TIMESTAMP'), comment="创建时间")
    
    def __repr__(self):
        return f"<ScheduleTaskHistory(id={self.id}, task_id='{self.task_id}', status='{self.status}')>"
    
    @property
    def is_completed(self) -> bool:
        """是否已完成"""
        return self.status in ['success', 'failed', 'timeout']
    
    @property
    def is_successful(self) -> bool:
        """是否执行成功"""
        return self.status == 'success'
    
    def mark_started(self, start_time: Optional[datetime] = None) -> None:
        """标记开始执行"""
        self.actual_start_time = start_time or datetime.now()
        self.status = "running"
    
    def mark_completed(self,
                      status: str,
                      end_time: Optional[datetime] = None,
                      result: Optional[Dict[str, Any]] = None,
                      error_message: Optional[str] = None,
                      exception_traceback: Optional[str] = None) -> None:
        """标记执行完成"""
        if end_time is None:
            from app.utils.timezone import now
            end_time = now()
        self.actual_end_time = end_time
        self.status = status
        self.result = result
        self.error_message = error_message
        self.exception_traceback = exception_traceback
        
        # 计算执行耗时
        if self.actual_start_time and self.actual_end_time:
            duration = self.actual_end_time - self.actual_start_time
            self.duration_seconds = int(duration.total_seconds())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'task_id': self.task_id,
            'task_name': self.task_name,
            'task_type': self.task_type,
            'function_path': self.function_path,
            'function_args': self.function_args,
            'function_kwargs': self.function_kwargs,
            'trigger_type': self.trigger_type,
            'scheduled_time': self.scheduled_time.isoformat() if self.scheduled_time else None,
            'actual_start_time': self.actual_start_time.isoformat() if self.actual_start_time else None,
            'actual_end_time': self.actual_end_time.isoformat() if self.actual_end_time else None,
            'duration_seconds': self.duration_seconds,
            'status': self.status,
            'result': self.result,
            'error_message': self.error_message,
            'is_completed': self.is_completed,
            'is_successful': self.is_successful,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
