<template>
  <ArtSearchBar
    :filter="filter as Record<string, any>"
    :items="searchItems"
    @search="handleSearch"
    @reset="handleReset"
    @update:filter="$emit('update:filter', $event as ChaosScenarioSearchParams)"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ArtSearchBar from '@/components/core/forms/art-search-bar/index.vue'
import type { ChaosScenarioSearchParams } from '@/types/api/chaos'
import type { SearchFormItem } from '@/types/component'

defineOptions({ name: 'ScenarioSearch' })

interface Props {
  filter: ChaosScenarioSearchParams
}

interface Emits {
  (e: 'search'): void
  (e: 'reset'): void
  (e: 'update:filter', value: ChaosScenarioSearchParams): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 搜索项配置
const searchItems = ref<SearchFormItem[]>([
  {
    prop: 'keyword',
    label: '关键词',
    type: 'input' as const,
    placeholder: '搜索场景名称或描述'
  },
  {
    prop: 'fault_type',
    label: '故障类型',
    type: 'select' as const,
    placeholder: '选择故障类型',
    options: [
      { label: 'CPU', value: 'cpu' },
      { label: '内存', value: 'memory' },
      { label: '网络', value: 'network' },
      { label: '磁盘', value: 'disk' },
      { label: '进程', value: 'process' },
      { label: 'K8s', value: 'k8s' }
    ]
  },
  {
    prop: 'category',
    label: '分类',
    type: 'select',
    placeholder: '选择分类',
    options: [
      { label: '系统故障', value: '系统故障' },
      { label: '网络故障', value: '网络故障' },
      { label: '应用故障', value: '应用故障' },
      { label: '容器故障', value: '容器故障' }

    ]
  },
  {
    prop: 'is_builtin',
    label: '类型',
    type: 'select' as const,
    placeholder: '选择类型',
    options: [
      { label: '内置', value: 'true' },
      { label: '自定义', value: 'false' }
    ]
  }
])

const handleSearch = () => {
  emit('search')
}

const handleReset = () => {
  emit('reset')
}
</script>
