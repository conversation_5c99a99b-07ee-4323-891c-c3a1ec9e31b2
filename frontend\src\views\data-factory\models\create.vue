<template>
  <div class="model-create-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="handleBack" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div class="title-section">
          <h2 class="page-title">创建数据模型</h2>
          <p class="page-description">定义测试数据的结构和生成规则</p>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="handlePreview" :disabled="!canPreview">
          <el-icon><View /></el-icon>
          预览数据
        </el-button>
        <el-button type="primary" :loading="saving" @click="handleSave">
          <el-icon><Check /></el-icon>
          保存模型
        </el-button>
      </div>
    </div>

    <!-- 步骤指示器 -->
    <div class="steps-section">
      <el-steps :active="currentStep" align-center>
        <el-step title="基本信息" description="设置模型基本信息" />
        <el-step title="字段配置" description="配置数据字段和生成规则" />
        <el-step title="预览确认" description="预览数据并确认保存" />
      </el-steps>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <el-card class="form-card">
        <!-- 步骤1：基本信息 -->
        <div v-show="currentStep === 0" class="step-content">
          <h3 class="step-title">模型基本信息</h3>
          <el-form
            ref="basicFormRef"
            :model="modelForm"
            :rules="basicRules"
            label-width="120px"
            class="basic-form"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="模型名称" prop="name" required>
                  <el-input
                    v-model="modelForm.name"
                    placeholder="请输入模型名称"
                    clearable
                    maxlength="100"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="版本号" prop="version">
                  <el-input
                    v-model="modelForm.version"
                    placeholder="版本号（默认：1.0.0）"
                    clearable
                    maxlength="20"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="模型分类" prop="category">
                  <el-select
                    v-model="modelForm.category"
                    placeholder="选择或输入分类"
                    filterable
                    allow-create
                    clearable
                    style="width: 100%"
                  >
                    <el-option
                      v-for="category in categories"
                      :key="category"
                      :label="category"
                      :value="category"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-radio-group v-model="modelForm.status">
                    <el-radio label="1">启用</el-radio>
                    <el-radio label="2">禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="模型描述" prop="description">
              <el-input
                v-model="modelForm.description"
                type="textarea"
                :rows="4"
                placeholder="请输入模型描述"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="标签" prop="tags">
              <el-select
                v-model="modelForm.tags"
                multiple
                filterable
                allow-create
                placeholder="添加标签"
                style="width: 100%"
              >
                <el-option
                  v-for="tag in commonTags"
                  :key="tag"
                  :label="tag"
                  :value="tag"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>

        <!-- 步骤2：字段配置 -->
        <div v-show="currentStep === 1" class="step-content">
          <div class="step-header">
            <h3 class="step-title">字段配置</h3>
            <el-button type="primary" @click="handleAddField">
              <el-icon><Plus /></el-icon>
              添加字段
            </el-button>
          </div>

          <div class="fields-section">
            <div v-if="modelForm.fields_config.length === 0" class="empty-fields">
              <el-empty description="暂无字段配置">
                <el-button type="primary" @click="handleAddField">添加第一个字段</el-button>
              </el-empty>
            </div>

            <div v-else class="fields-list">
              <div
                v-for="(field, index) in modelForm.fields_config"
                :key="index"
                class="field-item"
              >
                <FieldConfigCard
                  :field="field"
                  :index="index"
                  @update="handleUpdateField"
                  @delete="handleDeleteField"
                  @move-up="handleMoveFieldUp"
                  @move-down="handleMoveFieldDown"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤3：预览确认 -->
        <div v-show="currentStep === 2" class="step-content">
          <h3 class="step-title">预览确认</h3>
          
          <!-- 模型信息预览 -->
          <div class="model-preview">
            <el-descriptions title="模型信息" :column="2" border>
              <el-descriptions-item label="模型名称">{{ modelForm.name }}</el-descriptions-item>
              <el-descriptions-item label="版本号">{{ modelForm.version }}</el-descriptions-item>
              <el-descriptions-item label="分类">{{ modelForm.category || '-' }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="modelForm.status === '1' ? 'success' : 'danger'">
                  {{ modelForm.status === '1' ? '启用' : '禁用' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="字段数量">{{ modelForm.fields_config.length }}</el-descriptions-item>
              <el-descriptions-item label="标签">
                <el-tag v-for="tag in modelForm.tags" :key="tag" size="small" class="tag-item">
                  {{ tag }}
                </el-tag>
                <span v-if="!modelForm.tags?.length">-</span>
              </el-descriptions-item>
              <el-descriptions-item label="描述" :span="2">
                {{ modelForm.description || '-' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 字段配置预览 -->
          <div class="fields-preview">
            <h4>字段配置</h4>
            <el-table :data="modelForm.fields_config" border style="width: 100%">
              <el-table-column prop="name" label="字段名称" width="150" />
              <el-table-column prop="type" label="数据类型" width="120">
                <template #default="{ row }">
                  <el-tag size="small" type="info">{{ row.type }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="generator" label="生成器" width="120">
                <template #default="{ row }">
                  <el-tag size="small" type="success">{{ row.generator }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="required" label="必填" width="80">
                <template #default="{ row }">
                  <el-tag :type="row.required ? 'success' : 'info'" size="small">
                    {{ row.required ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
            </el-table>
          </div>

          <!-- 数据预览 -->
          <div v-if="previewData.length > 0" class="data-preview">
            <h4>数据预览</h4>
            <el-table :data="previewData" border style="width: 100%" max-height="300">
              <el-table-column
                v-for="field in modelForm.fields_config"
                :key="field.name"
                :prop="field.name"
                :label="field.name"
                min-width="120"
                show-overflow-tooltip
              />
            </el-table>
          </div>
        </div>

        <!-- 步骤导航 -->
        <div class="step-navigation">
          <el-button v-if="currentStep > 0" @click="handlePrevStep">上一步</el-button>
          <el-button v-if="currentStep < 2" type="primary" @click="handleNextStep">下一步</el-button>
        </div>
      </el-card>
    </div>

    <!-- 字段配置对话框 -->
    <FieldConfigDialog
      v-model:visible="fieldDialog.visible"
      :field="fieldDialog.field"
      :mode="fieldDialog.mode"
      @confirm="handleConfirmField"
    />

    <!-- 数据预览对话框 -->
    <DataPreviewDialog
      v-model:visible="previewDialog.visible"
      :model-info="previewModelInfo"
      :preview-data="previewData"
      :loading="previewLoading"
      @refresh="handleRefreshPreview"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { ArrowLeft, View, Check, Plus } from '@element-plus/icons-vue'
import { DataFactoryService } from '@/api/dataFactoryApi'
import FieldConfigCard from './components/FieldConfigCard.vue'
import FieldConfigDialog from './components/FieldConfigDialog.vue'
import DataPreviewDialog from './components/DataPreviewDialog.vue'

const router = useRouter()

// 响应式数据
const currentStep = ref(0)
const saving = ref(false)
const basicFormRef = ref<FormInstance>()
const categories = ref<string[]>([])
const commonTags = ref<string[]>(['测试数据', '用户数据', '订单数据', '商品数据', '日志数据'])
const previewData = ref<any[]>([])
const previewLoading = ref(false)

// 表单数据
const modelForm = reactive<Api.DataFactory.ModelForm>({
  name: '',
  description: '',
  version: '1.0.0',
  category: '',
  tags: [],
  fields_config: [],
  status: '1'
})

// 字段配置对话框
const fieldDialog = reactive({
  visible: false,
  field: null as Api.DataFactory.FieldConfig | null,
  mode: 'create' as 'create' | 'edit',
  index: -1
})

// 预览对话框
const previewDialog = reactive({
  visible: false
})

// 表单验证规则
const basicRules: FormRules = {
  name: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    { min: 1, max: 100, message: '名称长度应在 1 到 100 个字符', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/,
      message: '名称只能包含字母、数字、下划线和中文字符',
      trigger: 'blur'
    }
  ],
  version: [
    {
      pattern: /^\d+\.\d+\.\d+$/,
      message: '版本号格式应为 x.y.z（如：1.0.0）',
      trigger: 'blur'
    }
  ],
  description: [
    { max: 1000, message: '描述长度不能超过 1000 个字符', trigger: 'blur' }
  ],
  category: [
    { max: 50, message: '分类长度不能超过 50 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const canPreview = computed(() => {
  return modelForm.name && modelForm.fields_config.length > 0
})

const previewModelInfo = computed(() => {
  if (!canPreview.value) return null
  
  return {
    id: 0,
    name: modelForm.name,
    description: modelForm.description,
    version: modelForm.version,
    category: modelForm.category,
    tags: modelForm.tags,
    fields_config: modelForm.fields_config,
    usage_count: 0,
    status: modelForm.status,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    created_by: '',
    updated_by: ''
  } as Api.DataFactory.ModelInfo
})

// 页面初始化
onMounted(() => {
  loadCategories()
})

// 加载分类
const loadCategories = async () => {
  try {
    const response = await DataFactoryService.getModelCategories()
    if (response.success) {
      categories.value = response.data
    }
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

// 事件处理
const handleBack = () => {
  router.back()
}

const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const handleNextStep = async () => {
  if (currentStep.value === 0) {
    // 验证基本信息
    if (!basicFormRef.value) return
    try {
      await basicFormRef.value.validate()
      currentStep.value++
    } catch (error) {
      ElMessage.error('请检查基本信息填写')
    }
  } else if (currentStep.value === 1) {
    // 验证字段配置
    if (modelForm.fields_config.length === 0) {
      ElMessage.error('请至少添加一个字段')
      return
    }
    
    // 生成预览数据
    await generatePreviewData()
    currentStep.value++
  }
}

const handleAddField = () => {
  fieldDialog.field = null
  fieldDialog.mode = 'create'
  fieldDialog.index = -1
  fieldDialog.visible = true
}

const handleUpdateField = (index: number, field: Api.DataFactory.FieldConfig) => {
  fieldDialog.field = { ...field }
  fieldDialog.mode = 'edit'
  fieldDialog.index = index
  fieldDialog.visible = true
}

const handleDeleteField = (index: number) => {
  modelForm.fields_config.splice(index, 1)
}

const handleMoveFieldUp = (index: number) => {
  if (index > 0) {
    const field = modelForm.fields_config.splice(index, 1)[0]
    modelForm.fields_config.splice(index - 1, 0, field)
  }
}

const handleMoveFieldDown = (index: number) => {
  if (index < modelForm.fields_config.length - 1) {
    const field = modelForm.fields_config.splice(index, 1)[0]
    modelForm.fields_config.splice(index + 1, 0, field)
  }
}

const handleConfirmField = (field: Api.DataFactory.FieldConfig) => {
  if (fieldDialog.mode === 'create') {
    modelForm.fields_config.push(field)
  } else {
    modelForm.fields_config[fieldDialog.index] = field
  }
  fieldDialog.visible = false
}

const handlePreview = () => {
  if (!canPreview.value) return
  
  previewDialog.visible = true
  generatePreviewData()
}

const handleRefreshPreview = () => {
  generatePreviewData()
}

const generatePreviewData = async () => {
  if (!canPreview.value) return
  
  try {
    previewLoading.value = true
    // 这里应该调用后端API生成预览数据
    // 暂时使用模拟数据
    const mockData = []
    for (let i = 0; i < 5; i++) {
      const record: any = {}
      modelForm.fields_config.forEach(field => {
        // 简单的模拟数据生成
        switch (field.generator) {
          case 'uuid':
            record[field.name] = `uuid-${Math.random().toString(36).substr(2, 9)}`
            break
          case 'name':
            record[field.name] = `用户${i + 1}`
            break
          case 'phone':
            record[field.name] = `138${Math.random().toString().substr(2, 8)}`
            break
          case 'email':
            record[field.name] = `user${i + 1}@example.com`
            break
          case 'range':
            record[field.name] = Math.floor(Math.random() * 100) + 1
            break
          default:
            record[field.name] = `${field.generator}_${i + 1}`
        }
      })
      mockData.push(record)
    }
    previewData.value = mockData
  } catch (error) {
    ElMessage.error('生成预览数据失败')
  } finally {
    previewLoading.value = false
  }
}

const handleSave = async () => {
  try {
    // 最终验证
    if (!basicFormRef.value) return
    await basicFormRef.value.validate()
    
    if (modelForm.fields_config.length === 0) {
      ElMessage.error('请至少添加一个字段')
      return
    }
    
    saving.value = true
    
    await DataFactoryService.createDataModel(modelForm)
    ElMessage.success('数据模型创建成功')
    router.push('/data-factory/models')
  } catch (error) {
    ElMessage.error('创建失败，请检查输入信息')
  } finally {
    saving.value = false
  }
}
</script>

<style scoped lang="scss">
.model-create-page {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .back-button {
        padding: 8px 12px;
      }

      .title-section {
        .page-title {
          margin: 0 0 4px 0;
          font-size: 24px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .page-description {
          margin: 0;
          color: var(--el-text-color-regular);
          font-size: 14px;
        }
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .steps-section {
    margin-bottom: 30px;
  }

  .form-content {
    .form-card {
      min-height: 600px;

      .step-content {
        .step-title {
          margin: 0 0 20px 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .step-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;

          .step-title {
            margin: 0;
          }
        }

        .basic-form {
          max-width: 800px;
        }

        .fields-section {
          .empty-fields {
            text-align: center;
            padding: 40px 0;
          }

          .fields-list {
            .field-item {
              margin-bottom: 16px;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }

        .model-preview {
          margin-bottom: 30px;
        }

        .fields-preview {
          margin-bottom: 30px;

          h4 {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
          }
        }

        .data-preview {
          h4 {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
          }
        }

        .tag-item {
          margin-right: 8px;
        }
      }

      .step-navigation {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid var(--el-border-color-light);
        display: flex;
        justify-content: center;
        gap: 12px;
      }
    }
  }
}
</style>
