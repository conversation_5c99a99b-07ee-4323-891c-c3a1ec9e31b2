"""
FastAPI 应用主入口 - 清理版本
"""
import asyncio
import traceback
from contextlib import asynccontextmanager
from pathlib import Path

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from app.schedule.tasks import init_business_tasks
from app.api.router import router_manager
from app.core.config import settings
from app.database import init_db, close_db
from app.middleware.request_logging import UnifiedRequestMiddleware
from app.core.responses import response_builder
from app.core.exceptions import BusinessException
from app.utils.logger import setup_logger

# 设置日志
logger = setup_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 应用启动中...")
    
    # 1. 初始化数据库
    await init_db()
    logger.info("✅ 数据库初始化完成")
    
    # 2. 启动调度管理器
    logger.info("📅 开始启动调度管理器...")
    try:
        from app.schedule.schedule_manager import start_schedule_manager
        await start_schedule_manager()
        logger.info("✅ 调度管理器启动成功")
        
        # 等待调度器完全初始化
        await asyncio.sleep(1)
        
    except Exception as e:
        logger.error(f"❌ 启动调度管理器失败: {e}")
        traceback.print_exc()
    
    # 3. 初始化业务调度任务（这会触发装饰器执行）
    try:
        await init_business_tasks()
        logger.info("✅ 业务调度任务初始化成功")
    except Exception as e:
        logger.error(f"❌ 初始化业务调度任务失败: {e}")
        traceback.print_exc()

    # 4. 注册装饰器定义的定时任务（在模块导入之后）
    try:
        from app.schedule.decorators import register_pending_tasks
        await register_pending_tasks()
        logger.info("✅ 装饰器定时任务注册成功")
    except Exception as e:
        logger.error(f"❌ 注册装饰器定时任务失败: {e}")
        traceback.print_exc()
    
    # 5. 验证调度器状态
    try:
        from app.schedule.schedule_manager import is_running, get_jobs
        if is_running():
            jobs = get_jobs()
            logger.info(f"✅ 调度器运行正常，当前有 {len(jobs)} 个作业")
            
            # 显示作业详情
            for job in jobs:
                logger.info(f"   📋 作业: {job.id}, 下次执行: {job.next_run_time}")
        else:
            logger.warning("⚠️ 调度器未运行")
    except Exception as e:
        logger.error(f"❌ 检查调度器状态失败: {e}")
        traceback.print_exc()
    
    logger.info("🎉 应用启动完成")
    
    yield  # 应用运行期间
    
    # 关闭时执行
    logger.info("🔄 应用关闭中...")
    try:
        from app.schedule.schedule_manager import shutdown_schedule_manager
        await shutdown_schedule_manager()
        logger.info("✅ 调度管理器已停止")
    except Exception as e:
        logger.error(f"❌ 停止调度管理器失败: {e}")
    
    await close_db()
    logger.info("👋 应用已关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="DpTestPlatform Backend API - 基于 FastAPI + SQLAlchemy + Pydantic V2",
    lifespan=lifespan,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=settings.cors_methods,
    allow_headers=settings.cors_headers
)

# 添加统一请求监控中间件（性能优化版本）
app.add_middleware(
    UnifiedRequestMiddleware,
    log_slow_requests=True,
    slow_threshold=1.0,  # 1秒以上的请求记录警告
    log_threshold=0.2    # 200ms以上的请求才记录日志，减少日志量
)


# 注册所有API路由
app.include_router(router_manager.get_router())

# 静态文件服务
project_root = Path(__file__).parent.parent
uploads_dir = project_root / "uploads"
uploads_dir.mkdir(exist_ok=True)
app.mount("/uploads", StaticFiles(directory=str(uploads_dir)), name="uploads")


# 业务异常全局处理器
@app.exception_handler(BusinessException)
async def business_exception_handler(request: Request, exc: BusinessException):
    """业务异常全局处理器"""
    logger.warning(f"业务异常: {exc.error_code.name}({exc.error_code.value}) - {exc.message}")
    error_response = response_builder.error(
        error_code=exc.error_code,
        message=exc.message,
        details=exc.details
    )
    return JSONResponse(
        status_code=exc.http_code,
        content=error_response.model_dump()
    )


# HTTP异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    if isinstance(exc.detail, dict):
        return JSONResponse(
            status_code=exc.status_code,
            content=exc.detail
        )
    
    from app.core.error_codes import ErrorCode
    error_response = response_builder.error(
        error_code=ErrorCode.INTERNAL_SERVER_ERROR if exc.status_code >= 500 else ErrorCode.INVALID_REQUEST_PARAMS,
        message=str(exc.detail)
    )
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.model_dump()
    )


# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
    
    from app.core.error_codes import ErrorCode
    if settings.debug:
        error_response = response_builder.error(
            error_code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=f"服务器内部错误: {str(exc)}",
            details={"type": type(exc).__name__, "traceback": str(exc)}
        )
    else:
        error_response = response_builder.error(
            error_code=ErrorCode.INTERNAL_SERVER_ERROR
        )
    
    return JSONResponse(
        status_code=500,
        content=error_response.model_dump()
    )


if __name__ == "__main__":
    import uvicorn
    
    logger.info(f"🚀 启动 {settings.app_name} v{settings.app_version}")
    logger.info(f"📍 服务地址: http://{settings.host}:{settings.port}")
    logger.info(f"📚 API文档: http://{settings.host}:{settings.port}/docs")
    
    uvicorn.run(
        app,
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
