<template>
  <div class="art-full-height">
    <ElCard class="art-table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">创建场景模板</span>
          <div class="header-actions">
            <ElButton @click="handleCancel">取消</ElButton>
            <ElButton type="primary" @click="handleSubmit" :loading="submitting">
              创建场景
            </ElButton>
          </div>
        </div>
      </template>

      <ElForm
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="scenario-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">基本信息</h3>
          
          <ElFormItem label="场景名称" prop="name" required>
            <ElInput
              v-model="formData.name"
              placeholder="请输入场景名称"
              maxlength="100"
              show-word-limit
            />
          </ElFormItem>

          <ElFormItem label="故障类型" required>
            <ElCascader
              v-model="cascaderValue"
              :options="cascaderOptions"
              :props="cascaderProps"
              placeholder="请选择故障分类和类型"
              style="width: 100%"
              clearable
            />
          </ElFormItem>

          <!-- 隐藏字段用于验证 -->
          <ElFormItem prop="fault_type" style="display: none;">
            <ElInput v-model="formData.fault_type" />
          </ElFormItem>

          <ElFormItem prop="category" style="display: none;">
            <ElInput v-model="formData.category" />
          </ElFormItem>

          <ElFormItem label="场景描述" prop="description">
            <ElInput
              v-model="formData.description"
              type="textarea"
              :rows="3"
              placeholder="请输入场景描述"
              maxlength="1000"
              show-word-limit
            />
          </ElFormItem>

          <ElFormItem label="标签" prop="tags">
            <ElInput
              v-model="formData.tags"
              placeholder="请输入标签，多个标签用逗号分隔"
              maxlength="500"
            />
          </ElFormItem>
        </div>

        <!-- 参数配置 -->
        <div class="form-section">
          <h3 class="section-title">参数配置</h3>

          <ElFormItem label="默认参数" prop="default_params" required>
            <div class="json-editor">
              <div class="json-toolbar">
                <ElButton size="small" @click="formatJson">格式化JSON</ElButton>
                <ElButton size="small" @click="showJsonExample">示例模板</ElButton>
                <ElButton size="small" @click="clearJson">清空</ElButton>
              </div>
              <ElInput
                v-model="defaultParamsText"
                type="textarea"
                :rows="10"
                placeholder="请输入JSON格式的默认参数，例如：&#10;{&#10;  &quot;duration&quot;: &quot;30s&quot;,&#10;  &quot;cpu-percent&quot;: 80,&#10;  &quot;timeout&quot;: &quot;60s&quot;&#10;}"
                @blur="validateJson('default_params')"
                @input="onJsonInput"
              />
              <div v-if="jsonErrors.default_params" class="json-error">
                <i class="el-icon-warning"></i>
                {{ jsonErrors.default_params }}
              </div>
              <div v-else-if="jsonValid" class="json-success">
                <i class="el-icon-success"></i>
                JSON格式正确
              </div>
            </div>
          </ElFormItem>
        </div>
      </ElForm>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElCard, ElForm, ElFormItem, ElInput, ElCascader, ElButton } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { CHAOS_CATEGORIES } from '@/constants/chaos'
import { useChaosScenariosStore } from '@/store/business/chaos/scenarios'
import type { ChaosScenarioCreate } from '@/types/api/chaos'

defineOptions({ name: 'ChaosScenarioCreate' })

const router = useRouter()
const chaosScenariosStore = useChaosScenariosStore()

// 表单引用
const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const formData = reactive<ChaosScenarioCreate>({
  name: '',
  fault_type: '',
  description: '',
  default_params: {},
  param_schema: {}, // 设置为空对象，后端需要
  category: '',
  tags: ''
})

// JSON文本
const defaultParamsText = ref('{\n  "duration": "30s",\n  "timeout": "60s"\n}')

// JSON验证状态
const jsonErrors = reactive({
  default_params: ''
})

const jsonValid = ref(false)

// 获取故障类型标签
const getFaultTypeLabel = (faultType: string) => {
  const labels: Record<string, string> = {
    cpu: 'CPU故障',
    memory: '内存故障',
    disk: '磁盘故障',
    network: '网络故障',
    jvm: 'JVM故障',
    process: '进程故障',
    docker: 'Docker故障',
    kubernetes: 'Kubernetes故障'
  }
  return labels[faultType] || faultType
}

// 级联选择器配置
const cascaderProps = {
  value: 'value',
  label: 'label',
  children: 'children',
  emitPath: true // 返回完整路径
}

// 级联选择器选项
const cascaderOptions = CHAOS_CATEGORIES.map(category => ({
  value: category.key,
  label: category.label,
  children: category.faultTypes.map(faultType => ({
    value: faultType,
    label: getFaultTypeLabel(faultType)
  }))
}))

// 级联选择器的值
const cascaderValue = ref<string[]>([])

// 监听级联选择器变化
watch(cascaderValue, (newValue) => {
  if (newValue && newValue.length === 2) {
    formData.category = newValue[0]
    formData.fault_type = newValue[1]
  } else {
    formData.category = ''
    formData.fault_type = ''
  }
}, { immediate: true })

// 监听表单数据变化，同步到级联选择器
watch([() => formData.category, () => formData.fault_type], ([category, faultType]) => {
  if (category && faultType) {
    cascaderValue.value = [category, faultType]
  } else {
    cascaderValue.value = []
  }
}, { immediate: true })

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入场景名称', trigger: 'blur' },
    { min: 1, max: 100, message: '场景名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  fault_type: [
    { required: true, message: '请选择故障类型', trigger: 'change' }
  ],
  description: [
    { max: 1000, message: '描述长度不能超过 1000 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择场景分类', trigger: 'change' }
  ],
  tags: [
    { max: 500, message: '标签长度不能超过 500 个字符', trigger: 'blur' }
  ],
  default_params: [
    { required: true, message: '请输入默认参数', trigger: 'blur' },
    { validator: validateDefaultParams, trigger: 'blur' }
  ]
}

// JSON验证函数
function validateJson(field: 'default_params') {
  const text = defaultParamsText.value

  try {
    const parsed = JSON.parse(text)
    formData[field] = parsed
    formData.param_schema = {} // 设置为空对象
    jsonErrors[field] = ''
    jsonValid.value = true
    return true
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'JSON格式错误'
    jsonErrors[field] = `JSON格式错误: ${errorMessage}`
    jsonValid.value = false
    return false
  }
}

// 默认参数验证器
function validateDefaultParams(_rule: any, _value: any, callback: any) {
  if (!validateJson('default_params')) {
    callback(new Error('默认参数JSON格式错误'))
  } else {
    callback()
  }
}

// JSON输入监听
function onJsonInput() {
  // 延迟验证，避免频繁验证
  setTimeout(() => {
    validateJson('default_params')
  }, 500)
}

// 格式化JSON
function formatJson() {
  try {
    const parsed = JSON.parse(defaultParamsText.value)
    defaultParamsText.value = JSON.stringify(parsed, null, 2)
    validateJson('default_params')
    ElMessage.success('JSON格式化成功')
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化')
  }
}

// 显示JSON示例
function showJsonExample() {
  const examples = {
    'cpu': {
      "cpu-percent": 80,
      "duration": "30s",
      "timeout": "60s"
    },
    'memory': {
      "mem-percent": 70,
      "duration": "30s",
      "timeout": "60s"
    },
    'network': {
      "destination-ip": "*************",
      "interface": "eth0",
      "percent": 100,
      "duration": "30s",
      "timeout": "60s"
    },
    'disk': {
      "path": "/tmp",
      "percent": 80,
      "duration": "30s",
      "timeout": "60s"
    },
    'process': {
      "process": "nginx",
      "signal": "SIGKILL",
      "duration": "30s",
      "timeout": "60s"
    },
    'k8s': {
      "namespace": "default",
      "deployment": "nginx",
      "duration": "30s",
      "timeout": "60s"
    },
    'docker': {
      "container": "nginx",
      "action": "stop",
      "duration": "30s",
      "timeout": "60s"
    }
  }

  const faultType = formData.fault_type
  const example = examples[faultType as keyof typeof examples] || examples['cpu']
  defaultParamsText.value = JSON.stringify(example, null, 2)
  validateJson('default_params')
  ElMessage.success('已加载示例模板')
}

// 清空JSON
function clearJson() {
  defaultParamsText.value = '{}'
  validateJson('default_params')
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    console.log('提交前的表单数据:', formData)
    console.log('级联选择器的值:', cascaderValue.value)

    // 确保数据类型正确
    if (Array.isArray(formData.category)) {
      formData.category = formData.category[0] || ''
    }
    if (Array.isArray(formData.fault_type)) {
      formData.fault_type = formData.fault_type[0] || ''
    }

    console.log('处理后的表单数据:', formData)

    await formRef.value.validate()

    // 验证JSON格式
    if (!validateJson('default_params')) {
      ElMessage.error('请检查JSON格式')
      return
    }

    submitting.value = true

    await chaosScenariosStore.createScenario(formData)

    ElMessage.success('场景创建成功')
    router.push('/chaos/scenarios')
  } catch (error) {
    console.error('创建场景失败:', error)
    ElMessage.error('创建场景失败')
  } finally {
    submitting.value = false
  }
}

// 取消操作
const handleCancel = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消创建吗？未保存的数据将丢失。',
      '确认取消',
      { type: 'warning' }
    )
    router.push('/chaos/scenarios')
  } catch (error) {
    // 用户取消
  }
}

// 初始化
onMounted(() => {
  // 初始验证JSON
  validateJson('default_params')
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: rgb(var(--art-text-primary));
}

.header-actions {
  display: flex;
  gap: 12px;
}

.scenario-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: 1600;
  color: rgb(var(--art-text-primary));
  margin: 0 0 20px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid rgb(var(--art-border-light));
}

.json-editor {
  position: relative;
}

.json-toolbar {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  padding: 8px;
  background: rgb(var(--art-main-bg-color));
  border: 1px solid rgb(var(--art-border-light));
  border-radius: 4px 4px 0 0;
}

.json-error {
  color: rgb(var(--art-danger));
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.json-success {
  color: rgb(var(--art-success));
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

:deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}
</style>
