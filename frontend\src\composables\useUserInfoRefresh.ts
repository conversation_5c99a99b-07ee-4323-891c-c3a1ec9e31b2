/**
 * 用户信息刷新策略组合式函数
 * 提供智能的用户信息刷新机制
 */

import { ref, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { userInfoManager } from '@/utils/userInfoManager'

interface RefreshOptions {
  enableAutoRefresh?: boolean
  refreshInterval?: number
  enableVisibilityRefresh?: boolean
  enableFocusRefresh?: boolean
  enableStorageRefresh?: boolean
}

export function useUserInfoRefresh(options: RefreshOptions = {}) {
  const {
    enableAutoRefresh = false,
    refreshInterval = 30 * 60 * 1000, // 30分钟
    enableVisibilityRefresh = true,
    enableFocusRefresh = true,
    enableStorageRefresh = true
  } = options

  const userStore = useUserStore()
  const isRefreshing = ref(false)
  const lastRefreshTime = ref(0)
  
  let refreshTimer: NodeJS.Timeout | null = null
  let visibilityHandler: (() => void) | null = null
  let focusHandler: (() => void) | null = null
  let storageHandler: ((e: StorageEvent) => void) | null = null

  /**
   * 执行用户信息刷新
   */
  const refreshUserInfo = async (force = false) => {
    if (isRefreshing.value || !userStore.isLogin) return

    try {
      isRefreshing.value = true
      
      const userData = await userInfoManager.getUserInfo({
        forceRefresh: force,
        fallbackToCache: !force
      })
      
      if (userData) {
        userStore.setUserInfo(userData)
        lastRefreshTime.value = Date.now()
      }
    } catch (error) {
      console.error('刷新用户信息失败:', error)
    } finally {
      isRefreshing.value = false
    }
  }

  /**
   * 检查是否需要刷新
   */
  const shouldRefresh = () => {
    if (!userStore.isLogin) return false
    
    const now = Date.now()
    const timeSinceLastRefresh = now - lastRefreshTime.value
    
    return timeSinceLastRefresh > refreshInterval || userInfoManager.shouldRefresh()
  }

  /**
   * 页面可见性变化处理
   */
  const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible' && shouldRefresh()) {
      refreshUserInfo()
    }
  }

  /**
   * 窗口焦点变化处理
   */
  const handleWindowFocus = () => {
    if (shouldRefresh()) {
      refreshUserInfo()
    }
  }

  /**
   * 本地存储变化处理（多标签页同步）
   */
  const handleStorageChange = (e: StorageEvent) => {
    if (e.key === 'user_info_cache' && e.newValue) {
      try {
        const newData = JSON.parse(e.newValue)
        userStore.setUserInfo(newData.data)
        lastRefreshTime.value = newData.timestamp
      } catch (error) {
        console.warn('同步用户信息失败:', error)
      }
    }
  }

  /**
   * 启动自动刷新
   */
  const startAutoRefresh = () => {
    if (refreshTimer) return

    refreshTimer = setInterval(() => {
      if (shouldRefresh()) {
        refreshUserInfo()
      }
    }, Math.min(refreshInterval, 5 * 60 * 1000)) // 最多5分钟检查一次
  }

  /**
   * 停止自动刷新
   */
  const stopAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }

  /**
   * 初始化刷新策略
   */
  const initRefreshStrategy = () => {
    // 页面可见性监听
    if (enableVisibilityRefresh) {
      visibilityHandler = handleVisibilityChange
      document.addEventListener('visibilitychange', visibilityHandler)
    }

    // 窗口焦点监听
    if (enableFocusRefresh) {
      focusHandler = handleWindowFocus
      window.addEventListener('focus', focusHandler)
    }

    // 本地存储监听（多标签页同步）
    if (enableStorageRefresh) {
      storageHandler = handleStorageChange
      window.addEventListener('storage', storageHandler)
    }

    // 自动刷新
    if (enableAutoRefresh) {
      startAutoRefresh()
    }
  }

  /**
   * 清理刷新策略
   */
  const cleanupRefreshStrategy = () => {
    stopAutoRefresh()

    if (visibilityHandler) {
      document.removeEventListener('visibilitychange', visibilityHandler)
      visibilityHandler = null
    }

    if (focusHandler) {
      window.removeEventListener('focus', focusHandler)
      focusHandler = null
    }

    if (storageHandler) {
      window.removeEventListener('storage', storageHandler)
      storageHandler = null
    }
  }

  /**
   * 获取刷新状态
   */
  const getRefreshStatus = () => {
    const cacheStatus = userInfoManager.getCacheStatus()
    
    return {
      isRefreshing: isRefreshing.value,
      lastRefreshTime: lastRefreshTime.value,
      shouldRefresh: shouldRefresh(),
      cacheStatus
    }
  }

  // 生命周期钩子
  onMounted(() => {
    initRefreshStrategy()
    
    // 初始检查
    if (userStore.isLogin && shouldRefresh()) {
      refreshUserInfo()
    }
  })

  onUnmounted(() => {
    cleanupRefreshStrategy()
  })

  return {
    isRefreshing,
    lastRefreshTime,
    refreshUserInfo,
    shouldRefresh,
    startAutoRefresh,
    stopAutoRefresh,
    getRefreshStatus
  }
}
