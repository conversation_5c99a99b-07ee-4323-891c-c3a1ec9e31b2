"""
定时任务执行历史服务
"""
import json
import logging
import traceback
from datetime import datetime
from typing import Optional, Dict, Any, List

from sqlalchemy import select, and_, desc
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.schedule.schedule_task_history import ScheduleTaskHistory
from app.repositories.base import BaseRepository

logger = logging.getLogger(__name__)


class ScheduleHistoryRepository(BaseRepository[ScheduleTaskHistory, dict, dict]):
    """定时任务执行历史仓库"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(ScheduleTaskHistory, db)


class ScheduleHistoryService:
    """定时任务执行历史服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.repository = ScheduleHistoryRepository(db)

    def _get_current_time(self) -> datetime:
        """获取当前应用时区时间"""
        from app.utils.timezone import now
        return now()
    
    async def create_execution_record(
        self,
        task_id: str,
        task_name: str,
        task_type: str,
        function_path: str,
        function_args: Optional[List] = None,
        function_kwargs: Optional[Dict[str, Any]] = None,
        trigger_type: str = "unknown",
        scheduled_time: Optional[datetime] = None
    ) -> ScheduleTaskHistory:
        """创建执行记录"""
        try:
            history = ScheduleTaskHistory(
                task_id=task_id,
                task_name=task_name,
                task_type=task_type,
                function_path=function_path,
                function_args=function_args,
                function_kwargs=function_kwargs,
                trigger_type=trigger_type,
                scheduled_time=scheduled_time or self._get_current_time(),
                status="running"
            )
            
            self.db.add(history)
            await self.db.commit()
            await self.db.refresh(history)
            
            logger.info(f"创建执行历史记录: {task_id} (ID: {history.id})")
            return history
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建执行历史记录失败: {task_id}, 错误: {str(e)}")
            raise
    
    async def mark_execution_started(
        self,
        history_id: int,
        start_time: Optional[datetime] = None
    ) -> None:
        """标记执行开始"""
        try:
            history = await self.repository.get(history_id)
            if history:
                history.mark_started(start_time)
                await self.db.commit()
                logger.debug(f"标记执行开始: {history.task_id}")
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"标记执行开始失败: {history_id}, 错误: {str(e)}")
    
    async def mark_execution_completed(
        self,
        history_id: int,
        status: str,
        end_time: Optional[datetime] = None,
        result: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
        exception_obj: Optional[Exception] = None
    ) -> None:
        """标记执行完成"""
        try:
            history = await self.repository.get(history_id)
            if history:
                exception_traceback = None
                if exception_obj:
                    exception_traceback = traceback.format_exception(
                        type(exception_obj), exception_obj, exception_obj.__traceback__
                    )
                    exception_traceback = ''.join(exception_traceback)
                
                history.mark_completed(
                    status=status,
                    end_time=end_time,
                    result=result,
                    error_message=error_message,
                    exception_traceback=exception_traceback
                )
                await self.db.commit()
                logger.info(f"标记执行完成: {history.task_id}, 状态: {status}")
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"标记执行完成失败: {history_id}, 错误: {str(e)}")
    
    async def get_task_history(
        self,
        task_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[ScheduleTaskHistory]:
        """获取任务执行历史"""
        try:
            query = select(ScheduleTaskHistory).where(
                ScheduleTaskHistory.task_id == task_id
            ).order_by(desc(ScheduleTaskHistory.created_at)).offset(offset).limit(limit)
            
            result = await self.db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"获取任务执行历史失败: {task_id}, 错误: {str(e)}")
            return []
    
    async def get_recent_executions(
        self,
        limit: int = 100,
        status: Optional[str] = None
    ) -> List[ScheduleTaskHistory]:
        """获取最近的执行记录"""
        try:
            query = select(ScheduleTaskHistory)
            
            if status:
                query = query.where(ScheduleTaskHistory.status == status)
            
            query = query.order_by(desc(ScheduleTaskHistory.created_at)).limit(limit)
            
            result = await self.db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"获取最近执行记录失败, 错误: {str(e)}")
            return []
    
    async def get_execution_statistics(
        self,
        task_id: Optional[str] = None,
        days: int = 7
    ) -> Dict[str, Any]:
        """获取执行统计信息"""
        try:
            from datetime import timedelta
            
            start_date = datetime.now() - timedelta(days=days)
            
            query = select(ScheduleTaskHistory).where(
                ScheduleTaskHistory.created_at >= start_date
            )
            
            if task_id:
                query = query.where(ScheduleTaskHistory.task_id == task_id)
            
            result = await self.db.execute(query)
            histories = result.scalars().all()
            
            # 统计信息
            total_count = len(histories)
            status_stats = {}
            success_count = 0
            total_duration = 0
            duration_count = 0
            
            for history in histories:
                # 状态统计
                status = history.status
                status_stats[status] = status_stats.get(status, 0) + 1
                
                # 成功率统计
                if history.is_successful:
                    success_count += 1
                
                # 执行时长统计
                if history.duration_seconds is not None:
                    total_duration += history.duration_seconds
                    duration_count += 1
            
            # 计算平均执行时长
            avg_duration = total_duration / duration_count if duration_count > 0 else 0
            
            # 计算成功率
            success_rate = success_count / total_count if total_count > 0 else 0
            
            return {
                'total_count': total_count,
                'status_stats': status_stats,
                'success_rate': round(success_rate * 100, 2),
                'avg_duration_seconds': round(avg_duration, 2),
                'period_days': days
            }
            
        except Exception as e:
            logger.error(f"获取执行统计信息失败, 错误: {str(e)}")
            return {
                'total_count': 0,
                'status_stats': {},
                'success_rate': 0,
                'avg_duration_seconds': 0,
                'period_days': days
            }
    
    async def cleanup_old_records(self, days: int = 30) -> int:
        """清理旧的执行记录"""
        try:
            from datetime import timedelta
            from sqlalchemy import delete
            
            cutoff_date = datetime.now() - timedelta(days=days)
            
            query = delete(ScheduleTaskHistory).where(
                ScheduleTaskHistory.created_at < cutoff_date
            )
            
            result = await self.db.execute(query)
            await self.db.commit()
            
            deleted_count = result.rowcount
            logger.info(f"清理了 {deleted_count} 条执行历史记录（{days}天前）")
            
            return deleted_count
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"清理执行历史记录失败, 错误: {str(e)}")
            return 0
