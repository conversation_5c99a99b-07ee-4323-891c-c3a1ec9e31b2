"""
数据生成任务仓库
提供生成任务的数据访问方法
"""
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime, timedelta
from sqlalchemy import select, func, and_, or_, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.repositories.base import BaseRepository
from app.models.data_factory.generation_task import GenerationTask
from app.schemas.data_factory.generation_task import GenerationTaskCreate, GenerationTaskUpdate


class GenerationTaskRepository(BaseRepository[GenerationTask, GenerationTaskCreate, GenerationTaskUpdate]):
    """
    数据生成任务仓库类
    继承BaseRepository，提供生成任务特有的数据访问方法
    """

    def __init__(self, db: AsyncSession):
        super().__init__(GenerationTask, db)

    async def get_with_model(self, task_id: int) -> Optional[GenerationTask]:
        """
        获取任务及其关联的数据模型
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务实例（包含关联模型）或None
        """
        result = await self.db.execute(
            select(GenerationTask)
            .options(selectinload(GenerationTask.model))
            .where(GenerationTask.id == task_id)
        )
        return result.scalar_one_or_none()

    async def get_by_status(self, status: str, skip: int = 0, limit: int = 100) -> List[GenerationTask]:
        """
        根据状态获取任务列表
        
        Args:
            status: 任务状态
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            任务列表
        """
        result = await self.db.execute(
            select(GenerationTask)
            .where(GenerationTask.status == status)
            .offset(skip)
            .limit(limit)
            .order_by(GenerationTask.created_at.desc())
        )
        return result.scalars().all()

    async def get_by_model_id(self, model_id: int, skip: int = 0, limit: int = 100) -> List[GenerationTask]:
        """
        根据模型ID获取任务列表
        
        Args:
            model_id: 数据模型ID
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            任务列表
        """
        result = await self.db.execute(
            select(GenerationTask)
            .where(GenerationTask.model_id == model_id)
            .offset(skip)
            .limit(limit)
            .order_by(GenerationTask.created_at.desc())
        )
        return result.scalars().all()

    async def get_running_tasks(self) -> List[GenerationTask]:
        """
        获取正在运行的任务列表
        
        Returns:
            正在运行的任务列表
        """
        result = await self.db.execute(
            select(GenerationTask)
            .where(GenerationTask.status == "running")
            .order_by(GenerationTask.started_at.asc())
        )
        return result.scalars().all()

    async def get_recent_tasks(self, days: int = 7, limit: int = 100) -> List[GenerationTask]:
        """
        获取最近的任务列表
        
        Args:
            days: 最近天数
            limit: 限制数量
            
        Returns:
            最近的任务列表
        """
        since_date = datetime.utcnow() - timedelta(days=days)
        
        result = await self.db.execute(
            select(GenerationTask)
            .where(GenerationTask.created_at >= since_date)
            .limit(limit)
            .order_by(GenerationTask.created_at.desc())
        )
        return result.scalars().all()

    async def update_task_status(self, task_id: int, status: str, progress: int = None, 
                                error_message: str = None) -> Optional[GenerationTask]:
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态
            progress: 进度
            error_message: 错误信息
            
        Returns:
            更新后的任务实例
        """
        task = await self.get(task_id)
        if not task:
            return None
        
        task.status = status
        if progress is not None:
            task.progress = progress
        if error_message is not None:
            task.error_message = error_message
        
        # 更新时间戳
        if status == "running" and not task.started_at:
            task.started_at = datetime.utcnow()
        elif status in ["completed", "failed", "cancelled"]:
            task.completed_at = datetime.utcnow()
            if task.started_at:
                task.execution_time = int((task.completed_at - task.started_at).total_seconds())
        
        await self.db.commit()
        await self.db.refresh(task)
        return task

    async def get_statistics(self) -> Dict[str, Any]:
        """
        获取任务统计信息
        
        Returns:
            统计信息字典
        """
        # 总数统计
        total_result = await self.db.execute(select(func.count(GenerationTask.id)))
        total_count = total_result.scalar()
        
        # 状态统计
        status_result = await self.db.execute(
            select(GenerationTask.status, func.count(GenerationTask.id))
            .group_by(GenerationTask.status)
        )
        status_stats = dict(status_result.fetchall())
        
        # 成功率计算
        completed_count = status_stats.get('completed', 0)
        failed_count = status_stats.get('failed', 0)
        total_finished = completed_count + failed_count
        success_rate = (completed_count / total_finished * 100) if total_finished > 0 else 0
        
        # 平均执行时间
        avg_time_result = await self.db.execute(
            select(func.avg(GenerationTask.execution_time))
            .where(GenerationTask.execution_time.isnot(None))
        )
        avg_execution_time = avg_time_result.scalar()
        
        return {
            'total_tasks': total_count,
            'pending_tasks': status_stats.get('pending', 0),
            'running_tasks': status_stats.get('running', 0),
            'completed_tasks': status_stats.get('completed', 0),
            'failed_tasks': status_stats.get('failed', 0),
            'cancelled_tasks': status_stats.get('cancelled', 0),
            'success_rate': round(success_rate, 2),
            'avg_execution_time': round(avg_execution_time, 2) if avg_execution_time else None
        }
