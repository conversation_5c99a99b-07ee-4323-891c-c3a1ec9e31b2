"""
数据模型实体
定义测试数据模型的数据库表结构
"""
from sqlalchemy import Column, String, Text, JSON, Integer
from app.models.base import BaseModel


class DataModel(BaseModel):
    """
    数据模型实体
    用于定义测试数据的结构和生成规则
    """
    __tablename__ = "data_factory_models"

    # 基本信息
    name = Column(String(100), nullable=False, unique=True, index=True, comment="模型名称（唯一标识）")
    description = Column(Text, nullable=True, comment="模型描述")
    version = Column(String(20), default="1.0.0", comment="版本号")
    
    # 分类和标签
    category = Column(String(50), nullable=True, comment="模型分类")
    tags = Column(JSON, nullable=True, comment="标签列表")
    
    # 字段配置
    fields_config = Column(JSON, nullable=False, comment="字段配置JSON")
    
    # 统计信息
    usage_count = Column(Integer, default=0, comment="使用次数")
    
    # 状态
    status = Column(String(10), default="1", comment="状态：1-启用，2-禁用")

    def __repr__(self):
        return f"<DataModel(id={self.id}, name='{self.name}', version='{self.version}')>"

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'version': self.version,
            'category': self.category,
            'tags': self.tags,
            'fields_config': self.fields_config,
            'usage_count': self.usage_count,
            'status': self.status,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'created_by': self.created_by,
            'updated_by': self.updated_by
        }
