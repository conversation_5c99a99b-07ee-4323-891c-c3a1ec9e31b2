"""
数据模型管理API
提供数据模型的CRUD操作接口
"""
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_user, get_db
from app.core.responses import response_builder
from app.schemas.base import PaginationResponse
from app.schemas.common import SearchParams
from app.schemas.data_factory.data_model import (
    DataModelCreate,
    DataModelUpdate,
    DataModelResponse,
    DataModelPreviewRequest,
    DataModelPreviewResponse
)
from app.services.data_factory.data_model import DataModelService
from app.models.user.user import User

router = APIRouter()


@router.get("/", response_model=PaginationResponse[DataModelResponse], summary="获取数据模型列表")
async def get_data_models(
    current: int = Query(1, ge=1, description="当前页码"),
    size: int = Query(20, ge=1, le=100, description="每页条数"),
    keyword: str = Query(None, description="搜索关键词"),
    category: str = Query(None, description="模型分类筛选"),
    status: str = Query(None, description="状态筛选：1-启用，2-禁用"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取数据模型列表，支持分页、筛选和搜索"""
    service = DataModelService(db)

    # 构建搜索参数
    search_params = SearchParams(
        current=current,
        size=size,
        keyword=keyword
    )

    # 使用BaseService的标准分页方法
    result = await service.list_with_pagination(search_params)

    return response_builder.paginated(
        result.records, result.total, result.current, result.size
    )


@router.post("/", response_model=DataModelResponse, status_code=201, summary="创建数据模型")
async def create_data_model(
    model_data: DataModelCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建新的数据模型"""
    service = DataModelService(db)
    model = await service.create(model_data, str(current_user.id))
    return response_builder.created(model)


@router.get("/{model_id}", response_model=DataModelResponse, summary="获取数据模型详情")
async def get_data_model(
    model_id: int = Path(..., description="模型ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指定数据模型的详细信息"""
    service = DataModelService(db)
    model = await service.get_by_id(model_id)
    return response_builder.success(model)


@router.put("/{model_id}", response_model=DataModelResponse, summary="更新数据模型")
async def update_data_model(
    model_data: DataModelUpdate,
    model_id: int = Path(..., description="模型ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新指定的数据模型"""
    service = DataModelService(db)
    model = await service.update(model_id, model_data, str(current_user.id))
    return response_builder.success(model)


@router.delete("/{model_id}", status_code=204, summary="删除数据模型")
async def delete_data_model(
    model_id: int = Path(..., description="模型ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除指定的数据模型"""
    service = DataModelService(db)
    await service.delete(model_id)
    # HTTP 204 No Content - 无返回语句


@router.post("/parse-sample", summary="解析样本数据")
async def parse_sample_data(
    request: dict,
    current_user: User = Depends(get_current_user)
):
    """解析JSON样本数据，自动推断字段类型和生成器"""
    from app.utils.data_factory.sample_parser import SampleDataParser

    json_data = request.get('json_data')
    if not json_data:
        return response_builder.error("请提供样本数据")

    parser = SampleDataParser()
    result = parser.parse_json_samples(json_data)

    if result['success']:
        return response_builder.success(result)
    else:
        return response_builder.error(result['error'])


@router.post("/test-custom-generator", summary="测试自定义生成器")
async def test_custom_generator(
    request: dict,
    current_user: User = Depends(get_current_user)
):
    """测试自定义生成器代码"""
    from app.utils.data_factory.sample_parser import test_custom_generator_code

    code = request.get('code')
    samples = request.get('samples', [])
    test_count = request.get('test_count', 5)

    if not code:
        return response_builder.error("请提供生成器代码")

    result = test_custom_generator_code(code, samples, test_count)

    if result['success']:
        return response_builder.success(result)
    else:
        return response_builder.error(result['error'])


@router.post("/{model_id}/preview", response_model=DataModelPreviewResponse, summary="预览数据模型")
async def preview_data_model(
    model_id: int = Path(..., description="模型ID"),
    count: int = Query(5, ge=1, le=20, description="预览数据条数"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """预览数据模型生成的数据"""
    service = DataModelService(db)
    preview = await service.preview_data(model_id, count)
    return response_builder.success(preview)


@router.post("/{model_id}/duplicate", response_model=DataModelResponse, status_code=201, summary="复制数据模型")
async def duplicate_data_model(
    model_id: int = Path(..., description="源模型ID"),
    new_name: str = Query(..., description="新模型名称"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """复制现有的数据模型"""
    service = DataModelService(db)
    model = await service.duplicate_model(model_id, new_name, str(current_user.id))
    return response_builder.created(model)


@router.get("/statistics/overview", response_model=Dict[str, Any], summary="获取数据模型统计")
async def get_model_statistics(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取数据模型的统计信息"""
    service = DataModelService(db)
    statistics = await service.get_statistics()
    return response_builder.success(statistics)


@router.get("/categories/list", response_model=List[str], summary="获取模型分类列表")
async def get_model_categories(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有模型分类列表"""
    # 这里应该从数据库查询所有不重复的分类
    # 简化实现，返回常用分类
    categories = [
        "用户数据",
        "订单数据", 
        "商品数据",
        "日志数据",
        "测试数据",
        "其他"
    ]
    return response_builder.success(categories)
