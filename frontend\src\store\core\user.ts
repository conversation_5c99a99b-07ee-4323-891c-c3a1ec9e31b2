/**
 * 用户核心状态管理
 * 迁移自 src/store/modules/user.ts
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useLoading } from '@/store/shared/loading'
import { globalCache } from '@/store/shared/cache'

// 这里需要根据实际的用户API和类型定义进行调整
// import userApi from '@/api/userApi'
// import type { UserInfo, UserLoginRequest } from '@/types/api/user'

export const useUserStore = defineStore('user', () => {
  // ==================== 状态定义 ====================
  
  const userInfo = ref<any>(null)
  const token = ref<string>('')
  const permissions = ref<string[]>([])
  const roles = ref<string[]>([])
  
  // 加载状态
  const loading = useLoading()

  // ==================== 计算属性 ====================
  
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  const isAdmin = computed(() => roles.value.includes('admin'))
  const isSuperUser = computed(() => roles.value.includes('superuser'))
  
  const hasPermission = computed(() => (permission: string) => {
    return permissions.value.includes(permission)
  })
  
  const hasRole = computed(() => (role: string) => {
    return roles.value.includes(role)
  })

  // ==================== 用户管理方法 ====================
  
  /**
   * 用户登录
   */
  const login = async (loginData: any) => {
    return await loading.withLoading(async () => {
      // const response = await userApi.login(loginData)
      // token.value = response.token
      // userInfo.value = response.user
      // permissions.value = response.permissions || []
      // roles.value = response.roles || []
      
      // 缓存用户信息
      // globalCache.set('user_info', response.user, 30 * 60 * 1000) // 30分钟缓存
      
      // 存储到localStorage
      // localStorage.setItem('token', response.token)
      // localStorage.setItem('user_info', JSON.stringify(response.user))
      
      // return response
      
      // 临时实现，需要根据实际API调整
      console.log('Login method needs to be implemented with actual API')
      return null
    })
  }

  /**
   * 用户登出
   */
  const logout = async () => {
    return await loading.withLoading(async () => {
      try {
        // await userApi.logout()
      } catch (error) {
        console.error('Logout API error:', error)
      } finally {
        // 清除状态
        token.value = ''
        userInfo.value = null
        permissions.value = []
        roles.value = []
        
        // 清除缓存
        globalCache.clear()
        
        // 清除localStorage
        localStorage.removeItem('token')
        localStorage.removeItem('user_info')
      }
    })
  }

  /**
   * 获取用户信息（带智能缓存）
   */
  const fetchUserInfo = async (forceRefresh: boolean = false) => {
    // 如果不强制刷新，先检查缓存
    if (!forceRefresh) {
      const cached = globalCache.get('user_info')
      const lastFetchTime = globalCache.get('user_info_timestamp')
      const cacheValidTime = 10 * 60 * 1000 // 10分钟缓存有效期

      if (cached && lastFetchTime && (Date.now() - lastFetchTime < cacheValidTime)) {
        userInfo.value = cached.user
        permissions.value = cached.permissions || []
        roles.value = cached.roles || []
        return cached
      }
    }

    return await loading.withLoading(async () => {
      try {
        const response = await userApi.getUserInfo()

        // 更新状态
        userInfo.value = response.user
        permissions.value = response.permissions || []
        roles.value = response.roles || []

        // 缓存用户信息和时间戳
        const cacheData = {
          user: response.user,
          permissions: response.permissions || [],
          roles: response.roles || []
        }
        globalCache.set('user_info', cacheData, 30 * 60 * 1000) // 30分钟过期
        globalCache.set('user_info_timestamp', Date.now(), 30 * 60 * 1000)

        return response
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 如果请求失败，尝试使用过期的缓存数据
        const cached = globalCache.get('user_info')
        if (cached) {
          userInfo.value = cached.user
          permissions.value = cached.permissions || []
          roles.value = cached.roles || []
          return cached
        }
        throw error
      }
    })
  }

  /**
   * 更新用户信息
   */
  const updateUserInfo = async (updateData: any) => {
    return await loading.withLoading(async () => {
      try {
        const response = await userApi.updateUserInfo(updateData)

        // 更新本地状态
        userInfo.value = { ...userInfo.value, ...response }

        // 更新缓存
        const cacheData = {
          user: userInfo.value,
          permissions: permissions.value,
          roles: roles.value
        }
        globalCache.set('user_info', cacheData, 30 * 60 * 1000)
        globalCache.set('user_info_timestamp', Date.now(), 30 * 60 * 1000)

        return response
      } catch (error) {
        console.error('更新用户信息失败:', error)
        throw error
      }
    })
  }

  /**
   * 强制刷新用户信息（清除缓存）
   */
  const refreshUserInfo = async () => {
    // 清除缓存
    globalCache.remove('user_info')
    globalCache.remove('user_info_timestamp')
    localStorage.removeItem('user_info_last_update')

    // 强制重新获取
    return await fetchUserInfo(true)
  }

  /**
   * 检查用户信息是否需要刷新
   */
  const shouldRefreshUserInfo = (): boolean => {
    const lastFetchTime = globalCache.get('user_info_timestamp')
    const cacheValidTime = 10 * 60 * 1000 // 10分钟

    return !lastFetchTime || (Date.now() - lastFetchTime > cacheValidTime)
  }

  /**
   * 修改密码
   */
  const changePassword = async (passwordData: any) => {
    return await loading.withLoading(async () => {
      // const response = await userApi.changePassword(passwordData)
      // return response
      
      // 临时实现
      console.log('ChangePassword method needs to be implemented with actual API')
      return null
    })
  }

  /**
   * 刷新Token
   */
  const refreshToken = async () => {
    return await loading.withLoading(async () => {
      // const response = await userApi.refreshToken()
      // token.value = response.token
      
      // 更新localStorage
      // localStorage.setItem('token', response.token)
      
      // return response
      
      // 临时实现
      console.log('RefreshToken method needs to be implemented with actual API')
      return null
    })
  }

  // ==================== 初始化方法 ====================
  
  /**
   * 从localStorage初始化状态
   */
  const initializeFromStorage = () => {
    const storedToken = localStorage.getItem('token')
    const storedUserInfo = localStorage.getItem('user_info')
    
    if (storedToken) {
      token.value = storedToken
    }
    
    if (storedUserInfo) {
      try {
        userInfo.value = JSON.parse(storedUserInfo)
      } catch (error) {
        console.error('Failed to parse stored user info:', error)
        localStorage.removeItem('user_info')
      }
    }
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    userInfo.value = null
    token.value = ''
    permissions.value = []
    roles.value = []
    loading.reset()
  }

  // 初始化
  initializeFromStorage()

  return {
    // 状态
    userInfo: readonly(userInfo),
    token: readonly(token),
    permissions: readonly(permissions),
    roles: readonly(roles),
    loading,
    
    // 计算属性
    isLoggedIn,
    isAdmin,
    isSuperUser,
    hasPermission,
    hasRole,
    
    // 方法
    login,
    logout,
    fetchUserInfo,
    updateUserInfo,
    refreshUserInfo,
    shouldRefreshUserInfo,
    changePassword,
    refreshToken,
    initializeFromStorage,
    resetState
  }
})
