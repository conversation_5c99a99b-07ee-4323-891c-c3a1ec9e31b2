"""
数据生成任务实体
定义数据生成任务的数据库表结构
"""
from sqlalchemy import Column, String, Integer, Text, ForeignKey, DateTime, JSON
from sqlalchemy.orm import relationship
from app.models.base import BaseModel


class GenerationTask(BaseModel):
    """
    数据生成任务实体
    记录数据生成任务的状态和结果
    """
    __tablename__ = "data_factory_generation_tasks"

    # 基本信息
    name = Column(String(100), nullable=False, comment="任务名称")
    description = Column(Text, nullable=True, comment="任务描述")
    
    # 关联模型
    model_id = Column(Integer, ForeignKey('data_factory_models.id'), nullable=False, comment="数据模型ID")
    model = relationship("DataModel", backref="generation_tasks")
    
    # 生成配置
    record_count = Column(Integer, nullable=False, comment="生成数据条数")
    export_format = Column(String(20), default="json", comment="导出格式：json/csv/excel/sql")
    export_config = Column(JSON, nullable=True, comment="导出配置JSON")
    
    # 任务状态
    status = Column(String(20), default="pending", comment="任务状态：pending/running/completed/failed/cancelled")
    progress = Column(Integer, default=0, comment="执行进度（0-100）")
    
    # 执行信息
    started_at = Column(DateTime, nullable=True, comment="开始执行时间")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")
    execution_time = Column(Integer, nullable=True, comment="执行耗时（秒）")
    
    # 结果信息
    result_file_path = Column(String(500), nullable=True, comment="结果文件路径")
    result_file_size = Column(Integer, nullable=True, comment="结果文件大小（字节）")
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    # 资源使用
    memory_usage = Column(Integer, nullable=True, comment="内存使用量（MB）")
    cpu_usage = Column(Integer, nullable=True, comment="CPU使用率（%）")

    def __repr__(self):
        return f"<GenerationTask(id={self.id}, name='{self.name}', status='{self.status}')>"

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'model_id': self.model_id,
            'record_count': self.record_count,
            'export_format': self.export_format,
            'export_config': self.export_config,
            'status': self.status,
            'progress': self.progress,
            'started_at': self.started_at,
            'completed_at': self.completed_at,
            'execution_time': self.execution_time,
            'result_file_path': self.result_file_path,
            'result_file_size': self.result_file_size,
            'error_message': self.error_message,
            'memory_usage': self.memory_usage,
            'cpu_usage': self.cpu_usage,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'created_by': self.created_by,
            'updated_by': self.updated_by
        }
