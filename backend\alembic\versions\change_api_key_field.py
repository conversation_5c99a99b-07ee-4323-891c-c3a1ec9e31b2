"""change api_key_encrypted to api_key

Revision ID: change_api_key_field
Revises: 
Create Date: 2025-01-03 01:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'change_api_key_field'
down_revision = None
depends_on = None


def upgrade() -> None:
    """修改 model_config 表的 api_key_encrypted 字段为 api_key"""
    # 检查表是否存在
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    
    if 'model_config' in inspector.get_table_names():
        # 检查字段是否存在
        columns = [col['name'] for col in inspector.get_columns('model_config')]
        
        if 'api_key_encrypted' in columns:
            # 重命名字段
            op.alter_column('model_config', 'api_key_encrypted', 
                          new_column_name='api_key',
                          comment='API Key（明文存储，本地模型可为空）')
        elif 'api_key' not in columns:
            # 如果两个字段都不存在，添加新字段
            op.add_column('model_config', 
                         sa.Column('api_key', sa.String(255), nullable=True, 
                                 comment='API Key（明文存储，本地模型可为空）'))


def downgrade() -> None:
    """回滚：将 api_key 字段改回 api_key_encrypted"""
    # 检查表是否存在
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    
    if 'model_config' in inspector.get_table_names():
        # 检查字段是否存在
        columns = [col['name'] for col in inspector.get_columns('model_config')]
        
        if 'api_key' in columns:
            # 重命名字段
            op.alter_column('model_config', 'api_key', 
                          new_column_name='api_key_encrypted',
                          comment='加密的API Key（本地模型可为空）')
