"""
混沌测试故障场景数据访问层
"""
from typing import List, Optional, Dict, Any, Tuple

from sqlalchemy import and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.repositories.base import BaseRepository
from app.models.chaos.chaos_scenario import ChaosScenario
from app.schemas.chaos.chaos_scenario import ChaosScenarioCreate, ChaosScenarioUpdate


class ChaosScenarioRepository(BaseRepository[ChaosScenario, ChaosScenarioCreate, ChaosScenarioUpdate]):
    """
    故障场景模板数据访问层
    专注于场景模板相关的数据库操作
    """

    def __init__(self, db: AsyncSession):
        super().__init__(ChaosScenario, db)

    async def get_by_name(self, name: str) -> Optional[ChaosScenario]:
        """
        根据名称获取场景"""
        return await self.get_by_field("name", name, unique=True)

    async def get_by_fault_type(self, fault_type: str) -> List[ChaosScenario]:
        """根据故障类型获取场景列表"""
        return await self.get_by_field("fault_type", fault_type, unique=False)


    async def get_custom_scenarios(self) -> List[ChaosScenario]:
        """获取自定义场景列表 """
        return await self.get_by_field("is_builtin", False, unique=False)

    async def get_active_scenarios(self) -> List[ChaosScenario]:
        """获取启用的场景列表 """
        return await self.get_by_field("is_active", True, unique=False)

    async def get_by_category(self, category: str) -> List[ChaosScenario]:
        """根据分类获取场景列表 """
        return await self.get_by_field("category", category, unique=False)

    async def increment_usage_count(self, scenario_id: int) -> bool:
        """增加场景使用次数 """
        scenario = await self.get(scenario_id)
        if not scenario:
            return False

        scenario.usage_count = (scenario.usage_count or 0) + 1
        await self.db.commit()
        return True

    async def get_scenarios_by_tags(self, tags: List[str]) -> List[ChaosScenario]:
        """根据标签获取场景列表 """
        if not tags:
            return []

        conditions = []
        for tag in tags:
            conditions.append(ChaosScenario.tags.ilike(f"%{tag}%"))

        query = (
            select(ChaosScenario)
            .where(
                and_(
                    ChaosScenario.is_active == True,
                    or_(*conditions)
                )
            )
        )
        result = await self.db.execute(query)
        return result.scalars().all()

    async def check_name_exists(self, name: str, exclude_id: Optional[int] = None) -> bool:
        """检查场景名称是否已存在 """
        query = select(ChaosScenario).where(ChaosScenario.name == name)
        
        if exclude_id:
            query = query.where(ChaosScenario.id != exclude_id)

        result = await self.db.execute(query)
        return result.scalar_one_or_none() is not None

    async def get_statistics(self) -> Dict[str, Any]:
        """获取场景统计信息 """
        # 总场景数
        total_query = select(func.count(ChaosScenario.id))
        total_result = await self.db.execute(total_query)
        total_count = total_result.scalar()

        # 内置vs自定义统计
        builtin_query = select(func.count(ChaosScenario.id)).where(ChaosScenario.is_builtin == True)
        builtin_result = await self.db.execute(builtin_query)
        builtin_count = builtin_result.scalar()

        # 按故障类型统计
        fault_type_query = (
            select(ChaosScenario.fault_type, func.count(ChaosScenario.id))
            .group_by(ChaosScenario.fault_type)
        )
        fault_type_result = await self.db.execute(fault_type_query)
        fault_type_stats = {row[0]: row[1] for row in fault_type_result.fetchall()}

        # 按分类统计
        category_query = (
            select(ChaosScenario.category, func.count(ChaosScenario.id))
            .where(ChaosScenario.category.isnot(None))
            .group_by(ChaosScenario.category)
        )
        category_result = await self.db.execute(category_query)
        category_stats = {row[0]: row[1] for row in category_result.fetchall()}

        return {
            "total_count": total_count,
            "builtin_count": builtin_count,
            "custom_count": total_count - builtin_count,
            "fault_type_stats": fault_type_stats,
            "category_stats": category_stats
        }

    async def search_scenarios_with_query(self, query) -> Tuple[List[ChaosScenario], int]:
        """使用新的查询参数搜索场景"""
        # 基础查询
        base_query = select(ChaosScenario)

        # 筛选条件
        conditions = []

        if query.keyword:
            conditions.append(
                or_(
                    ChaosScenario.name.ilike(f"%{query.keyword}%"),
                    ChaosScenario.description.ilike(f"%{query.keyword}%")
                )
            )

        if query.fault_type:
            conditions.append(ChaosScenario.fault_type == query.fault_type)

        if query.category:
            conditions.append(ChaosScenario.category == query.category)

        if query.is_builtin is not None:
            conditions.append(ChaosScenario.is_builtin == query.is_builtin)

        if query.is_active is not None:
            conditions.append(ChaosScenario.is_active == query.is_active)

        if conditions:
            base_query = base_query.where(and_(*conditions))

        # 计算总条数
        count_query = select(func.count()).select_from(base_query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()

        # 排序
        if query.order_by == "usage_count":
            order_column = ChaosScenario.usage_count
        elif query.order_by == "created_at":
            order_column = ChaosScenario.created_at
        elif query.order_by == "updated_at":
            order_column = ChaosScenario.updated_at
        else:
            order_column = ChaosScenario.usage_count

        if query.desc:
            base_query = base_query.order_by(order_column.desc())
        else:
            base_query = base_query.order_by(order_column.asc())

        # 分页
        base_query = base_query.offset(query.offset).limit(query.size)

        result = await self.db.execute(base_query)
        items = result.scalars().all()

        return items, total

    async def list_active_scenarios(self) -> List[ChaosScenario]:
        """
        获取所有启用的场景列表，用于任务创建
        """
        query = select(ChaosScenario).where(
            ChaosScenario.is_active == True
        ).order_by(ChaosScenario.fault_type, ChaosScenario.name)

        result = await self.db.execute(query)
        return result.scalars().all()
