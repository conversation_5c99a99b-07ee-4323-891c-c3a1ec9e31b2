"""
服务层通用工具函数
提供各种服务类常用的辅助方法
"""
from typing import Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession


class ServiceHelper:
    """服务层辅助工具类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_user_info(self, user_id: str) -> Optional[Dict[str, Any]]:
        """根据用户ID获取用户信息"""
        if not user_id:
            return None
            
        try:
            from app.repositories.user.user import UserRepository
            user_repo = UserRepository(self.db)
            user = await user_repo.get(int(user_id))
            if user:
                return {
                    "id": user.id,
                    "username": user.username,
                    "nickname": user.nickname,
                    "email": user.email,
                    "display_name": user.nickname or user.username
                }
            return None
        except Exception:
            return None
    
    async def get_user_display_name(self, user_id: str) -> str:
        """获取用户显示名称（昵称或用户名）"""
        if not user_id:
            return "-"
            
        user_info = await self.get_user_info(user_id)
        if user_info:
            return user_info["display_name"]
        return f"用户{user_id}"
    
    async def get_environment_info(self, env_id: int) -> Optional[Dict[str, Any]]:
        """根据环境ID获取环境信息"""
        if not env_id:
            return None
            
        try:
            from app.repositories.env import EnvironmentRepository
            env_repo = EnvironmentRepository(self.db)
            environment = await env_repo.get(env_id)
            if environment:
                return {
                    "id": environment.id,
                    "name": environment.name,
                    "description": environment.description,
                    "status": environment.status
                }
            return None
        except Exception:
            return None
    
    async def get_environment_name(self, env_id: int) -> str:
        """获取环境名称"""
        if not env_id:
            return "-"
            
        env_info = await self.get_environment_info(env_id)
        if env_info:
            return env_info["name"]
        return f"环境{env_id}"
    
    async def get_multiple_users_display_names(self, user_ids: list) -> Dict[str, str]:
        """批量获取用户显示名称"""
        result = {}
        for user_id in user_ids:
            if user_id:
                result[str(user_id)] = await self.get_user_display_name(str(user_id))
        return result
    
    async def get_multiple_environments_names(self, env_ids: list) -> Dict[int, str]:
        """批量获取环境名称"""
        result = {}
        for env_id in env_ids:
            if env_id:
                result[env_id] = await self.get_environment_name(env_id)
        return result


# 全局辅助函数，可以直接导入使用
async def get_user_display_name(db: AsyncSession, user_id: str) -> str:
    """获取用户显示名称的便捷函数"""
    helper = ServiceHelper(db)
    return await helper.get_user_display_name(user_id)


async def get_environment_name(db: AsyncSession, env_id: int) -> str:
    """获取环境名称的便捷函数"""
    helper = ServiceHelper(db)
    return await helper.get_environment_name(env_id)


async def get_user_info(db: AsyncSession, user_id: str) -> Optional[Dict[str, Any]]:
    """获取用户信息的便捷函数"""
    helper = ServiceHelper(db)
    return await helper.get_user_info(user_id)


async def get_environment_info(db: AsyncSession, env_id: int) -> Optional[Dict[str, Any]]:
    """获取环境信息的便捷函数"""
    helper = ServiceHelper(db)
    return await helper.get_environment_info(env_id)
