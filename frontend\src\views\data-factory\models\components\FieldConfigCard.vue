<template>
  <div class="field-config-card">
    <el-card class="field-card" :class="{ 'field-card--required': field.required }">
      <!-- 卡片头部 -->
      <template #header>
        <div class="card-header">
          <div class="field-info">
            <div class="field-name">
              <span class="name-text">{{ field.name }}</span>
              <el-tag v-if="field.required" type="danger" size="small" class="required-tag">
                必填
              </el-tag>
            </div>
            <div class="field-meta">
              <el-tag type="info" size="small">{{ field.type }}</el-tag>
              <el-tag type="success" size="small">{{ field.generator }}</el-tag>
            </div>
          </div>
          <div class="card-actions">
            <el-button-group size="small">
              <el-button @click="handleMoveUp" :disabled="index === 0">
                <el-icon><ArrowUp /></el-icon>
              </el-button>
              <el-button @click="handleMoveDown" :disabled="isLast">
                <el-icon><ArrowDown /></el-icon>
              </el-button>
            </el-button-group>
            <el-dropdown @command="handleCommand" trigger="click">
              <el-button size="small" type="text">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item command="duplicate">
                    <el-icon><CopyDocument /></el-icon>
                    复制
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>

      <!-- 卡片内容 -->
      <div class="card-content">
        <!-- 字段描述 -->
        <div v-if="field.description" class="field-description">
          <el-icon><InfoFilled /></el-icon>
          <span>{{ field.description }}</span>
        </div>

        <!-- 生成器选项 -->
        <div v-if="hasOptions" class="generator-options">
          <div class="options-title">
            <el-icon><Setting /></el-icon>
            <span>生成器配置</span>
          </div>
          <div class="options-content">
            <div
              v-for="(value, key) in field.options"
              :key="key"
              class="option-item"
            >
              <span class="option-key">{{ key }}:</span>
              <span class="option-value">{{ formatOptionValue(value) }}</span>
            </div>
          </div>
        </div>

        <!-- 字段预览 -->
        <div class="field-preview">
          <div class="preview-title">
            <el-icon><View /></el-icon>
            <span>数据预览</span>
            <el-button size="small" text @click="handleRefreshPreview">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
          <div class="preview-content">
            <div class="preview-samples">
              <el-tag
                v-for="(sample, idx) in previewSamples"
                :key="idx"
                class="sample-tag"
                size="small"
                type="info"
              >
                {{ sample }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 字段统计 -->
        <div class="field-stats">
          <div class="stat-item">
            <span class="stat-label">数据类型</span>
            <span class="stat-value">{{ getTypeDescription(field.type) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">生成器</span>
            <span class="stat-value">{{ getGeneratorDescription(field.generator) }}</span>
          </div>
          <div v-if="hasOptions" class="stat-item">
            <span class="stat-label">配置项</span>
            <span class="stat-value">{{ Object.keys(field.options || {}).length }} 个</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowUp,
  ArrowDown,
  MoreFilled,
  Edit,
  CopyDocument,
  Delete,
  InfoFilled,
  Setting,
  View,
  Refresh
} from '@element-plus/icons-vue'

interface Props {
  field: Api.DataFactory.FieldConfig
  index: number
  isLast?: boolean
}

interface Emits {
  (e: 'update', index: number, field: Api.DataFactory.FieldConfig): void
  (e: 'delete', index: number): void
  (e: 'move-up', index: number): void
  (e: 'move-down', index: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const previewSamples = ref<string[]>([])

// 计算属性
const hasOptions = computed(() => {
  return props.field.options && Object.keys(props.field.options).length > 0
})

// 页面初始化
onMounted(() => {
  generatePreviewSamples()
})

// 方法
const handleCommand = async (command: string) => {
  switch (command) {
    case 'edit':
      emit('update', props.index, props.field)
      break
    case 'duplicate':
      const duplicatedField = {
        ...props.field,
        name: `${props.field.name}_copy`
      }
      emit('update', -1, duplicatedField) // -1 表示新增
      break
    case 'delete':
      try {
        await ElMessageBox.confirm(
          `确定要删除字段 "${props.field.name}" 吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        emit('delete', props.index)
      } catch (error) {
        // 用户取消删除
      }
      break
  }
}

const handleMoveUp = () => {
  emit('move-up', props.index)
}

const handleMoveDown = () => {
  emit('move-down', props.index)
}

const handleRefreshPreview = () => {
  generatePreviewSamples()
}

const formatOptionValue = (value: any): string => {
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  return String(value)
}

const getTypeDescription = (type: string): string => {
  const descriptions: Record<string, string> = {
    string: '字符串',
    integer: '整数',
    decimal: '小数',
    boolean: '布尔值',
    date: '日期',
    datetime: '日期时间'
  }
  return descriptions[type] || type
}

const getGeneratorDescription = (generator: string): string => {
  const descriptions: Record<string, string> = {
    uuid: 'UUID生成器',
    name: '姓名生成器',
    phone: '手机号生成器',
    email: '邮箱生成器',
    range: '数值范围生成器',
    sequence: '序列生成器',
    date: '日期生成器'
  }
  return descriptions[generator] || generator
}

const generatePreviewSamples = () => {
  // 根据字段类型和生成器生成预览样本
  const samples: string[] = []
  
  for (let i = 0; i < 3; i++) {
    let sample = ''
    
    switch (props.field.generator) {
      case 'uuid':
        sample = `uuid-${Math.random().toString(36).substr(2, 8)}`
        break
      case 'name':
        const names = ['张三', '李四', '王五', '赵六', '钱七']
        sample = names[Math.floor(Math.random() * names.length)]
        break
      case 'phone':
        sample = `138${Math.random().toString().substr(2, 8)}`
        break
      case 'email':
        sample = `user${i + 1}@example.com`
        break
      case 'range':
        const min = props.field.options?.min || 1
        const max = props.field.options?.max || 100
        sample = String(Math.floor(Math.random() * (max - min + 1)) + min)
        break
      case 'sequence':
        const start = props.field.options?.start || 1
        sample = String(start + i)
        break
      case 'date':
        const date = new Date()
        date.setDate(date.getDate() - Math.floor(Math.random() * 365))
        sample = date.toISOString().split('T')[0]
        break
      default:
        sample = `${props.field.generator}_${i + 1}`
    }
    
    samples.push(sample)
  }
  
  previewSamples.value = samples
}
</script>

<style scoped lang="scss">
.field-config-card {
  .field-card {
    border: 2px solid var(--el-border-color-light);
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--el-color-primary);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
    }

    &--required {
      border-left: 4px solid var(--el-color-danger);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .field-info {
        flex: 1;

        .field-name {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          .name-text {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
          }

          .required-tag {
            font-size: 12px;
          }
        }

        .field-meta {
          display: flex;
          gap: 6px;
        }
      }

      .card-actions {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }

    .card-content {
      .field-description {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 16px;
        padding: 12px;
        background: var(--el-fill-color-light);
        border-radius: 6px;
        font-size: 14px;
        color: var(--el-text-color-regular);
        line-height: 1.5;

        .el-icon {
          color: var(--el-color-info);
          margin-top: 2px;
          flex-shrink: 0;
        }
      }

      .generator-options {
        margin-bottom: 16px;

        .options-title {
          display: flex;
          align-items: center;
          gap: 6px;
          margin-bottom: 8px;
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-primary);

          .el-icon {
            color: var(--el-color-warning);
          }
        }

        .options-content {
          background: var(--el-fill-color-extra-light);
          padding: 12px;
          border-radius: 6px;

          .option-item {
            display: flex;
            margin-bottom: 6px;
            font-size: 13px;

            &:last-child {
              margin-bottom: 0;
            }

            .option-key {
              min-width: 80px;
              color: var(--el-text-color-regular);
              font-weight: 500;
            }

            .option-value {
              color: var(--el-text-color-primary);
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            }
          }
        }
      }

      .field-preview {
        margin-bottom: 16px;

        .preview-title {
          display: flex;
          align-items: center;
          gap: 6px;
          margin-bottom: 8px;
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-primary);

          .el-icon {
            color: var(--el-color-success);
          }
        }

        .preview-content {
          .preview-samples {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;

            .sample-tag {
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              font-size: 12px;
              background: var(--el-fill-color-light);
              border: 1px solid var(--el-border-color-lighter);
            }
          }
        }
      }

      .field-stats {
        border-top: 1px solid var(--el-border-color-lighter);
        padding-top: 12px;

        .stat-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 6px;
          font-size: 13px;

          &:last-child {
            margin-bottom: 0;
          }

          .stat-label {
            color: var(--el-text-color-regular);
          }

          .stat-value {
            color: var(--el-text-color-primary);
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style>
