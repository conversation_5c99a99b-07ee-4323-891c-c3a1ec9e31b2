<template>
  <ElConfigProvider size="default" :locale="locales[language]" :z-index="3000">
    <RouterView></RouterView>
  </ElConfigProvider>
</template>

<script setup lang="ts">
  import { useUserStore } from './store/modules/user'
  import zh from 'element-plus/es/locale/lang/zh-cn'
  import en from 'element-plus/es/locale/lang/en'
  import { UserService } from './api/usersApi'
  import { setThemeTransitionClass } from './utils/theme/animation'
  import { checkStorageCompatibility } from './utils/storage'

  const userStore = useUserStore()
  const { language } = storeToRefs(userStore)

  const locales = {
    zh: zh,
    en: en
  }

  onBeforeMount(() => {
    setThemeTransitionClass(true)
  })

  onMounted(() => {
    // 检查存储兼容性
    checkStorageCompatibility()
    // 提升暗黑主题下页面刷新视觉体验
    setThemeTransitionClass(false)
    // 系统升级
    // 获取用户信息
    getUserInfo()
  })

  // 获取用户信息（智能缓存）
  const getUserInfo = async () => {
    if (userStore.isLogin) {
      try {
        const { userInfoManager } = await import('@/utils/userInfoManager')

        // 检查是否需要刷新
        const hasUserInfo = userStore.getUserInfo && Object.keys(userStore.getUserInfo).length > 0
        const shouldRefresh = !hasUserInfo || userInfoManager.shouldRefresh()

        if (shouldRefresh) {
          const data = await userInfoManager.getUserInfo({
            fallbackToCache: true, // 允许在网络错误时使用缓存
            cacheTime: 10 * 60 * 1000 // 10分钟缓存
          })

          if (data) {
            userStore.setUserInfo(data)
          }
        }
      } catch (error) {
        console.error('获取用户信息失败', error)

        // 根据错误类型决定处理方式
        if (error?.response?.status === 401) {
          // 认证失败，登出用户
          userStore.logOut()
        } else if (error?.response?.status >= 500) {
          // 服务器错误，不影响用户使用，静默处理
          console.warn('服务器暂时不可用，使用缓存数据')
        }
        // 其他错误（如网络错误）不做特殊处理，让用户继续使用
      }
    }
  }
</script>
