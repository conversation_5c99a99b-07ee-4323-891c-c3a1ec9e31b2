"""
用户数据访问层
参考示例代码的简洁设计，专注于数据访问操作
"""
from typing import List, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from sqlalchemy import func, or_

from app.repositories.base import BaseRepository
from app.models.user.user import User
from app.schemas.user.user import UserCreate, UserUpdate


class UserRepository(BaseRepository[User, UserCreate, UserUpdate]):
    """用户数据访问层"""

    def __init__(self, db: AsyncSession):
        super().__init__(User, db)

    async def get_by_username(self, username: str) -> Optional[User]:
        """根据用户名查询用户（用于登录验证）"""
        result = await self.db.execute(
            select(User)
            .options(selectinload(User.roles))
            .where(User.username == username)
        )
        return result.scalar_one_or_none()

    async def get_by_email(self, email: str) -> Optional[User]:
        """根据邮箱查询用户（用于邮箱唯一性校验）"""
        result = await self.db.execute(
            select(User)
            .options(selectinload(User.roles))
            .where(User.email == email)
        )
        return result.scalar_one_or_none()

    async def get_with_roles(self, user_id: int) -> Optional[User]:
        """获取用户信息（预加载角色关系）"""
        result = await self.db.execute(
            select(User)
            .options(selectinload(User.roles))
            .where(User.id == user_id)
        )
        return result.scalar_one_or_none()

    async def list(
        self,
        keyword: Optional[str] = None,
        status: Optional[str] = None,
        is_active: Optional[bool] = None,
        offset: int = 0,
        limit: int = 10
    ) -> Tuple[List[User], int]:
        """查询用户列表（带筛选和分页）"""
        # 基础查询
        query = select(User).options(selectinload(User.roles))

        # 筛选条件
        if keyword:
            query = query.where(
                or_(
                    User.username.ilike(f"%{keyword}%"),
                    User.email.ilike(f"%{keyword}%"),
                    User.nickname.ilike(f"%{keyword}%")
                )
            )
        if status:
            query = query.where(User.status == status)
        if is_active is not None:
            query = query.where(User.is_active == is_active)

        # 计算总条数
        total_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(total_query)
        total = total_result.scalar()

        # 分页查询
        query = query.offset(offset).limit(limit).order_by(User.created_at.desc())
        result = await self.db.execute(query)
        items = result.scalars().all()

        return items, total


    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """用户认证"""
        from app.utils.security import verify_password

        user = await self.get_by_username(username)
        if not user:
            return None

        if not verify_password(password, user.hashed_password):
            return None

        return user

    async def update_last_login(self, user_id: int) -> bool:
        """更新最后登录时间"""
        user = await self.get(user_id)
        if not user:
            return False
        from app.utils.timezone import now
        user.last_login_at = now().strftime("%Y-%m-%d %H:%M:%S")
        await self.db.commit()
        return True