"""
数据工厂模块 - Schema定义
"""
from .data_model import (
    DataModelBase,
    DataModelCreate,
    DataModelUpdate,
    DataModelResponse,
    DataModelPreviewRequest,
    DataModelPreviewResponse
)
from .generation_task import (
    GenerationTaskBase,
    GenerationTaskCreate,
    GenerationTaskUpdate,
    GenerationTaskResponse,
    TaskStatusUpdate
)

__all__ = [
    # 数据模型相关
    "DataModelBase",
    "DataModelCreate",
    "DataModelUpdate", 
    "DataModelResponse",
    "DataModelPreviewRequest",
    "DataModelPreviewResponse",
    
    # 生成任务相关
    "GenerationTaskBase",
    "GenerationTaskCreate",
    "GenerationTaskUpdate",
    "GenerationTaskResponse",
    "TaskStatusUpdate"
]
