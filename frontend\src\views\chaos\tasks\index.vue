<template>
  <div class=" art-full-height">
    <!-- 搜索栏 -->
    <TaskSearch v-model:filter="searchForm" @reset="handleReset" @search="handleSearch" />

    <ElCard class="art-table-card" shadow="never">

      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElDropdown @command="handleCreateTask">
            <ElButton type="primary">
              创建任务 <ElIcon><ArrowDown /></ElIcon>
            </ElButton>
            <template #dropdown>
              <ElDropdownMenu>
                <ElDropdownItem command="single">创建单次任务</ElDropdownItem>
                <ElDropdownItem command="batch">创建批次任务</ElDropdownItem>
              </ElDropdownMenu>
            </template>
          </ElDropdown>
          <!-- 移除批量执行按钮 -->
          <ElButton
            v-if="selectedTasks.length > 0"
            @click="handleBatchDelete"
            type="danger"
            style="margin-left: 10px;"
          >
            批量删除
          </ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        :loading="loading"
        :data="taskList"
        :columns="columns"
        :pagination="paginationState"
        @selection-change="handleSelectionChange"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
      </ArtTable>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElButton, ElCard, ElTag, ElLink, ElDropdown, ElDropdownMenu, ElDropdownItem, ElIcon, ElTooltip, ElSwitch } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import ArtTable from '@/components/core/tables/art-table/index.vue'
import ArtTableHeader from '@/components/core/tables/art-table-header/index.vue'
import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'

import TaskSearch from './modules/task-search.vue'
import { useTable } from '@/composables/useTable'
import { useChaosTasksStore } from '@/store/business/chaos/tasks'
import ChaosService from '@/api/chaosApi'
import type { ChaosTask } from '@/types/api/chaos'

defineOptions({ name: 'ChaosTasks' })

const router = useRouter()
const chaosTasksStore = useChaosTasksStore()

/**
 * API适配函数 - 将任务API适配为useTable需要的格式
 * 同时获取单次任务和批次任务
 */
const getTaskListForTable = async (params: any) => {
  try {
    // 同时调用两个接口
    const [singleTaskResponse, batchTaskResponse] = await Promise.all([
      ChaosService.getTaskList(params),
      ChaosService.getBatchTaskList(params)
    ])

    // 为单次任务添加类型标识
    const singleTasks = singleTaskResponse.items.map(task => ({
      ...task,
      task_type: 'single',
      type_label: '单次任务'
    }))

    // 为批次任务添加类型标识
    const batchTasks = batchTaskResponse.items.map(task => ({
      ...task,
      task_type: 'batch',
      type_label: '批次任务'
    }))

    // 合并任务列表
    const allTasks = [...singleTasks, ...batchTasks]

    // 按创建时间排序
    allTasks.sort((a, b) => {
      const timeA = a.created_at ? new Date(a.created_at).getTime() : 0
      const timeB = b.created_at ? new Date(b.created_at).getTime() : 0
      return timeB - timeA
    })

    // 计算总数
    const total = singleTaskResponse.total + batchTaskResponse.total

    return {
      records: allTasks,
      total: total,
      current: params.page || 1,
      size: params.size || 20
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    return {
      records: [],
      total: 0,
      current: 1,
      size: 20
    }
  }
}

// 选中的任务
const selectedTasks = ref<ChaosTask[]>([])

// Switch开关加载状态
const switchLoading = ref(new Set<number>())

// 搜索表单
const searchForm = reactive({
  keyword: '',
  env_ids: [] as number[],
  fault_type: '',
  status: '',
  execution_type: '',
  created_by: ''
})

// 表格配置
const {
  tableData: taskList,
  isLoading: loading,
  paginationState,
  columns,
  columnChecks,
  onPageSizeChange,
  onCurrentPageChange,
  loadData: refreshData,
  searchState,
  searchData
} = useTable({
  core: {
    apiFn: getTaskListForTable,
    apiParams: {
      page: 1,
      size: 20,
      order_by: 'created_at',
      desc: true
    },
    paginationKey: { current: 'page' }, // 使用 'page' 作为分页字段名
    columnsFactory: () => [
      { type: 'selection' as const, width: 55 },
      {
        prop: 'name',
        label: '任务名称',
        minWidth: 150,
        formatter: (row: ChaosTask) => {
          return h(ElLink, {
            type: 'primary',
            onClick: () => handleViewTask(row)
          }, () => row.name)
        }
      },
      {
        prop: 'task_type',
        label: '任务类型',
        width: 100,
        formatter: (row: ChaosTask) => {
          const taskType = (row as any).task_type || 'single'
          return h(ElTag, {
            type: taskType === 'batch' ? 'warning' : 'info',
            size: 'small'
          }, () => taskType === 'batch' ? '批次任务' : '单次任务')
        }
      },
      {
        prop: 'fault_type',
        label: '故障类型',
        width: 120,
        formatter: (row: ChaosTask) => {
          const taskType = (row as any).task_type || 'single'
          if (taskType === 'batch') {
            const taskCount = (row as any).task_count || 0
            return h(ElTag, {
              type: 'warning',
              size: 'small'
            }, () => `${taskCount}个子任务`)
          }
          return h(ElTag, {
            type: getFaultTypeTagType(row.fault_type) as any
          }, () => getFaultTypeLabel(row.fault_type))
        }
      },
      {
        prop: 'environment_names',
        label: '目标环境',
        width: 120,
        formatter: (row: ChaosTask) => {
          const taskType = (row as any).task_type || 'single'

          if (taskType === 'batch') {
            // 批次任务显示单个环境
            const envName = (row as any).environment_name || '-'
            return envName
          }

          // 单次任务显示多个环境
          if (!row.environment_names || row.environment_names.length === 0) {
            return '-'
          }
          if (row.environment_names.length === 1) {
            return row.environment_names[0]
          }
          return `${row.environment_names[0]} 等${row.environment_names.length}个环境`
        }
      },
      {
        prop: 'status',
        label: '运行状态',
        width: 120,
        formatter: (row: ChaosTask) => {
          return h(ElTag, {
            type: getStatusTagType(row.status) as any
          }, () => getStatusLabel(row.status))
        }
      },
      {
        prop: 'task_status',
        label: '任务状态',
        width: 100,
        formatter: (row: ChaosTask) => {
          return h(ElSwitch, {
            modelValue: row.task_status === 'enabled',
            activeText: '启用',
            inactiveText: '禁用',
            inlinePrompt: true,
            loading: switchLoading.value.has(row.id),
            style: {
              '--el-switch-on-color': '#67c23a',
              '--el-switch-off-color': '#f56c6c'
            },
            'onUpdate:modelValue': (value: string | number | boolean) => handleTaskStatusSwitch(row, value as boolean)
          })
        }
      },
      {
        prop: 'execution_type',
        label: '执行类型',
        width: 120,
        formatter: (row: ChaosTask) => getExecutionTypeLabel(row.execution_type)
      },

      {
        prop: 'created_at',
        label: '创建时间',
        width: 150,
        formatter: (row: ChaosTask) => formatDateTime(row.created_at)
      },
      { prop: 'created_by', label: '创建者', width: 200 },
      {
        prop: 'operation',
        label: '操作',
        width: 160,
        fixed: 'right' as const,
        formatter: (row: ChaosTask) => {
          const buttons = []

          if (row.can_execute) {
            buttons.push(
              h(ElTooltip, {
                content: '执行任务', // 悬浮提示内容
                placement: 'top' // 提示位置：top/bottom/left/right
              }, {
                default: () => h(ArtButtonTable, {
                  type: 'test',
                  onClick: () => handleExecuteTask(row)
                }, () => '执行')
              })
            )
          }

          // 停止按钮
          if (row.can_stop) {
            buttons.push(
              h(ArtButtonTable, {
                type: 'stop',
                onClick: () => handleStopTask(row)
              }, () => '停止')
            )
          }

          // 启用/禁用按钮移到下拉菜单中

          // 编辑按钮移到更多菜单中

          // 更多操作下拉菜单
          buttons.push(
            h(ElDropdown, {
              onCommand: (command: string) => handleDropdownCommand(command, row)
            }, {
              default: () => h(ArtButtonTable, { type: 'more' }, () => [
                '更多',
                h(ElIcon, { class: 'el-icon--right' }, () => h(ArrowDown))
              ]),
              dropdown: () => h(ElDropdownMenu, {}, () => [
                h(ElDropdownItem, { command: 'edit' }, () => '编辑任务'),
                h(ElDropdownItem, { command: 'copy' }, () => '复制任务'),
                h(ElDropdownItem, { command: 'executions' }, () => '执行记录'),
                ...(row.status !== 'pending' ? [h(ElDropdownItem, { command: 'reset' }, () => '重置任务')] : []),
                h(ElDropdownItem, { command: 'delete', divided: true }, () => '删除任务')
              ])
            })
          )

          return h('div', { style: 'display: flex; gap: 8px; flex-wrap: wrap;' }, buttons)
        }
      }
    ]
  }
})



// 方法
const refreshAll = () => {
  refreshData()
}

const handleSearch = () => {
  Object.assign(searchState, searchForm)
  searchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    env_ids: [],
    fault_type: '',
    status: '',
    execution_type: '',
    created_by: ''
  })
  Object.assign(searchState, searchForm)
  searchData()
}

const handleCreateTask = (taskType: string) => {
  if (taskType === 'single') {
    router.push('/chaos/tasks/create')
  } else if (taskType === 'batch') {
    router.push('/chaos/tasks/create-batch')
  }
}

const handleViewTask = (task: ChaosTask) => {
  const taskType = (task as any).task_type || 'single'
  if (taskType === 'batch') {
    router.push(`/chaos/batch-tasks/${task.id}`)
  } else {
    router.push(`/chaos/tasks/${task.id}`)
  }
}

const handleExecuteTask = async (task: ChaosTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要执行任务 "${task.name}" 吗？`,
      '确认执行',
      { type: 'warning' }
    )

    let result
    // 根据任务类型调用不同的执行方法
    if (task.task_type === 'batch') {
      // 批次任务执行
      result = await ChaosService.executeBatchTask(task.id, { force: false })
    } else {
      // 单次任务执行
      result = await chaosTasksStore.executeTask(task.id, { force: false })
    }

    ElMessage.success('任务执行成功')
    refreshData()

    // 如果有执行记录，询问是否跳转到详情页面
    if (result?.execution_ids && result.execution_ids.length > 0) {
      try {
        await ElMessageBox.confirm(
          '任务执行成功！是否跳转到执行记录详情页面查看执行结果？',
          '跳转确认',
          {
            type: 'success',
            confirmButtonText: '立即跳转',
            cancelButtonText: '稍后查看'
          }
        )

        // 跳转到第一个执行记录的详情页面
        const executionId = result.execution_ids[0]
        router.push(`/chaos/executions/${executionId}`)
      } catch {
        // 用户选择稍后查看，不做任何操作
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('任务执行失败')
    }
  }
}

const handleStopTask = async (task: ChaosTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要停止任务 "${task.name}" 吗？`,
      '确认停止',
      { type: 'warning' }
    )

    // 根据任务类型调用不同的停止方法
    if (task.task_type === 'batch') {
      // 批次任务停止
      await ChaosService.stopBatchTask(task.id)
    } else {
      // 单次任务停止
      await chaosTasksStore.stopTask(task.id)
    }

    ElMessage.success('任务已停止')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('停止任务失败')
    }
  }
}



// 处理任务状态Switch开关变化
const handleTaskStatusSwitch = async (task: ChaosTask, value: boolean) => {
  // 添加到加载状态
  switchLoading.value.add(task.id)

  try {
    if (value) {
      // 启用任务
      if (task.task_type === 'batch') {
        await ChaosService.enableBatchTask(task.id)
      } else {
        await chaosTasksStore.enableTask(task.id)
      }
      ElMessage.success('任务已启用')
    } else {
      // 禁用任务，需要确认
      await ElMessageBox.confirm(
        `确定要禁用任务 "${task.name}" 吗？禁用后循环任务将停止调度。`,
        '确认禁用',
        { type: 'warning' }
      )

      if (task.task_type === 'batch') {
        await ChaosService.disableBatchTask(task.id)
      } else {
        await chaosTasksStore.disableTask(task.id)
      }
      ElMessage.success('任务已禁用')
    }

    // 刷新数据
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(value ? '启用任务失败' : '禁用任务失败')
    }
    // 如果操作失败，需要恢复Switch状态
    refreshData()
  } finally {
    // 移除加载状态
    switchLoading.value.delete(task.id)
  }
}

const handleResetTask = async (task: ChaosTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置任务 "${task.name}" 吗？重置后任务状态将变为待执行，可以重新执行。`,
      '确认重置',
      { type: 'warning' }
    )

    if (task.task_type === 'batch') {
      await ChaosService.resetBatchTask(task.id)
    } else {
      await chaosTasksStore.resetTask(task.id)
    }

    ElMessage.success('任务已重置')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置任务失败')
    }
  }
}

const handleDropdownCommand = (command: string, task: ChaosTask) => {
  const taskType = (task as any).task_type || 'single'

  switch (command) {
    case 'edit':
      if (taskType === 'batch') {
        router.push(`/chaos/batch-tasks/${task.id}/edit`)
      } else {
        router.push(`/chaos/tasks/${task.id}/edit`)
      }
      break
    case 'copy':
      if (taskType === 'batch') {
        router.push(`/chaos/tasks/create-batch?copy=${task.id}`)
      } else {
        router.push(`/chaos/tasks/create?copy=${task.id}`)
      }
      break
    case 'executions':
      router.push(`/chaos/executions?task_id=${task.id}&task_type=${taskType}`)
      break
    case 'reset':
      handleResetTask(task)
      break
    case 'delete':
      handleDeleteTask(task)
      break
  }
}

const handleDeleteTask = async (task: ChaosTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务 "${task.name}" 吗？此操作不可恢复。`,
      '确认删除',
      { type: 'warning' }
    )

    // 根据任务类型调用不同的删除方法
    if (task.task_type === 'batch') {
      await ChaosService.deleteBatchTask(task.id)
    } else {
      await chaosTasksStore.deleteTask(task.id)
    }

    ElMessage.success('任务删除成功')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除任务失败')
    }
  }
}

const handleSelectionChange = (selection: ChaosTask[]) => {
  selectedTasks.value = selection
}

// 移除批量执行功能

const handleBatchDelete = async () => {
  if (!selectedTasks.value || selectedTasks.value.length === 0) {
    ElMessage.warning('请选择要删除的任务')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedTasks.value.length} 个任务吗？此操作不可恢复。`,
      '确认批量删除',
      { type: 'warning' }
    )

    // 分别处理单次任务和批次任务
    const singleTasks = selectedTasks.value.filter(task => task.task_type !== 'batch')
    const batchTasks = selectedTasks.value.filter(task => task.task_type === 'batch')

    const promises = []

    // 批量删除单次任务
    if (singleTasks.length > 0) {
      const singleTaskIds = singleTasks.map(task => task.id)
      promises.push(chaosTasksStore.batchOperation(singleTaskIds, 'delete'))
    }

    // 逐个删除批次任务
    if (batchTasks.length > 0) {
      batchTasks.forEach(task => {
        promises.push(ChaosService.deleteBatchTask(task.id))
      })
    }

    await Promise.all(promises)
    ElMessage.success('批量删除成功')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}



// 工具方法
const getFaultTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    cpu: 'CPU',
    memory: '内存',
    network: '网络',
    disk: '磁盘',
    process: '进程',
    k8s: 'K8s'
  }
  return labels[type] || type
}

const getFaultTypeTagType = (type: string) => {
  const types: Record<string, string> = {
    cpu: 'danger',
    memory: 'warning',
    network: 'info',
    disk: 'success',
    process: 'primary',
    k8s: ''
  }
  return (types[type] || '') as any
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待执行',
    running: '运行中',
    completed: '已完成',
    failed: '已失败',
    cancelled: '已取消'
  }
  return labels[status] || status
}

const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    running: 'primary',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  return types[status] || 'info'
}



const getExecutionTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    immediate: '立即执行',
    scheduled: '定时执行',
    cron: 'Cron执行'
  }
  return labels[type] || type
}

const formatDateTime = (dateTime: string | undefined) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}


</script>

<style scoped>


</style>
