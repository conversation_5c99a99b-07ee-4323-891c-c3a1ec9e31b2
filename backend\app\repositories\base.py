"""
基础仓储类
专注于数据访问抽象，不包含业务逻辑
"""
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union, Tuple, Callable
from abc import ABC, abstractmethod

from sqlalchemy import func, or_, and_, desc, asc, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload, joinedload
from pydantic import BaseModel

from app.database.base import Base

ModelType = TypeVar("ModelType", bound=Base) # type: ignore
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class QueryBuilder:
    """
    查询构建器
    提供链式查询构建功能
    """

    def __init__(self, model: Type[ModelType], session: AsyncSession):
        self.model = model
        self.session = session
        self.query = select(model)
        self._filters = []
        self._joins = []
        self._order_by = []
        self._limit_value = None
        self._offset_value = None
        self._preload_relations = []

    def filter(self, *conditions) -> 'QueryBuilder':
        """添加过滤条件"""
        self._filters.extend(conditions)
        return self

    def filter_by(self, **kwargs) -> 'QueryBuilder':
        """根据字段值过滤"""
        for field, value in kwargs.items():
            if hasattr(self.model, field):
                self._filters.append(getattr(self.model, field) == value)
        return self

    def search(self, keyword: str, *fields) -> 'QueryBuilder':
        """在指定字段中搜索关键词"""
        if keyword and fields:
            search_conditions = []
            for field in fields:
                if hasattr(self.model, field):
                    search_conditions.append(
                        getattr(self.model, field).ilike(f"%{keyword}%")
                    )
            if search_conditions:
                self._filters.append(or_(*search_conditions))
        return self

    def join(self, *relations) -> 'QueryBuilder':
        """添加JOIN关联"""
        for relation in relations:
            self._joins.append(relation)
        return self

    def order_by(self, field: str, descending: bool = False) -> 'QueryBuilder':
        """添加排序"""
        if hasattr(self.model, field):
            order_func = desc if descending else asc
            self._order_by.append(order_func(getattr(self.model, field)))
        return self

    def limit(self, limit: int) -> 'QueryBuilder':
        """设置限制数量"""
        self._limit_value = limit
        return self

    def offset(self, offset: int) -> 'QueryBuilder':
        """设置偏移量"""
        self._offset_value = offset
        return self

    def preload(self, *relations) -> 'QueryBuilder':
        """预加载关联数据"""
        self._preload_relations.extend(relations)
        return self

    def build(self):
        """构建最终查询"""
        query = self.query

        # 添加JOIN
        for join_relation in self._joins:
            query = query.join(join_relation)

        # 添加过滤条件
        if self._filters:
            query = query.where(and_(*self._filters))

        # 添加排序
        if self._order_by:
            query = query.order_by(*self._order_by)

        # 添加预加载
        if self._preload_relations:
            query = query.options(*[selectinload(rel) for rel in self._preload_relations])

        # 添加分页
        if self._offset_value is not None:
            query = query.offset(self._offset_value)
        if self._limit_value is not None:
            query = query.limit(self._limit_value)

        return query

    async def all(self) -> List[ModelType]:
        """执行查询并返回所有结果"""
        query = self.build()
        result = await self.session.execute(query)
        return result.scalars().all()

    async def first(self) -> Optional[ModelType]:
        """执行查询并返回第一个结果"""
        query = self.build()
        result = await self.session.execute(query)
        return result.scalar_one_or_none()

    async def count(self) -> int:
        """执行计数查询"""
        count_query = select(func.count(self.model.id))
        if self._filters:
            count_query = count_query.where(and_(*self._filters))
        result = await self.session.execute(count_query)
        return result.scalar()

    async def paginate(self, page: int, per_page: int) -> Tuple[List[ModelType], int]:
        """分页查询"""
        total = await self.count()
        offset = (page - 1) * per_page
        items = await self.offset(offset).limit(per_page).all()
        return items, total


class BaseRepository(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    基础仓储类
    提供通用的数据库操作方法
    """

    def __init__(self, model: Type[ModelType], db: AsyncSession):
        """
        初始化仓储

        Args:
            model: SQLAlchemy 模型类
            db: 数据库会话
        """
        self.model = model
        self.db = db

    def query(self) -> QueryBuilder:
        """创建查询构建器"""
        return QueryBuilder(self.model, self.db)

    async def get(self, id: Any) -> Optional[ModelType]:
        """
        根据ID获取单个对象
        
        Args:
            id: 对象ID
            
        Returns:
            模型实例或None
        """
        result = await self.db.execute(
            select(self.model).where(self.model.id == id)
        )
        return result.scalar_one_or_none()

    async def get_by_ids(self, ids: List[int]) -> List[ModelType]:
        """
        根据ID列表批量获取记录

        Args:
            ids: ID列表

        Returns:
            模型实例列表
        """
        if not ids:
            return []
        result = await self.db.execute(
            select(self.model).where(self.model.id.in_(ids))
        )
        return result.scalars().all()

    async def get_by_field(
        self, 
        field_name: str, 
        field_value: Any,
        unique: bool = True
    ) -> Union[Optional[ModelType], List[ModelType]]:
        """
        根据字段值获取对象
        
        Args:
            field_name: 字段名
            field_value: 字段值
            unique: 是否期望唯一结果
            
        Returns:
            模型实例、模型列表或None
        """
        query = select(self.model).where(
            getattr(self.model, field_name) == field_value
        )
        result = await self.db.execute(query)
        
        if unique:
            return result.scalar_one_or_none()
        else:
            return result.scalars().all()

    async def get_multi(
        self,
        *,
        skip: int = 0,
        limit: int = 100,
        order_by: Optional[str] = None,
        desc: bool = False
    ) -> List[ModelType]:
        """
        获取多个对象
        
        Args:
            skip: 跳过的记录数
            limit: 限制的记录数
            order_by: 排序字段
            desc: 是否降序
            
        Returns:
            模型实例列表
        """
        query = select(self.model)
        
        # 添加排序
        if order_by and hasattr(self.model, order_by):
            order_field = getattr(self.model, order_by)
            if desc:
                order_field = order_field.desc()
            query = query.order_by(order_field)
        
        query = query.offset(skip).limit(limit)
        result = await self.db.execute(query)
        return result.scalars().all()

    async def search(
        self,
        *,
        skip: int = 0,
        limit: int = 100,
        keyword: Optional[str] = None,
        search_fields: Optional[List[str]] = None,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        desc: bool = False
    ) -> tuple[List[ModelType], int]:
        """
        搜索对象
        
        Args:
            skip: 跳过的记录数
            limit: 限制的记录数
            keyword: 搜索关键词
            search_fields: 搜索字段列表
            filters: 额外过滤条件
            order_by: 排序字段
            desc: 是否降序
            
        Returns:
            (模型实例列表, 总数)
        """
        query = select(self.model)
        count_query = select(func.count(self.model.id))
        
        # 构建搜索条件
        conditions = []
        
        # 关键词搜索
        if keyword and search_fields:
            search_conditions = []
            for field in search_fields:
                if hasattr(self.model, field):
                    field_attr = getattr(self.model, field)
                    search_conditions.append(field_attr.ilike(f"%{keyword}%"))
            
            if search_conditions:
                conditions.append(or_(*search_conditions))
        
        # 额外过滤条件
        if filters:
            for field, value in filters.items():
                if hasattr(self.model, field):
                    conditions.append(getattr(self.model, field) == value)
        
        # 应用条件
        if conditions:
            combined_conditions = conditions[0] if len(conditions) == 1 else or_(*conditions)
            query = query.where(combined_conditions)
            count_query = count_query.where(combined_conditions)
        
        # 获取总数
        count_result = await self.db.execute(count_query)
        total = count_result.scalar()
        
        # 添加排序
        if order_by and hasattr(self.model, order_by):
            order_field = getattr(self.model, order_by)
            if desc:
                order_field = order_field.desc()
            query = query.order_by(order_field)
        
        # 分页
        query = query.offset(skip).limit(limit)
        result = await self.db.execute(query)
        items = result.scalars().all()
        
        return items, total

    async def create(self, *, obj_in: CreateSchemaType) -> ModelType:
        """
        创建新对象
        
        Args:
            obj_in: 创建数据
            
        Returns:
            创建的模型实例
        """
        obj_data = obj_in.model_dump() if hasattr(obj_in, 'model_dump') else dict(obj_in)
        db_obj = self.model(**obj_data)
        self.db.add(db_obj)
        await self.db.commit()
        await self.db.refresh(db_obj)
        return db_obj

    async def update(
        self,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> ModelType:
        """
        更新对象
        
        Args:
            db_obj: 数据库中的对象
            obj_in: 更新数据
            
        Returns:
            更新后的模型实例
        """
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
        
        for field, value in update_data.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)
        
        self.db.add(db_obj)
        await self.db.commit()
        await self.db.refresh(db_obj)
        return db_obj

    async def delete(self, *, id: Any) -> Optional[ModelType]:
        """
        删除对象
        
        Args:
            id: 对象ID
            
        Returns:
            删除的模型实例或None
        """
        obj = await self.get(id)
        if obj:
            await self.db.delete(obj)
            await self.db.commit()
        return obj

    async def count(self, **filters) -> int:
        """
        获取记录总数
        
        Args:
            **filters: 过滤条件
            
        Returns:
            总记录数
        """
        query = select(func.count(self.model.id))
        
        if filters:
            conditions = []
            for field, value in filters.items():
                if hasattr(self.model, field):
                    conditions.append(getattr(self.model, field) == value)
            
            if conditions:
                combined_conditions = conditions[0] if len(conditions) == 1 else or_(*conditions)
                query = query.where(combined_conditions)
        
        result = await self.db.execute(query)
        return result.scalar()

    async def exists(self, **filters) -> bool:
        """
        检查记录是否存在

        Args:
            **filters: 过滤条件

        Returns:
            是否存在
        """
        count = await self.count(**filters)
        return count > 0

    # ==================== 批量操作方法 ====================

    async def create_many(self, *, objs_in: List[CreateSchemaType]) -> List[ModelType]:
        """
        批量创建对象

        Args:
            objs_in: 创建数据列表

        Returns:
            创建的对象列表
        """
        db_objs = []
        for obj_in in objs_in:
            obj_data = obj_in.model_dump()
            db_obj = self.model(**obj_data)
            db_objs.append(db_obj)

        self.db.add_all(db_objs)
        await self.db.commit()

        # 刷新所有对象以获取ID
        for db_obj in db_objs:
            await self.db.refresh(db_obj)

        return db_objs

    async def update_many(
        self,
        *,
        filters: Dict[str, Any],
        update_data: Dict[str, Any]
    ) -> int:
        """
        批量更新对象

        Args:
            filters: 过滤条件
            update_data: 更新数据

        Returns:
            更新的记录数
        """
        from sqlalchemy import update

        # 构建更新查询
        stmt = update(self.model)

        # 添加过滤条件
        conditions = []
        for field, value in filters.items():
            if hasattr(self.model, field):
                conditions.append(getattr(self.model, field) == value)

        if conditions:
            stmt = stmt.where(and_(*conditions))

        # 设置更新值
        stmt = stmt.values(**update_data)

        result = await self.db.execute(stmt)
        await self.db.commit()

        return result.rowcount

    async def delete_many(self, *, filters: Dict[str, Any]) -> int:
        """
        批量删除对象

        Args:
            filters: 过滤条件

        Returns:
            删除的记录数
        """
        from sqlalchemy import delete

        # 构建删除查询
        stmt = delete(self.model)

        # 添加过滤条件
        conditions = []
        for field, value in filters.items():
            if hasattr(self.model, field):
                conditions.append(getattr(self.model, field) == value)

        if conditions:
            stmt = stmt.where(and_(*conditions))

        result = await self.db.execute(stmt)
        await self.db.commit()

        return result.rowcount

    # ==================== 查询优化方法 ====================

    async def get_multi_with_total(
        self,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        desc: bool = False,
        preload: Optional[List[str]] = None
    ) -> Tuple[List[ModelType], int]:
        """
        获取分页数据和总数（优化版本）

        Args:
            skip: 跳过记录数
            limit: 限制记录数
            filters: 过滤条件
            order_by: 排序字段
            desc: 是否降序
            preload: 预加载关系

        Returns:
            (记录列表, 总数)
        """
        builder = self.query()

        # 添加过滤条件
        if filters:
            builder = builder.filter_by(**filters)

        # 添加排序
        if order_by:
            builder = builder.order_by(order_by, desc)

        # 添加预加载
        if preload:
            builder = builder.preload(*preload)

        # 获取总数和分页数据
        total = await builder.count()
        records = await builder.offset(skip).limit(limit).all()

        return records, total

    async def find_by_ids(self, ids: List[Any]) -> List[ModelType]:
        """
        根据ID列表批量查询

        Args:
            ids: ID列表

        Returns:
            对象列表
        """
        if not ids:
            return []

        query = select(self.model).where(self.model.id.in_(ids))
        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_or_create(
        self,
        *,
        defaults: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Tuple[ModelType, bool]:
        """
        获取或创建对象

        Args:
            defaults: 创建时的默认值
            **kwargs: 查询条件

        Returns:
            (对象, 是否新创建)
        """
        # 尝试获取现有对象
        obj = await self.query().filter_by(**kwargs).first()

        if obj:
            return obj, False

        # 创建新对象
        create_data = {**kwargs}
        if defaults:
            create_data.update(defaults)

        db_obj = self.model(**create_data)
        self.db.add(db_obj)
        await self.db.commit()
        await self.db.refresh(db_obj)

        return db_obj, True