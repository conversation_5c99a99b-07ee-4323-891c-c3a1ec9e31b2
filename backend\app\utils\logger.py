"""
日志工具模块
"""
import logging
import sys
import time
from datetime import datetime
from typing import Any, Dict, Optional, Union
from fastapi import Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings


def setup_logger() -> logging.Logger:
    """
    设置应用日志器
    """
    logger = logging.getLogger(settings.app_name)
    logger.setLevel(getattr(logging, settings.log_level.upper()))

    # 避免重复添加handler
    if not logger.handlers:
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)

        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)

        logger.addHandler(console_handler)

    return logger


class OperationLogger:
    """
    操作日志记录器 - 增强版
    支持业务操作、审计日志、性能监控等功能
    """

    def __init__(self):
        self.logger = setup_logger()

    async def log_operation(
        self,
        operation: str,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        request_data: Optional[Dict[str, Any]] = None,
        result: str = "success",
        error_msg: Optional[str] = None,
        duration_ms: Optional[float] = None
    ) -> None:
        """
        记录操作日志

        Args:
            operation: 操作类型
            user_id: 用户ID
            username: 用户名
            request_data: 请求数据
            result: 操作结果
            error_msg: 错误消息
            duration_ms: 操作耗时(毫秒)
        """
        from app.utils.timezone import now
        log_data = {
            "timestamp": now().isoformat(),
            "operation": operation,
            "user_id": user_id,
            "username": username,
            "request_data": request_data,
            "result": result,
            "error_msg": error_msg,
            "duration_ms": duration_ms
        }

        if result == "success":
            self.logger.info(f"操作成功: {operation} | 用户: {username}({user_id}) | 耗时: {duration_ms}ms")
        else:
            self.logger.error(f"操作失败: {operation} | 用户: {username}({user_id}) | 错误: {error_msg}")
    
    async def log_login(
        self,
        username: str,
        ip_address: str,
        user_agent: str,
        success: bool = True,
        error_msg: Optional[str] = None
    ) -> None:
        """
        记录登录日志
        """
        await self.log_operation(
            operation="user_login",
            username=username,
            request_data={
                "ip_address": ip_address,
                "user_agent": user_agent
            },
            result="success" if success else "failed",
            error_msg=error_msg
        )
    
    async def log_crud_operation(
        self,
        operation: str,
        model_name: str,
        object_id: Optional[int] = None,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        changes: Optional[Dict[str, Any]] = None,
        duration_ms: Optional[float] = None
    ) -> None:
        """
        记录CRUD操作日志
        """
        await self.log_operation(
            operation=f"{operation}_{model_name.lower()}",
            user_id=user_id,
            username=username,
            request_data={
                "model": model_name,
                "object_id": object_id,
                "changes": changes
            },
            duration_ms=duration_ms
        )

    async def log_business_operation(
        self,
        module: str,
        operation: str,
        user_id: Optional[int] = None,
        username: Optional[str] = None,
        target_id: Optional[Union[int, str]] = None,
        target_name: Optional[str] = None,
        operation_data: Optional[Dict[str, Any]] = None,
        result: str = "success",
        error_msg: Optional[str] = None,
        duration_ms: Optional[float] = None
    ) -> None:
        """
        记录业务操作日志

        Args:
            module: 模块名称 (user/env/model/chaos)
            operation: 操作类型 (create/update/delete/execute/test等)
            user_id: 操作用户ID
            username: 操作用户名
            target_id: 目标对象ID
            target_name: 目标对象名称
            operation_data: 操作相关数据
            result: 操作结果
            error_msg: 错误消息
            duration_ms: 操作耗时
        """
        await self.log_operation(
            operation=f"{module}_{operation}",
            user_id=user_id,
            username=username,
            request_data={
                "module": module,
                "target_id": target_id,
                "target_name": target_name,
                "operation_data": operation_data
            },
            result=result,
            error_msg=error_msg,
            duration_ms=duration_ms
        )

    async def log_audit_operation(
        self,
        operation: str,
        user_id: int,
        username: str,
        resource_type: str,
        resource_id: Optional[Union[int, str]] = None,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None
    ) -> None:
        """
        记录审计日志 - 用于重要操作的审计追踪

        Args:
            operation: 操作类型
            user_id: 用户ID
            username: 用户名
            resource_type: 资源类型
            resource_id: 资源ID
            old_values: 修改前的值
            new_values: 修改后的值
            ip_address: 客户端IP
        """
        audit_data = {
            "resource_type": resource_type,
            "resource_id": resource_id,
            "old_values": old_values,
            "new_values": new_values,
            "ip_address": ip_address
        }

        self.logger.warning(f"[AUDIT] {operation} | 用户: {username}({user_id}) | 资源: {resource_type}({resource_id}) | IP: {ip_address}")

        await self.log_operation(
            operation=f"audit_{operation}",
            user_id=user_id,
            username=username,
            request_data=audit_data,
            result="success"
        )


class BusinessLogger:
    """
    业务日志记录器 - 为各个业务模块提供专用的日志记录方法
    """

    def __init__(self, module_name: str):
        self.module_name = module_name
        self.operation_logger = operation_logger
        self.logger = setup_logger()

    async def log_create(
        self,
        target_name: str,
        user_id: int,
        username: str,
        target_id: Optional[Union[int, str]] = None,
        data: Optional[Dict[str, Any]] = None,
        duration_ms: Optional[float] = None
    ) -> None:
        """记录创建操作"""
        await self.operation_logger.log_business_operation(
            module=self.module_name,
            operation="create",
            user_id=user_id,
            username=username,
            target_id=target_id,
            target_name=target_name,
            operation_data=data,
            duration_ms=duration_ms
        )

    async def log_update(
        self,
        target_name: str,
        user_id: int,
        username: str,
        target_id: Union[int, str],
        changes: Optional[Dict[str, Any]] = None,
        duration_ms: Optional[float] = None
    ) -> None:
        """记录更新操作"""
        await self.operation_logger.log_business_operation(
            module=self.module_name,
            operation="update",
            user_id=user_id,
            username=username,
            target_id=target_id,
            target_name=target_name,
            operation_data={"changes": changes},
            duration_ms=duration_ms
        )

    async def log_delete(
        self,
        target_name: str,
        user_id: int,
        username: str,
        target_id: Union[int, str],
        duration_ms: Optional[float] = None
    ) -> None:
        """记录删除操作"""
        await self.operation_logger.log_business_operation(
            module=self.module_name,
            operation="delete",
            user_id=user_id,
            username=username,
            target_id=target_id,
            target_name=target_name,
            duration_ms=duration_ms
        )

    async def log_execute(
        self,
        action: str,
        user_id: int,
        username: str,
        target_id: Optional[Union[int, str]] = None,
        target_name: Optional[str] = None,
        params: Optional[Dict[str, Any]] = None,
        result: str = "success",
        error_msg: Optional[str] = None,
        duration_ms: Optional[float] = None
    ) -> None:
        """记录执行操作"""
        await self.operation_logger.log_business_operation(
            module=self.module_name,
            operation=f"execute_{action}",
            user_id=user_id,
            username=username,
            target_id=target_id,
            target_name=target_name,
            operation_data=params,
            result=result,
            error_msg=error_msg,
            duration_ms=duration_ms
        )


def get_business_logger(module_name: str) -> BusinessLogger:
    """获取业务日志记录器"""
    return BusinessLogger(module_name)


# 创建全局操作日志器实例
operation_logger = OperationLogger()