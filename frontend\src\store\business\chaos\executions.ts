/**
 * 混沌测试执行记录状态管理
 * 专门管理执行记录相关的状态和操作
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { usePagination } from '@/store/shared/pagination'
import { useLoading } from '@/store/shared/loading'
import { globalCache } from '@/store/shared/cache'
import ChaosService from '@/api/chaosApi'
import type {
  ChaosExecution,
  ChaosExecutionSearchParams,
  ChaosExecutionRetryRequest,
  ChaosExecutionBatchRequest
} from '@/types/api/chaos'

export const useChaosExecutionsStore = defineStore('chaos-executions', () => {
  // ==================== 状态定义 ====================
  
  const executions = ref<ChaosExecution[]>([])
  const currentExecution = ref<ChaosExecution | null>(null)
  
  // 分页和加载状态
  const pagination = usePagination({ defaultSize: 20 })
  const loading = useLoading()
  const logLoading = useLoading()
  const monitorLoading = useLoading()

  // ==================== 计算属性 ====================
  
  const totalExecutions = computed(() => pagination.pagination.total)
  
  const runningExecutions = computed(() => 
    executions.value.filter(execution => execution.status === 'running')
  )
  
  const successExecutions = computed(() => 
    executions.value.filter(execution => execution.status === 'success')
  )
  
  const failedExecutions = computed(() => 
    executions.value.filter(execution => execution.status === 'failed')
  )

  const recentExecutions = computed(() => 
    executions.value.slice(0, 10) // 最近10条执行记录
  )

  // ==================== 执行记录管理方法 ====================
  
  /**
   * 获取执行记录列表
   */
  const fetchExecutions = async (params: ChaosExecutionSearchParams = {}) => {
    const queryParams = {
      ...params,
      ...pagination.params.value
    }

    return await loading.withLoading(async () => {
      const response = await ChaosService.getExecutionList(queryParams)
      
      executions.value = response.items || response.records || []
      pagination.updateFromResponse(response)
      
      return response
    })
  }
  
  /**
   * 获取执行记录详情
   */
  const fetchExecution = async (id: number) => {
    // 先尝试从缓存获取
    const cacheKey = `execution_detail_${id}`
    const cached = globalCache.get(cacheKey)
    if (cached) {
      currentExecution.value = cached
      return cached
    }

    return await loading.withLoading(async () => {
      const response = await ChaosService.getExecutionDetail(id)
      
      currentExecution.value = response
      
      // 缓存执行详情
      globalCache.set(cacheKey, response, 3 * 60 * 1000) // 3分钟缓存
      
      return response
    })
  }
  
  /**
   * 删除执行记录
   */
  const deleteExecution = async (id: number) => {
    return await loading.withLoading(async () => {
      await ChaosService.deleteExecution(id)
      
      // 从本地状态中移除
      const index = executions.value.findIndex(execution => execution.id === id)
      if (index !== -1) {
        executions.value.splice(index, 1)
        pagination.setTotal(pagination.pagination.total - 1)
      }
      
      if (currentExecution.value?.id === id) {
        currentExecution.value = null
      }
      
      // 清除相关缓存
      globalCache.remove(`execution_detail_${id}`)
      globalCache.remove(`execution_log_${id}`)
      globalCache.remove(`execution_monitor_${id}`)
    })
  }
  
  /**
   * 获取任务的执行记录
   */
  const fetchTaskExecutions = async (taskId: number, page = 1, size = 20) => {
    const cacheKey = `task_executions_${taskId}_${page}_${size}`
    const cached = globalCache.get(cacheKey)
    if (cached) {
      return cached
    }

    return await loading.withLoading(async () => {
      const response = await ChaosService.getTaskExecutions(taskId, page, size)
      
      // 缓存任务执行记录
      globalCache.set(cacheKey, response, 2 * 60 * 1000) // 2分钟缓存
      
      return response
    })
  }
  
  /**
   * 获取执行日志
   */
  const getExecutionLog = async (executionId: number, logType = 'output') => {
    const cacheKey = `execution_log_${executionId}_${logType}`
    const cached = globalCache.get(cacheKey)
    if (cached) {
      return cached
    }

    return await logLoading.withLoading(async () => {
      const response = await ChaosService.getExecutionLog(executionId, logType)
      
      // 缓存日志（较短时间，因为日志可能会更新）
      globalCache.set(cacheKey, response, 30 * 1000) // 30秒缓存
      
      return response
    })
  }

  /**
   * 获取执行监控信息
   */
  const getExecutionMonitor = async (executionId: number) => {
    const cacheKey = `execution_monitor_${executionId}`
    const cached = globalCache.get(cacheKey)
    if (cached) {
      return cached
    }

    return await monitorLoading.withLoading(async () => {
      const response = await ChaosService.getExecutionMonitor(executionId)
      
      // 缓存监控信息（较短时间）
      globalCache.set(cacheKey, response, 15 * 1000) // 15秒缓存
      
      return response
    })
  }

  /**
   * 获取执行统计
   */
  const getExecutionStatistics = async (taskId?: number) => {
    const cacheKey = taskId ? `execution_statistics_${taskId}` : 'execution_statistics_all'
    const cached = globalCache.get(cacheKey)
    if (cached) {
      return cached
    }

    return await loading.withLoading(async () => {
      const response = await ChaosService.getExecutionStatistics(taskId)
      
      // 缓存统计信息
      globalCache.set(cacheKey, response, 2 * 60 * 1000) // 2分钟缓存
      
      return response
    })
  }
  
  /**
   * 重试执行
   */
  const retryExecution = async (id: number, data: ChaosExecutionRetryRequest) => {
    return await loading.withLoading(async () => {
      const response = await ChaosService.retryExecution(id, data)
      
      // 更新本地状态
      const index = executions.value.findIndex(execution => execution.id === id)
      if (index !== -1) {
        executions.value[index] = response
      }
      
      if (currentExecution.value?.id === id) {
        currentExecution.value = response
      }
      
      // 清除相关缓存
      globalCache.remove(`execution_detail_${id}`)
      
      return response
    })
  }

  /**
   * 取消执行
   */
  const cancelExecution = async (id: number) => {
    return await loading.withLoading(async () => {
      await ChaosService.cancelExecution(id)
      
      // 更新执行状态
      updateExecutionStatus(id, 'cancelled')
    })
  }

  /**
   * 批量操作执行记录
   */
  const batchExecutionOperation = async (executionIds: number[], action: string) => {
    const data: ChaosExecutionBatchRequest = { execution_ids: executionIds, action }
    
    return await loading.withLoading(async () => {
      const response = await ChaosService.batchExecutionOperation(data)
      
      // 根据操作类型更新本地状态
      if (action === 'delete') {
        executions.value = executions.value.filter(execution => !executionIds.includes(execution.id))
        pagination.setTotal(pagination.pagination.total - executionIds.length)
      } else if (action === 'cancel') {
        executionIds.forEach(id => updateExecutionStatus(id, 'cancelled'))
      }
      
      // 清除相关缓存
      executionIds.forEach(id => {
        globalCache.remove(`execution_detail_${id}`)
        globalCache.remove(`execution_log_${id}`)
        globalCache.remove(`execution_monitor_${id}`)
      })
      
      return response
    })
  }

  // ==================== 辅助方法 ====================
  
  /**
   * 更新执行状态
   */
  const updateExecutionStatus = (id: number, status: string) => {
    const execution = executions.value.find(e => e.id === id)
    if (execution) {
      execution.status = status
    }
    
    if (currentExecution.value?.id === id) {
      currentExecution.value.status = status
    }
    
    // 清除相关缓存
    globalCache.remove(`execution_detail_${id}`)
  }

  /**
   * 实时更新执行状态
   */
  const refreshExecutionStatus = async (id: number) => {
    try {
      const execution = await ChaosService.getExecutionDetail(id)
      updateExecutionStatus(id, execution.status)
      return execution
    } catch (error) {
      console.error('Failed to refresh execution status:', error)
      return null
    }
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    executions.value = []
    currentExecution.value = null
    pagination.reset()
    loading.reset()
    logLoading.reset()
    monitorLoading.reset()
  }

  return {
    // 状态
    executions: readonly(executions),
    currentExecution: readonly(currentExecution),
    pagination,
    loading,
    logLoading,
    monitorLoading,
    
    // 计算属性
    totalExecutions,
    runningExecutions,
    successExecutions,
    failedExecutions,
    recentExecutions,
    
    // 方法
    fetchExecutions,
    fetchExecution,
    deleteExecution,
    fetchTaskExecutions,
    getExecutionLog,
    getExecutionMonitor,
    getExecutionStatistics,
    retryExecution,
    cancelExecution,
    batchExecutionOperation,
    refreshExecutionStatus,
    resetState
  }
})
