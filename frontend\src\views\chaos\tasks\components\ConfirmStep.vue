<template>
  <div class="confirm-step">
    <div class="confirm-container">
      <div class="confirm-header">
        <el-icon class="confirm-icon"><CircleCheck /></el-icon>
        <h3>确认任务配置</h3>
        <p>请仔细检查以下配置信息，确认无误后提交任务</p>
      </div>

      <div class="confirm-content">
        <!-- 基本信息 -->
        <div class="info-section">
          <h4 class="section-title">基本信息</h4>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">任务名称</span>
              <span class="value">{{ formData.name }}</span>
            </div>
            <div class="info-item">
              <span class="label">任务描述</span>
              <span class="value">{{ formData.description || '无' }}</span>
            </div>

            <!-- 单次任务：多环境 -->
            <template v-if="!isBatch">
              <div class="info-item">
                <span class="label">目标环境</span>
                <span class="value">{{ getEnvironmentNames(formData.env_ids || []) }}</span>
              </div>
              <div class="info-item">
                <span class="label">环境数量</span>
                <span class="value">{{ (formData.env_ids || []).length }} 个</span>
              </div>
            </template>

            <!-- 批次任务：单环境 -->
            <template v-else>
              <div class="info-item">
                <span class="label">目标环境</span>
                <span class="value">{{ getSingleEnvironmentName(formData.env_id) }}</span>
              </div>
              <div class="info-item">
                <span class="label">执行模式</span>
                <span class="value">{{ getExecutionModeLabel(formData.batch_execution_mode) }}</span>
              </div>
              <div class="info-item" v-if="formData.batch_execution_mode === 'sequential' && formData.wait_time">
                <span class="label">等待时间</span>
                <span class="value">{{ formData.wait_time }}秒</span>
              </div>
              <div class="info-item">
                <span class="label">子任务数量</span>
                <span class="value">{{ (formData.task_items || []).length }} 个</span>
              </div>
            </template>
          </div>
        </div>

        <!-- 单次任务：故障配置 -->
        <div v-if="!isBatch" class="info-section">
          <h4 class="section-title">故障配置</h4>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">故障类型</span>
              <span class="value">{{ getFaultTypeLabel(formData.fault_type || '') }}</span>
            </div>
            <div class="info-item full-width">
              <span class="label">故障参数</span>
              <div class="params-display">
                <div
                  v-for="(value, key) in (formData.fault_params || {})"
                  :key="key"
                  class="param-item"
                >
                  <span class="param-key">{{ key }}</span>
                  <span class="param-value">{{ formatParamValue(value) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 批次任务：子任务配置 -->
        <div v-else class="info-section">
          <h4 class="section-title">子任务配置</h4>
          <div class="task-items-display">
            <div
              v-for="(item, index) in (formData.task_items || [])"
              :key="index"
              class="task-item-display"
            >
              <div class="task-item-header">
                <span class="task-item-title">子任务 {{ index + 1 }}</span>
                <span class="task-item-name">{{ item.name }}</span>
              </div>
              <div class="task-item-details">
                <div class="task-detail-item">
                  <span class="label">故障类型</span>
                  <span class="value">{{ getFaultTypeLabel(item.fault_type || '') }}</span>
                </div>
                <div class="task-detail-item">
                  <span class="label">描述</span>
                  <span class="value">{{ item.description || '无' }}</span>
                </div>
                <div class="task-detail-item full-width">
                  <span class="label">故障参数</span>
                  <div class="params-display">
                    <div
                      v-for="(value, key) in (item.fault_params || {})"
                      :key="key"
                      class="param-item"
                    >
                      <span class="param-key">{{ key }}</span>
                      <span class="param-value">{{ formatParamValue(value) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 执行计划 -->
        <div class="info-section">
          <h4 class="section-title">执行计划</h4>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">执行方式</span>
              <span class="value">{{ getExecutionTypeLabel(formData.execution_type) }}</span>
            </div>
            <div v-if="formData.execution_type === 'scheduled'" class="info-item">
              <span class="label">执行时间</span>
              <span class="value">{{ formData.scheduled_time || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">自动销毁</span>
              <span class="value">{{ formData.auto_destroy ? '开启' : '关闭' }}</span>
            </div>
            <div v-if="formData.auto_destroy" class="info-item">
              <span class="label">最大时长</span>
              <span class="value">{{ formData.max_duration }} 秒</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 风险提示 -->
      <div class="risk-warning">
        <el-alert
          title="风险提示"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <ul class="warning-list">
              <li>混沌测试会对目标环境造成故障影响，请确保在测试环境中执行</li>
              <li>请确认目标环境中没有重要的生产业务正在运行</li>
              <li>建议在业务低峰期执行混沌测试，避免影响正常业务</li>
              <li>执行前请确保已做好数据备份和回滚准备</li>
            </ul>
          </template>
        </el-alert>
      </div>

      <!-- 确认选项 -->
      <div class="confirm-options">
        <el-checkbox v-model="confirmed" size="large">
          我已仔细阅读上述配置和风险提示，确认执行此混沌测试任务
        </el-checkbox>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { CircleCheck } from '@element-plus/icons-vue'
import type { EnvironmentResponse } from '@/types/api/environment'
import type { ChaosScenario } from '@/types/api/chaos'

interface Props {
  formData: {
    name: string
    description?: string
    env_ids?: number[]
    env_id?: number
    fault_type?: string
    fault_params?: Record<string, any>
    task_items?: any[]
    batch_execution_mode?: string
    wait_time?: number
    execution_type?: string
    scheduled_time?: string
    auto_destroy?: boolean
    max_duration?: number
  }
  environments: EnvironmentResponse[]
  scenarios: ChaosScenario[]
  isBatch?: boolean
}

const props = defineProps<Props>()

const confirmed = ref(false)

// 计算属性
const isBatch = computed(() => props.isBatch || false)

// 获取环境名称
const getEnvironmentNames = (envIds: number[]) => {
  if (!envIds || envIds.length === 0) return '-'
  if (!props.environments || props.environments.length === 0) return '加载中...'

  const names = envIds.map(id => {
    const env = props.environments.find(e => e.id === id)
    return env?.name || `环境${id}`
  })
  return names.join(', ')
}

// 获取单个环境名称（批次任务）
const getSingleEnvironmentName = (envId?: number) => {
  if (!envId) return '无'

  const env = props.environments.find(e => e.id === envId)
  return env ? env.name : `环境${envId}`
}

// 获取执行模式标签
const getExecutionModeLabel = (mode?: string) => {
  const labels = {
    sequential: '间隔执行',
    order: '连续执行'
  }
  return labels[mode as keyof typeof labels] || mode || '未设置'
}

// 获取故障类型标签
const getFaultTypeLabel = (faultType: string) => {
  const scenario = props.scenarios.find(s => s.fault_type === faultType)
  return scenario?.name || faultType
}

// 获取执行方式标签
const getExecutionTypeLabel = (executionType?: string) => {
  if (!executionType) return '-'
  const labels: Record<string, string> = {
    immediate: '立即执行',
    scheduled: '定时执行'
  }
  return labels[executionType] || executionType
}

// 格式化参数值
const formatParamValue = (value: any) => {
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  return String(value)
}

// 检查是否可以提交
const canSubmit = () => {
  // 只检查确认框是否勾选
  return confirmed.value
}

defineExpose({
  canSubmit
})
</script>

<style scoped>
.confirm-step {
  padding: 10px 0;
}

.confirm-container {
  max-width: 800px;
  margin: 0 auto;
}

.confirm-header {
  text-align: center;
  margin-bottom: 40px;
}

.confirm-icon {
  font-size: 48px;
  color: #67c23a;
  margin-bottom: 16px;
}

.confirm-header h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.confirm-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.confirm-content {
  margin-bottom: 30px;
}

.info-section {
  margin-bottom: 30px;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color);
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.info-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  align-items: stretch;
}

.label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
}

.value {
  color: #303133;
  word-break: break-all;
}

.params-display {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 8px;
}

.param-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--el-bg-color);
  border-radius: 4px;
  border: 1px solid var(--el-border-color);
}

.param-key {
  font-weight: 500;
  color: #606266;
}

.param-value {
  color: #303133;
  font-family: monospace;
}

.risk-warning {
  margin-bottom: 30px;
}

.warning-list {
  margin: 0;
  padding-left: 20px;
}

.warning-list li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.confirm-options {
  text-align: center;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 2px dashed var(--el-border-color);
}

/* 批次任务样式 */
.task-items-display {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.task-item-display {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  background: var(--el-bg-color);
  overflow: hidden;
}

.task-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.task-item-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.task-item-name {
  color: var(--el-text-color-regular);
  font-size: 13px;
}

.task-item-details {
  padding: 16px;
}

.task-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.task-detail-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.task-detail-item.full-width {
  flex-direction: column;
  align-items: stretch;
}

.task-detail-item.full-width .label {
  margin-bottom: 8px;
}
</style>
