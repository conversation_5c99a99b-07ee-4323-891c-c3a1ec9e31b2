/**
 * 环境管理状态管理
 * 迁移自 src/stores/environment/index.ts
 * 管理环境配置列表、统计信息、连接测试等功能
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { usePagination } from '@/store/shared/pagination'
import { useLoading } from '@/store/shared/loading'
import { globalCache } from '@/store/shared/cache'
import { EnvironmentService } from '@/api/envApi'
import type { 
  EnvironmentResponse, 
  EnvironmentCreateRequest, 
  EnvironmentUpdateRequest,
  EnvironmentListParams,
  EnvironmentStats,
  ConnectionTestResponse
} from '@/types/api/environment'

export const useEnvironmentStore = defineStore('environment', () => {
  // ==================== 状态定义 ====================
  
  /** 环境配置列表 */
  const environments = ref<EnvironmentResponse[]>([])
  
  /** 当前选中的环境配置 */
  const currentEnvironment = ref<EnvironmentResponse | null>(null)
  
  /** 可用环境列表 */
  const availableEnvironments = ref<EnvironmentResponse[]>([])
  
  /** 环境统计信息 */
  const environmentStats = ref<EnvironmentStats | null>(null)
  
  /** 连接测试结果 */
  const connectionTestResults = ref<ConnectionTestResponse[]>([])
  
  // 分页和加载状态
  const pagination = usePagination({ defaultSize: 20 })
  const loading = useLoading()
  const statsLoading = useLoading()
  const testLoading = useLoading()

  // ==================== 计算属性 ====================
  
  const totalEnvironments = computed(() => pagination.pagination.total)
  
  const activeEnvironments = computed(() =>
    environments.value.filter(env => env.status === 'connected')
  )

  const inactiveEnvironments = computed(() =>
    environments.value.filter(env => env.status === 'failed' || env.status === 'unknown')
  )

  const environmentsByType = computed(() => {
    const grouped: Record<string, EnvironmentResponse[]> = {}
    environments.value.forEach(env => {
      if (!grouped[env.type]) {
        grouped[env.type] = []
      }
      grouped[env.type].push(env)
    })
    return grouped
  })

  // ==================== 环境管理方法 ====================
  
  /**
   * 获取环境列表
   */
  const fetchEnvironments = async (params: EnvironmentListParams = {}) => {
    const queryParams = {
      ...params,
      ...pagination.params.value
    }

    return await loading.withLoading(async () => {
      const response = await EnvironmentService.getEnvironmentList(queryParams)
      
      environments.value = response.items
      pagination.updateFromResponse(response)
      
      return response
    })
  }

  /**
   * 创建环境配置
   */
  const createEnvironment = async (data: EnvironmentCreateRequest) => {
    return await loading.withLoading(async () => {
      const response = await EnvironmentService.createEnvironment(data)
      
      // 添加到列表开头
      environments.value.unshift(response)
      pagination.setTotal(pagination.pagination.total + 1)
      
      // 清除相关缓存
      globalCache.remove('environment_stats')
      globalCache.remove('available_environments')
      
      return response
    })
  }

  /**
   * 获取环境详情
   */
  const fetchEnvironment = async (id: number) => {
    // 先尝试从缓存获取
    const cacheKey = `environment_detail_${id}`
    const cached = globalCache.get(cacheKey)
    if (cached) {
      currentEnvironment.value = cached
      return cached
    }

    return await loading.withLoading(async () => {
      const response = await EnvironmentService.getEnvironmentDetail(id)
      
      currentEnvironment.value = response
      
      // 缓存环境详情
      globalCache.set(cacheKey, response, 5 * 60 * 1000) // 5分钟缓存
      
      return response
    })
  }

  /**
   * 更新环境配置
   */
  const updateEnvironment = async (id: number, data: EnvironmentUpdateRequest) => {
    return await loading.withLoading(async () => {
      const response = await EnvironmentService.updateEnvironment(id, data)
      
      // 更新本地状态
      const index = environments.value.findIndex(env => env.id === id)
      if (index !== -1) {
        environments.value[index] = response
      }
      
      if (currentEnvironment.value?.id === id) {
        currentEnvironment.value = response
      }
      
      // 清除相关缓存
      globalCache.remove(`environment_detail_${id}`)
      globalCache.remove('available_environments')
      
      return response
    })
  }

  /**
   * 删除环境配置
   */
  const deleteEnvironment = async (id: number) => {
    return await loading.withLoading(async () => {
      await EnvironmentService.deleteEnvironment(id)
      
      // 从本地状态中移除
      const index = environments.value.findIndex(env => env.id === id)
      if (index !== -1) {
        environments.value.splice(index, 1)
        pagination.setTotal(pagination.pagination.total - 1)
      }
      
      if (currentEnvironment.value?.id === id) {
        currentEnvironment.value = null
      }
      
      // 清除相关缓存
      globalCache.remove(`environment_detail_${id}`)
      globalCache.remove('environment_stats')
      globalCache.remove('available_environments')
    })
  }

  /**
   * 获取可用环境列表
   */
  const fetchAvailableEnvironments = async () => {
    // 先尝试从缓存获取
    const cached = globalCache.get('available_environments')
    if (cached) {
      availableEnvironments.value = cached
      return cached
    }

    return await loading.withLoading(async () => {
      const response = await EnvironmentService.getAvailableEnvironments()
      
      availableEnvironments.value = response
      
      // 缓存可用环境列表
      globalCache.set('available_environments', response, 10 * 60 * 1000) // 10分钟缓存
      
      return response
    })
  }

  /**
   * 获取环境统计信息
   */
  const fetchEnvironmentStats = async () => {
    // 先尝试从缓存获取
    const cached = globalCache.get('environment_stats')
    if (cached) {
      environmentStats.value = cached
      return cached
    }

    return await statsLoading.withLoading(async () => {
      const response = await EnvironmentService.getEnvironmentStats()
      
      environmentStats.value = response
      
      // 缓存统计信息
      globalCache.set('environment_stats', response, 2 * 60 * 1000) // 2分钟缓存
      
      return response
    })
  }

  /**
   * 测试环境连接
   */
  const testConnection = async (id: number) => {
    return await testLoading.withLoading(async () => {
      const response = await EnvironmentService.testEnvironmentConnection(id)

      // 更新连接测试结果
      const existingIndex = connectionTestResults.value.findIndex(result => result.env_id === id)
      if (existingIndex !== -1) {
        connectionTestResults.value[existingIndex] = response
      } else {
        connectionTestResults.value.push(response)
      }

      return response
    })
  }

  /**
   * 批量测试连接
   */
  const batchTestConnection = async (ids: number[]) => {
    return await testLoading.withLoading(async () => {
      const response = await EnvironmentService.batchTestConnections(ids)

      // 更新连接测试结果
      response.forEach((result: any, index: number) => {
        const existingIndex = connectionTestResults.value.findIndex(r => r.env_id === ids[index])
        if (existingIndex !== -1) {
          connectionTestResults.value[existingIndex] = result
        } else {
          connectionTestResults.value.push({ ...result, env_id: ids[index] })
        }
      })

      return response
    })
  }

  /**
   * 批量测试连接（别名方法，用于兼容视图组件）
   */
  const batchTestConnections = async (ids: number[]) => {
    return await batchTestConnection(ids)
  }

  /**
   * 获取支持的环境类型
   */
  const fetchSupportedTypes = async () => {
    // 先尝试从缓存获取
    const cached = globalCache.get('supported_env_types')
    if (cached) {
      return cached
    }

    return await loading.withLoading(async () => {
      const response = await EnvironmentService.getSupportedTypes()
      
      // 缓存支持的类型（较长时间）
      globalCache.set('supported_env_types', response, 60 * 60 * 1000) // 1小时缓存
      
      return response
    })
  }

  // ==================== 辅助方法 ====================
  
  /**
   * 重置状态
   */
  const resetState = () => {
    environments.value = []
    currentEnvironment.value = null
    availableEnvironments.value = []
    environmentStats.value = null
    connectionTestResults.value = []
    pagination.reset()
    loading.reset()
    statsLoading.reset()
    testLoading.reset()
  }

  /**
   * 清除连接测试结果
   */
  const clearTestResults = () => {
    connectionTestResults.value = []
  }

  return {
    // 状态
    environments: readonly(environments),
    currentEnvironment: readonly(currentEnvironment),
    availableEnvironments: readonly(availableEnvironments),
    environmentStats: readonly(environmentStats),
    connectionTestResults: readonly(connectionTestResults),
    pagination,
    loading,
    statsLoading,
    testLoading,
    
    // 计算属性
    totalEnvironments,
    activeEnvironments,
    inactiveEnvironments,
    environmentsByType,
    
    // 方法
    fetchEnvironments,
    createEnvironment,
    fetchEnvironment,
    updateEnvironment,
    deleteEnvironment,
    fetchAvailableEnvironments,
    fetchEnvironmentStats,
    testConnection,
    batchTestConnection,
    batchTestConnections,
    fetchSupportedTypes,
    resetState,
    clearTestResults
  }
})
