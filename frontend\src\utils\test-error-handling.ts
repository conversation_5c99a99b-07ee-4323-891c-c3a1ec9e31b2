/**
 * 前端错误处理系统测试
 * 用于验证新的统一错误码体系
 */
import { ErrorCode, ErrorType, getErrorType, getErrorModule, isAuthError } from '@/constants/errorCodes'
import { handleError, handleErrorByCode } from '@/utils/errorHandler'
import { HttpError } from '@/utils/http/error'

/**
 * 测试错误码常量
 */
export function testErrorCodes() {
  console.log('=== 错误码常量测试 ===')
  
  // 测试错误码值
  console.log('用户不存在错误码:', ErrorCode.USER_NOT_FOUND) // 10301
  console.log('权限不足错误码:', ErrorCode.PERMISSION_DENIED) // 90204
  console.log('系统错误码:', ErrorCode.INTERNAL_SERVER_ERROR) // 90501
  
  // 测试错误类型获取
  console.log('用户不存在错误类型:', getErrorType(ErrorCode.USER_NOT_FOUND)) // not_found
  console.log('权限不足错误类型:', getErrorType(ErrorCode.PERMISSION_DENIED)) // permission
  
  // 测试错误模块获取
  console.log('用户不存在错误模块:', getErrorModule(ErrorCode.USER_NOT_FOUND)) // user
  console.log('系统错误模块:', getErrorModule(ErrorCode.INTERNAL_SERVER_ERROR)) // system
  
  // 测试认证错误判断
  console.log('是否为认证错误:', isAuthError(ErrorCode.UNAUTHORIZED)) // true
  console.log('是否为认证错误:', isAuthError(ErrorCode.USER_NOT_FOUND)) // false
}

/**
 * 测试HttpError类
 */
export function testHttpError() {
  console.log('=== HttpError类测试 ===')
  
  // 创建新格式错误
  const error = new HttpError('用户不存在', 404, {
    errorCode: ErrorCode.USER_NOT_FOUND,
    httpCode: 404,
    requestId: 'req-12345'
  })
  
  console.log('错误消息:', error.message)
  console.log('HTTP状态码:', error.code)
  console.log('业务错误码:', error.errorCode)
  console.log('错误类型:', error.getErrorType())
  console.log('错误模块:', error.getErrorModule())
  console.log('是否为认证错误:', error.isAuthError())
  console.log('错误日志数据:', error.toLogData())
}

/**
 * 测试错误处理函数
 */
export function testErrorHandling() {
  console.log('=== 错误处理函数测试 ===')
  
  // 测试新格式错误响应
  const newFormatError = {
    response: {
      data: {
        message: '用户不存在',
        error_code: ErrorCode.USER_NOT_FOUND,
        http_code: 404,
        timestamp: new Date().toISOString(),
        request_id: 'req-12345'
      }
    }
  }
  
  console.log('处理新格式错误:')
  handleError(newFormatError, { 
    showMessage: false, // 测试时不显示消息
    logError: true 
  })
  
  // 测试旧格式错误响应
  const oldFormatError = {
    response: {
      data: {
        success: false,
        code: 400,
        message: '请求参数错误'
      }
    }
  }
  
  console.log('处理旧格式错误:')
  handleError(oldFormatError, { 
    showMessage: false,
    logError: true 
  })
}

/**
 * 测试根据错误码处理
 */
export function testErrorByCode() {
  console.log('=== 根据错误码处理测试 ===')
  
  // 测试不同类型的错误码
  const testCodes = [
    ErrorCode.USER_NOT_FOUND,        // 资源不存在
    ErrorCode.PERMISSION_DENIED,     // 权限错误
    ErrorCode.FIELD_VALUE_INVALID,   // 验证错误
    ErrorCode.USER_ALREADY_EXISTS,   // 业务错误
    ErrorCode.INTERNAL_SERVER_ERROR  // 系统错误
  ]
  
  testCodes.forEach(code => {
    console.log(`处理错误码 ${code}:`)
    handleErrorByCode(code, `测试错误消息 - ${code}`)
  })
}

/**
 * 模拟API响应测试
 */
export function testApiResponse() {
  console.log('=== API响应测试 ===')
  
  // 模拟RESTful成功响应
  const successResponse = {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>'
  }
  console.log('RESTful成功响应:', successResponse)
  
  // 模拟RESTful错误响应
  const errorResponse = {
    message: '用户名已存在',
    error_code: ErrorCode.USER_ALREADY_EXISTS,
    http_code: 409,
    details: { field: 'username', value: 'john' },
    timestamp: new Date().toISOString(),
    request_id: 'req-67890'
  }
  console.log('RESTful错误响应:', errorResponse)
  
  // 模拟分页响应
  const paginationResponse = {
    records: [
      { id: 1, name: 'User 1' },
      { id: 2, name: 'User 2' }
    ],
    total: 100,
    current: 1,
    size: 20
  }
  console.log('RESTful分页响应:', paginationResponse)
}

/**
 * 运行所有测试
 */
export function runAllTests() {
  console.log('🚀 开始前端错误处理系统测试')
  
  try {
    testErrorCodes()
    testHttpError()
    testErrorHandling()
    testErrorByCode()
    testApiResponse()
    
    console.log('✅ 所有测试完成')
  } catch (error) {
    console.error('❌ 测试失败:', error)
  }
}

// 在开发环境下自动运行测试
if (import.meta.env.DEV) {
  // 可以在控制台手动调用 runAllTests() 进行测试
  (window as any).testErrorHandling = {
    runAllTests,
    testErrorCodes,
    testHttpError,
    testErrorHandling,
    testErrorByCode,
    testApiResponse
  }
  
  console.log('💡 错误处理测试工具已加载，可在控制台调用:')
  console.log('- window.testErrorHandling.runAllTests() - 运行所有测试')
  console.log('- window.testErrorHandling.testErrorCodes() - 测试错误码')
  console.log('- window.testErrorHandling.testHttpError() - 测试HttpError类')
}
