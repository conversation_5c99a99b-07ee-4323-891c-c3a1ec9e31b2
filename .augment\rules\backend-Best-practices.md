---
type: "always_apply"
---

# DpTestPlatform 后端开发规范

## 核心架构原则

### 分层架构模式
```
API层 (api/)          ←→ HTTP请求和响应，参数验证，路由定义
    ↓
业务逻辑层 (services/) ←→ 核心业务逻辑，业务规则实现
    ↓
数据访问层 (repositories/) ←→ 数据访问抽象，查询逻辑封装
    ↓
数据模型层 (models/)    ←→ 数据库实体定义，关系映射
```

###  Schema层最佳实践

```python
# ✅ 重构后：简洁的标准继承结构
class UserBase(BaseModel):
    """用户基础模型（包含公共字段）"""
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱")
    nickname: Optional[str] = Field(None, max_length=50, description="昵称")
    # ... 其他公共字段

class UserCreate(UserBase):
    """创建用户请求模型（必填字段）"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱")
    password: str = Field(..., min_length=6, max_length=50, description="密码")

class UserUpdate(UserBase):
    """更新用户请求模型（可选字段）"""
    password: Optional[str] = Field(None, min_length=6, max_length=50, description="新密码（可选）")

class UserResponse(UserBase, BaseResponseSchema):
    """用户详情响应模型"""
    id: int = Field(..., description="用户ID")
    is_superuser: bool = Field(..., description="是否超级管理员")
    status: str = Field(default="1", description="用户状态")
    roles: List[str] = Field(default=[], description="角色代码列表")

    class Config:
        from_attributes = True  # Pydantic V2 语法

class UserPageResponse(BaseModel):
    """用户列表分页响应模型"""
    items: List[UserResponse] = Field(..., description="当前页用户列表")
    total: int = Field(..., description="总条数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页条数")
    pages: int = Field(..., description="总页数")
```

#### Schema设计原则
1. **标准继承结构**: UserBase → UserCreate/UserUpdate/UserResponse
2. **移除前端适配**: 让前端适应后端标准，而不是后端适配前端
3. **类型安全**: 使用Pydantic V2的from_attributes配置
4. **分页标准化**: 统一的分页响应格式

###  Repository层最佳实践

```python
# ✅ 重构后：简洁的Repository
class UserRepository(BaseRepository[User, UserCreate, UserUpdate]):
    """用户数据访问层"""

    def __init__(self, db: AsyncSession):
        super().__init__(User, db)

    async def get_by_username(self, username: str) -> Optional[User]:
        """根据用户名查询用户（用于登录验证）"""
        result = await self.db.execute(
            select(User)
            .options(selectinload(User.roles))
            .where(User.username == username)
        )
        return result.scalar_one_or_none()

    async def list(
        self,
        keyword: Optional[str] = None,
        status: Optional[str] = None,
        is_active: Optional[bool] = None,
        offset: int = 0,
        limit: int = 10
    ) -> Tuple[List[User], int]:
        """查询用户列表（带筛选和分页）"""
        # 基础查询
        query = select(User).options(selectinload(User.roles))

        # 筛选条件
        if keyword:
            query = query.where(
                or_(
                    User.username.ilike(f"%{keyword}%"),
                    User.email.ilike(f"%{keyword}%"),
                    User.nickname.ilike(f"%{keyword}%")
                )
            )

        # 计算总条数和分页查询
        total_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(total_query)
        total = total_result.scalar()

        query = query.offset(offset).limit(limit).order_by(User.created_at.desc())
        result = await self.db.execute(query)
        items = result.scalars().all()

        return items, total

    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """用户认证"""
        from app.utils.security import verify_password

        user = await self.get_by_username(username)
        if not user:
            return None

        if not verify_password(password, user.hashed_password):
            return None

        return user
```

#### Repository设计原则
1. **继承BaseRepository**: 充分利用基类的通用CRUD方法
2. **专注数据访问**: 只负责数据查询，不包含业务逻辑
3. **预加载关系**: 使用selectinload避免N+1查询问题
4. **高效分页**: 优化的分页查询和计数逻辑

###  Service层重构最佳实践

```python
class UserService(BaseService[User, UserCreate, UserUpdate, UserResponse]):
    """用户业务服务"""

    def __init__(self, db: AsyncSession):
        repository = UserRepository(db)
        super().__init__(db, repository)

    async def _validate_before_create(self, create_data: UserCreate, **_kwargs) -> None:
        """创建用户前的验证"""
        # 检查用户名是否重复
        existing_user = await self.repository.get_by_username(create_data.username)
        if existing_user:
            raise_validation_error(f"用户名 '{create_data.username}' 已存在")

        # 检查邮箱是否重复
        if create_data.email:
            existing_email = await self.repository.get_by_email(create_data.email)
            if existing_email:
                raise_validation_error(f"邮箱 '{create_data.email}' 已被使用")

    async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
        """创建用户前的处理"""
        # 加密密码
        if 'password' in create_dict:
            password = create_dict.pop('password')
            create_dict['hashed_password'] = get_password_hash(password)

        return create_dict

    def _convert_to_response(self, user: User) -> UserResponse:
        """将User对象转换为UserResponse"""
        # 安全获取角色信息，避免延迟加载问题
        try:
            roles = [role.code for role in user.roles] if hasattr(user, 'roles') and user.roles else []
        except Exception:
            roles = []

        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            # ... 其他字段映射
            roles=roles,
            created_at=user.created_at.strftime("%Y-%m-%d %H:%M:%S") if user.created_at else None,
            updated_at=user.updated_at.strftime("%Y-%m-%d %H:%M:%S") if user.updated_at else None,
        )

    # ==================== 业务方法使用基类通用方法 ====================

    async def create_user(self, user_data: UserCreate, current_user_id: int) -> UserResponse:
        """创建用户 - 使用基类通用方法"""
        return await self.create(user_data, str(current_user_id))

    async def list_users(self, query: UserQuery) -> UserPageResponse:
        """查询用户列表，支持关键词搜索、状态筛选和分页"""
        users, total = await self.repository.list(
            keyword=query.keyword,
            status=query.status,
            is_active=query.is_active,
            offset=query.offset,
            limit=query.size
        )

        # 转换为响应格式
        user_responses = [self._convert_to_response(user) for user in users]

        # 计算总页数
        pages = (total + query.size - 1) // query.size

        return UserPageResponse(
            items=user_responses,
            total=total,
            page=query.page,
            size=query.size,
            pages=pages
        )
```

###  API层重构最佳实践

```python
# ✅ 重构后：简洁的RESTful API
@router.get("", response_model=UserPageResponse, summary="查询用户列表")
async def list_users(
    service: UserServiceType,
    query: UserQuery = Depends(),  # 自动接收并校验查询参数
    _current_user: User = Depends(get_current_superuser)
):
    """查询用户列表，支持关键词搜索、状态筛选和分页"""
    result = await service.list_users(query)
    return response_builder.success(result)

@router.get("/{user_id}", response_model=UserResponse, summary="获取用户详情")
async def get_user(
    service: UserServiceType,
    user_id: int,
    _current_user: User = Depends(get_current_superuser)
):
    """获取指定ID的用户详情"""
    user = await service.get_by_id(user_id)
    return response_builder.success(user)

@router.post("", response_model=UserResponse, summary="创建用户")
async def create_user(
    service: UserServiceType,
    user_in: UserCreate,
    current_user: User = Depends(get_current_superuser)
):
    """创建新用户，需要管理员权限"""
    user = await service.create_user(user_in, current_user.id)
    return response_builder.created(user)

@router.put("/{user_id}", response_model=UserResponse, summary="更新用户")
async def update_user(
    service: UserServiceType,
    user_id: int,
    user_in: UserUpdate,
    current_user: User = Depends(get_current_superuser)
):
    """更新用户信息，支持修改基本信息和密码"""
    user = await service.update_user(user_id, user_in, current_user.id)
    return response_builder.success(user)

@router.delete("/{user_id}", status_code=204, summary="删除用户")
async def delete_user(
    service: UserServiceType,
    user_id: int,
    current_user: User = Depends(get_current_superuser)
):
    """删除用户，不允许删除超级管理员和自己"""
    await service.delete_user(user_id, current_user.id)
```

#### API设计原则
1. **RESTful路径**: 使用标准的REST动词和路径
2. **参数简化**: 移除不必要的Path()、Body()装饰器，让FastAPI自动推断
3. **响应统一**: 使用response_builder构建统一响应格式
4. **类型安全**: 完整的类型提示和响应模型
