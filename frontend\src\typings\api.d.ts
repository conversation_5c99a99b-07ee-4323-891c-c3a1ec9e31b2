/**
 * namespace: Api
 *
 * 所有接口相关类型定义
 * 在.vue文件使用会报错，需要在 eslint.config.mjs 中配置 globals: { Api: 'readonly' }
 */
declare namespace Api {
  /** 基础类型 */
  namespace Http {
    /** 基础响应 */
    interface BaseResponse<T = any> {
      // 状态码
      code: number
      // 消息
      msg: string
      // 数据
      data: T
    }
  }

  /** 通用类型 */
  namespace Common {
    /** 分页参数 */
    interface PaginatingParams {
      /** 当前页码 */
      current: number
      /** 每页条数 */
      size: number
      /** 总条数 */
      total: number
    }

    /** 通用搜索参数 - 与后端UserQuery匹配 */
    interface PaginatingSearchParams {
      page?: number      // 页码，默认1
      size?: number      // 每页条数，默认10
      keyword?: string   // 搜索关键词
      status?: string    // 状态筛选
      is_active?: boolean // 激活状态筛选
    }

    /** 启用状态 */
    type EnableStatus = '1' | '2'

    /** 基础响应 */
    interface BaseResponse<T = any> {
      success: boolean
      code: number
      message: string
      data: T
    }
  }

  /** 认证类型 */
  namespace Auth {
    /** 登录参数 */
    interface LoginParams {
      username: string
      password: string
    }

    /** 登录响应 */
    interface LoginResponse {
      token: string
      refreshToken: string
    }
  }

  /** 用户类型 */
  namespace User {
    /** 用户信息*/
    interface UserInfo {
      id: number
      username: string
      email?: string
      nickname?: string
      avatar?: string
      description?: string
      is_active: boolean
      is_superuser: boolean
      status: string
      last_login_at?: string
      roles: string[]
      created_at?: string
      updated_at?: string
      created_by?: string
      updated_by?: string
      // 前端兼容字段
      userId?: number  // 映射到id
      buttons?: string[]  // 权限按钮，前端计算
    }

    /** 用户列表数据 - 与后端UserPageResponse匹配 */
    interface UserListData {
      items: UserInfo[]  // 改为items，与后端一致
      total: number
      page: number       // 改为page，与后端一致
      size: number
      pages: number      // 总页数
    }

    /** 用户列表项 - 与UserInfo保持一致 */
    interface UserListItem extends UserInfo {
      // 继承UserInfo的所有字段
    }

    /** 用户创建参数 - 与后端UserCreate匹配 */
    interface UserCreateParams {
      username: string
      email: string
      password: string
      nickname?: string
      avatar?: string
      description?: string
      is_active?: boolean
      is_superuser?: boolean
    }

    /** 用户更新参数 - 与后端UserUpdate匹配 */
    interface UserUpdateParams {
      username?: string
      email?: string
      password?: string
      nickname?: string
      avatar?: string
      description?: string
      is_active?: boolean
      is_superuser?: boolean
    }

    /** 角色信息 */
    interface RoleInfo {
      id: number
      roleCode: string
      roleName: string
      description?: string
      status: string
    }
  }

  /** 环境管理类型 */
  namespace Environment {
    // 重新导出环境相关类型
    type EnvironmentType = import('@/types/api/environment').EnvironmentType
    type EnvironmentStatus = import('@/types/api/environment').EnvironmentStatus
    type EnvironmentBase = import('@/types/api/environment').EnvironmentBase
    type EnvironmentCreateRequest = import('@/types/api/environment').EnvironmentCreateRequest
    type EnvironmentUpdateRequest = import('@/types/api/environment').EnvironmentUpdateRequest
    type EnvironmentResponse = import('@/types/api/environment').EnvironmentResponse
    type EnvironmentListItem = import('@/types/api/environment').EnvironmentListItem
    type EnvironmentListResponse = import('@/types/api/environment').EnvironmentListResponse
    type EnvironmentListParams = import('@/types/api/environment').EnvironmentListParams
    type ConnectionTestRequest = import('@/types/api/environment').ConnectionTestRequest
    type ConnectionTestResponse = import('@/types/api/environment').ConnectionTestResponse
    type SupportedEnvironmentType = import('@/types/api/environment').SupportedEnvironmentType
  }
}

