"""
模型配置数据访问仓库
"""
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_, func, case

from app.repositories.base import BaseRepository
from app.models.model.model_config import ModelConfig
from app.schemas.model.model_config import ModelConfigCreate, ModelConfigUpdate
from app.schemas.common import SearchParams


class ModelConfigRepository(BaseRepository[ModelConfig, ModelConfigCreate, ModelConfigUpdate]):
    """模型配置数据访问仓库"""

    def __init__(self, db: AsyncSession):
        super().__init__(ModelConfig, db)

    async def get_by_name(self, name: str) -> Optional[ModelConfig]:
        """根据模型名称获取模型配置"""
        result = await self.db.execute(
            select(ModelConfig).where(ModelConfig.name == name)
        )
        return result.scalar_one_or_none()

    async def get_by_platform(self, platform: str) -> List[ModelConfig]:
        """根据平台获取模型配置列表"""
        result = await self.db.execute(
            select(ModelConfig).where(ModelConfig.platform == platform)
        )
        return result.scalars().all()

    async def search_models_with_query(self, query) -> Tuple[List[ModelConfig], int]:
        """使用新的查询参数格式查询模型列表"""
        from sqlalchemy import func, or_

        # 基础查询
        base_query = select(ModelConfig)

        # 筛选条件
        if query.keyword:
            base_query = base_query.where(
                or_(
                    ModelConfig.name.ilike(f"%{query.keyword}%"),
                    ModelConfig.model_name.ilike(f"%{query.keyword}%"),
                    ModelConfig.description.ilike(f"%{query.keyword}%")
                )
            )

        if query.platform:
            base_query = base_query.where(ModelConfig.platform == query.platform)

        if query.status:
            base_query = base_query.where(ModelConfig.status == query.status)

        if query.health_status:
            base_query = base_query.where(ModelConfig.health_status == query.health_status)

        # 计算总条数
        total_query = select(func.count()).select_from(base_query.subquery())
        total_result = await self.db.execute(total_query)
        total = total_result.scalar()

        # 分页查询
        query_with_pagination = base_query.offset(query.offset).limit(query.size).order_by(ModelConfig.created_at.desc())
        result = await self.db.execute(query_with_pagination)
        items = result.scalars().all()

        return items, total

    async def get_enabled_models(self) -> List[ModelConfig]:
        """获取所有启用的模型配置"""
        result = await self.db.execute(
            select(ModelConfig).where(ModelConfig.status == 'enabled')
        )
        return result.scalars().all()

    async def get_healthy_models(self) -> List[ModelConfig]:
        """获取所有健康的模型配置"""
        result = await self.db.execute(
            select(ModelConfig).where(
                and_(
                    ModelConfig.status == 'enabled',
                    ModelConfig.health_status == 'healthy'
                )
            )
        )
        return result.scalars().all()

    async def get_available_models(self) -> List[ModelConfig]:
        """获取所有可用的模型配置（启用且健康）"""
        return await self.get_healthy_models()

    async def search_models(
        self, 
        params: SearchParams,
        platform: Optional[str] = None,
        status: Optional[str] = None,
        health_status: Optional[str] = None
    ) -> tuple[List[ModelConfig], int]:
        """搜索模型配置"""
        # 构建基础查询
        query = select(ModelConfig)
        count_query = select(func.count(ModelConfig.id))
        
        # 构建过滤条件
        conditions = []
        
        # 关键词搜索
        if params.keyword:
            keyword_condition = or_(
                ModelConfig.name.ilike(f"%{params.keyword}%"),
                ModelConfig.platform.ilike(f"%{params.keyword}%"),
                ModelConfig.description.ilike(f"%{params.keyword}%")
            )
            conditions.append(keyword_condition)
        
        # 平台过滤
        if platform:
            conditions.append(ModelConfig.platform == platform)
        
        # 状态过滤
        if status:
            conditions.append(ModelConfig.status == status)
        
        # 健康状态过滤
        if health_status:
            conditions.append(ModelConfig.health_status == health_status)
        
        # 应用过滤条件
        if conditions:
            filter_condition = and_(*conditions)
            query = query.where(filter_condition)
            count_query = count_query.where(filter_condition)
        
        # 获取总数
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 应用分页和排序
        query = query.order_by(ModelConfig.created_at.desc())
        query = query.offset((params.current - 1) * params.size).limit(params.size)
        
        # 执行查询
        result = await self.db.execute(query)
        models = result.scalars().all()
        
        return models, total

    async def update_health_status(
        self, 
        model_id: int, 
        health_status: str, 
        last_check_time: str
    ) -> Optional[ModelConfig]:
        """更新模型健康状态"""
        model = await self.get(model_id)
        if model:
            model.health_status = health_status
            model.last_health_check = last_check_time
            self.db.add(model)
            await self.db.commit()
            await self.db.refresh(model)
        return model

    async def batch_update_health_status(
        self, 
        health_updates: List[Dict[str, Any]]
    ) -> List[ModelConfig]:
        """批量更新模型健康状态"""
        updated_models = []
        for update in health_updates:
            model = await self.update_health_status(
                update['model_id'],
                update['health_status'],
                update['last_check_time']
            )
            if model:
                updated_models.append(model)
        return updated_models

    async def get_models_by_status(self, status: str) -> List[ModelConfig]:
        """根据状态获取模型列表"""
        result = await self.db.execute(
            select(ModelConfig).where(ModelConfig.status == status)
        )
        return result.scalars().all()

    async def get_models_by_health_status(self, health_status: str) -> List[ModelConfig]:
        """根据健康状态获取模型列表"""
        result = await self.db.execute(
            select(ModelConfig).where(ModelConfig.health_status == health_status)
        )
        return result.scalars().all()

    async def get_platform_stats(self) -> List[Dict[str, Any]]:
        """获取平台统计信息"""
        result = await self.db.execute(
            select(
                ModelConfig.platform,
                func.count(ModelConfig.id).label('total'),
                func.sum(case((ModelConfig.status == 'enabled', 1), else_=0)).label('enabled'),
                func.sum(case((ModelConfig.health_status == 'healthy', 1), else_=0)).label('healthy')
            ).group_by(ModelConfig.platform)
        )
        
        stats = []
        for row in result:
            stats.append({
                'platform': row.platform,
                'total': row.total,
                'enabled': row.enabled or 0,
                'healthy': row.healthy or 0
            })
        
        return stats
