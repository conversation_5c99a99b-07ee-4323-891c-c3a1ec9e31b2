"""
RESTful响应处理系统
提供标准化的RESTful API响应格式，集成统一错误码体系
"""
from typing import Any, List, Optional
from fastapi import HTTPException
from datetime import datetime
import uuid

from app.schemas.base import ErrorResponse, PaginationResponse
from app.core.error_codes import Error<PERSON><PERSON>, get_http_status_code
from app.core.error_messages import error_message_manager


class ResponseBuilder:
    """
    RESTful响应构建器
    提供标准RESTful响应，HTTP状态码直接反映业务状态
    """

    @staticmethod
    def success(data: Any = None) -> Any:
        """
        构建成功响应 - 直接返回业务数据

        Args:
            data: 响应数据

        Returns:
            业务数据（不包装）
        """
        return data

    @staticmethod
    def created(data: Any) -> Any:
        """
        构建创建成功响应 - HTTP 201

        Args:
            data: 创建的资源数据

        Returns:
            创建的资源数据
        """
        return data

    @staticmethod
    def error(
        error_code: ErrorCode,
        message: Optional[str] = None,
        details: Any = None,
        language: str = 'zh'
    ) -> ErrorResponse:
        """
        构建错误响应 - 使用统一错误码体系

        Args:
            error_code: 业务错误码
            message: 自定义错误消息（可选）
            details: 错误详情
            language: 语言代码

        Returns:
            ErrorResponse 实例
        """
        http_code = get_http_status_code(error_code)
        error_message = message or error_message_manager.get_message(error_code, language)

        return ErrorResponse(
            message=error_message,
            error_code=error_code.value,
            http_code=http_code,
            details=details,
            timestamp=datetime.utcnow().isoformat(),
            request_id=str(uuid.uuid4())[:8]
        )

    @staticmethod
    def paginated(
        records: List[Any],
        total: int,
        current: int,
        size: int
    ) -> PaginationResponse[Any]:
        """
        构建分页响应 - 直接返回分页数据结构

        Args:
            records: 数据列表
            total: 总数
            current: 当前页
            size: 页大小

        Returns:
            PaginationResponse 实例
        """
        return PaginationResponse(
            records=records,
            total=total,
            current=current,
            size=size
        )

    @staticmethod
    def http_error(
        error_code: ErrorCode,
        message: Optional[str] = None,
        details: Any = None,
        language: str = 'zh'
    ) -> HTTPException:
        """
        构建HTTP异常 - 用于直接抛出

        Args:
            error_code: 业务错误码
            message: 自定义错误消息（可选）
            details: 错误详情
            language: 语言代码

        Returns:
            HTTPException
        """
        error_response = ResponseBuilder.error(error_code, message, details, language)
        return HTTPException(
            status_code=error_response.http_code,
            detail=error_response.model_dump()
        )


# 全局响应构建器实例
response_builder = ResponseBuilder() 