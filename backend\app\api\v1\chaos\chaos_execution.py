"""
混沌测试执行记录API路由
"""
from typing import Optional
from fastapi import APIRouter, Depends, Path, Query, Body

from app.api.deps import get_current_user
from app.core.dependencies import get_chaos_execution_service
from app.core.responses import response_builder
from app.models.user.user import User
from app.services.chaos.chaos_execution_service import ChaosExecutionService
from app.schemas.base import PaginationResponse
from app.schemas.chaos.chaos_execution import (
    ChaosExecutionResponse, ChaosExecutionListResponse, ChaosExecutionDetailResponse,
    ChaosExecutionLogRequest, ChaosExecutionLogResponse, ChaosExecutionRetryRequest,
    ChaosExecutionBatchRequest, ChaosExecutionQuery, ChaosExecutionPageResponse
)

router = APIRouter()


@router.get("", response_model=ChaosExecutionPageResponse, summary="查询执行记录列表")
async def list_executions(
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    query: ChaosExecutionQuery = Depends(),  # 自动接收并校验查询参数
    _current_user: User = Depends(get_current_user)
):
    """查询执行记录列表，支持关键词搜索、状态筛选和分页"""
    result = await service.list_executions(query)
    return response_builder.success(result)


@router.get("/statistics-data", response_model=dict, summary="获取执行统计")
async def get_execution_statistics(
    task_id: Optional[int] = Query(None, description="任务ID，可选"),
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    _current_user: User = Depends(get_current_user)
):
    """获取执行统计信息"""
    statistics = await service.get_execution_statistics(task_id)
    return response_builder.success(statistics)


@router.post("/status-check", response_model=dict, summary="检查运行中的执行状态")
async def check_running_executions_status(
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    _current_user: User = Depends(get_current_user)
):
    """检查并更新正在运行的执行记录状态"""
    result = await service.check_and_update_running_executions()
    return response_builder.success(result)


@router.get("/{execution_id}", response_model=ChaosExecutionResponse, summary="获取执行记录详情")
async def get_execution(
    execution_id: int = Path(..., description="执行记录ID"),
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    _current_user: User = Depends(get_current_user)
):
    """获取执行记录详情"""
    execution = await service.get_by_id(execution_id)
    return response_builder.success(execution)


@router.delete("/{execution_id}", status_code=204, summary="删除执行记录")
async def delete_execution(
    execution_id: int = Path(..., description="执行记录ID"),
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    current_user: User = Depends(get_current_user)
):
    """删除执行记录"""
    await service.delete(execution_id, str(current_user.id))


@router.get("/task/{task_id}", response_model=PaginationResponse[ChaosExecutionListResponse], summary="获取任务的执行记录")
async def get_task_executions(
    task_id: int = Path(..., description="任务ID"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    _current_user: User = Depends(get_current_user)
):
    """获取任务的执行记录"""
    result = await service.get_task_executions(task_id, page, size)
    return response_builder.success(result)


@router.post("/{execution_id}/log", response_model=ChaosExecutionLogResponse, summary="获取执行日志")
async def get_execution_log(
    execution_id: int = Path(..., description="执行记录ID"),
    log_request: ChaosExecutionLogRequest = Body(...),
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    _current_user: User = Depends(get_current_user)
):
    """获取执行日志"""
    log_request.execution_id = execution_id
    log_response = await service.get_execution_log(log_request)
    return response_builder.success(log_response)


@router.post("/{execution_id}/retry", response_model=ChaosExecutionResponse, summary="重试执行")
async def retry_execution(
    execution_id: int = Path(..., description="执行记录ID"),
    retry_request: ChaosExecutionRetryRequest = Body(default=ChaosExecutionRetryRequest()),
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    current_user: User = Depends(get_current_user)
):
    """重试执行"""
    retry_request.execution_id = execution_id
    execution = await service.retry_execution(retry_request, current_user.id)
    return response_builder.success(execution)


@router.post("/{execution_id}/cancel", status_code=204, summary="取消执行")
async def cancel_execution(
    execution_id: int = Path(..., description="执行记录ID"),
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    current_user: User = Depends(get_current_user)
):
    """取消执行"""
    await service.cancel_execution(execution_id, current_user.id)


@router.post("/batch", response_model=dict, summary="批量操作执行记录")
async def batch_operation(
    request: ChaosExecutionBatchRequest,
    service: ChaosExecutionService = Depends(get_chaos_execution_service),
    current_user: User = Depends(get_current_user)
):
    """批量操作执行记录"""
    result = await service.batch_operation(request, current_user.id)
    return response_builder.success(result)



