"""
错误消息映射系统
支持多语言的错误消息定义
"""
from typing import Dict, Optional
from .error_codes import ErrorCode


# 中文错误消息映射
ERROR_MESSAGES_ZH = {
    # 系统通用错误
    ErrorCode.INVALID_REQUEST_PARAMS: "请求参数无效",
    ErrorCode.INVALID_JSON_FORMAT: "JSON格式错误",
    ErrorCode.MISSING_REQUIRED_FIELD: "缺少必填字段",
    ErrorCode.FIELD_VALUE_INVALID: "字段值无效",
    ErrorCode.FIELD_LENGTH_INVALID: "字段长度无效",
    
    ErrorCode.UNAUTHORIZED: "未授权访问",
    ErrorCode.TOKEN_INVALID: "令牌无效",
    ErrorCode.TOKEN_EXPIRED: "令牌已过期",
    ErrorCode.PERMISSION_DENIED: "权限不足",
    ErrorCode.ACCESS_FORBIDDEN: "访问被禁止",
    
    ErrorCode.RESOURCE_NOT_FOUND: "资源不存在",
    ErrorCode.ENDPOINT_NOT_FOUND: "接口不存在",
    ErrorCode.METHOD_NOT_ALLOWED: "请求方法不允许",
    
    ErrorCode.OPERATION_FAILED: "操作失败",
    ErrorCode.RESOURCE_CONFLICT: "资源冲突",
    ErrorCode.OPERATION_NOT_ALLOWED: "操作不被允许",
    ErrorCode.RATE_LIMIT_EXCEEDED: "请求频率超限",
    
    ErrorCode.INTERNAL_SERVER_ERROR: "服务器内部错误",
    ErrorCode.DATABASE_ERROR: "数据库错误",
    ErrorCode.EXTERNAL_SERVICE_ERROR: "外部服务错误",
    ErrorCode.CONFIGURATION_ERROR: "配置错误",
    ErrorCode.NETWORK_ERROR: "网络错误",
    
    # 用户模块错误
    ErrorCode.USER_USERNAME_INVALID: "用户名格式无效",
    ErrorCode.USER_PASSWORD_INVALID: "密码格式无效",
    ErrorCode.USER_EMAIL_INVALID: "邮箱格式无效",
    ErrorCode.USER_PHONE_INVALID: "手机号格式无效",
    ErrorCode.USER_NICKNAME_INVALID: "昵称格式无效",
    
    ErrorCode.USER_LOGIN_REQUIRED: "需要登录",
    ErrorCode.USER_LOGIN_FAILED: "登录失败",
    ErrorCode.USER_PASSWORD_WRONG: "密码错误",
    ErrorCode.USER_ACCOUNT_DISABLED: "账户已禁用",
    ErrorCode.USER_ACCOUNT_LOCKED: "账户已锁定",
    
    ErrorCode.USER_NOT_FOUND: "用户不存在",
    ErrorCode.USER_ROLE_NOT_FOUND: "角色不存在",
    
    ErrorCode.USER_ALREADY_EXISTS: "用户已存在",
    ErrorCode.USER_EMAIL_ALREADY_EXISTS: "邮箱已存在",
    ErrorCode.USER_CANNOT_DELETE_SELF: "不能删除自己",
    ErrorCode.USER_CANNOT_MODIFY_ADMIN: "不能修改管理员",
    
    # 环境模块错误
    ErrorCode.ENV_NAME_INVALID: "环境名称无效",
    ErrorCode.ENV_TYPE_INVALID: "环境类型无效",
    ErrorCode.ENV_CONFIG_INVALID: "环境配置无效",
    ErrorCode.ENV_HOST_INVALID: "主机地址无效",
    ErrorCode.ENV_PORT_INVALID: "端口号无效",
    
    ErrorCode.ENV_ACCESS_DENIED: "环境访问被拒绝",
    ErrorCode.ENV_OPERATION_FORBIDDEN: "环境操作被禁止",
    
    ErrorCode.ENV_NOT_FOUND: "环境不存在",
    ErrorCode.ENV_CONFIG_NOT_FOUND: "环境配置不存在",
    
    ErrorCode.ENV_NAME_ALREADY_EXISTS: "环境名称已存在",
    ErrorCode.ENV_CONNECTION_FAILED: "环境连接失败",
    ErrorCode.ENV_IN_USE: "环境正在使用中",
    ErrorCode.ENV_TEST_FAILED: "环境测试失败",
    
    # 混沌测试模块错误
    ErrorCode.CHAOS_TASK_NAME_INVALID: "任务名称无效",
    ErrorCode.CHAOS_FAULT_TYPE_INVALID: "故障类型无效",
    ErrorCode.CHAOS_PARAMS_INVALID: "故障参数无效",
    ErrorCode.CHAOS_SCHEDULE_INVALID: "调度配置无效",
    
    ErrorCode.CHAOS_TASK_ACCESS_DENIED: "任务访问被拒绝",
    ErrorCode.CHAOS_EXECUTION_FORBIDDEN: "执行被禁止",
    
    ErrorCode.CHAOS_TASK_NOT_FOUND: "任务不存在",
    ErrorCode.CHAOS_SCENARIO_NOT_FOUND: "场景不存在",
    ErrorCode.CHAOS_EXECUTION_NOT_FOUND: "执行记录不存在",
    
    ErrorCode.CHAOS_TASK_ALREADY_RUNNING: "任务已在运行",
    ErrorCode.CHAOS_TASK_CANNOT_EXECUTE: "任务无法执行",
    ErrorCode.CHAOS_BLADE_NOT_INSTALLED: "ChaosBlade未安装",
    ErrorCode.CHAOS_EXECUTION_FAILED: "执行失败",
    
    # 数据工厂模块错误
    ErrorCode.DATA_MODEL_NAME_INVALID: "数据模型名称无效",
    ErrorCode.DATA_FIELD_CONFIG_INVALID: "字段配置无效",
    ErrorCode.DATA_GENERATION_PARAMS_INVALID: "生成参数无效",
    
    ErrorCode.DATA_MODEL_ACCESS_DENIED: "数据模型访问被拒绝",
    ErrorCode.DATA_GENERATION_FORBIDDEN: "数据生成被禁止",
    
    ErrorCode.DATA_MODEL_NOT_FOUND: "数据模型不存在",
    ErrorCode.DATA_GENERATION_TASK_NOT_FOUND: "生成任务不存在",
    
    ErrorCode.DATA_MODEL_NAME_EXISTS: "数据模型名称已存在",
    ErrorCode.DATA_GENERATION_FAILED: "数据生成失败",
    ErrorCode.DATA_EXPORT_FAILED: "数据导出失败",
    
    # 模型配置模块错误
    ErrorCode.MODEL_NAME_INVALID: "模型名称无效",
    ErrorCode.MODEL_PLATFORM_INVALID: "模型平台无效",
    ErrorCode.MODEL_API_URL_INVALID: "API地址无效",
    ErrorCode.MODEL_API_KEY_INVALID: "API密钥无效",
    ErrorCode.MODEL_CONFIG_INVALID: "模型配置无效",
    
    ErrorCode.MODEL_ACCESS_DENIED: "模型访问被拒绝",
    ErrorCode.MODEL_CALL_FORBIDDEN: "模型调用被禁止",
    
    ErrorCode.MODEL_NOT_FOUND: "模型不存在",
    ErrorCode.MODEL_CONFIG_NOT_FOUND: "模型配置不存在",
    
    ErrorCode.MODEL_NAME_ALREADY_EXISTS: "模型名称已存在",
    ErrorCode.MODEL_CONNECTION_FAILED: "模型连接失败",
    ErrorCode.MODEL_CALL_FAILED: "模型调用失败",
    ErrorCode.MODEL_HEALTH_CHECK_FAILED: "健康检查失败",
}

# 英文错误消息映射
ERROR_MESSAGES_EN = {
    # System common errors
    ErrorCode.INVALID_REQUEST_PARAMS: "Invalid request parameters",
    ErrorCode.INVALID_JSON_FORMAT: "Invalid JSON format",
    ErrorCode.MISSING_REQUIRED_FIELD: "Missing required field",
    ErrorCode.FIELD_VALUE_INVALID: "Invalid field value",
    ErrorCode.FIELD_LENGTH_INVALID: "Invalid field length",
    
    ErrorCode.UNAUTHORIZED: "Unauthorized access",
    ErrorCode.TOKEN_INVALID: "Invalid token",
    ErrorCode.TOKEN_EXPIRED: "Token expired",
    ErrorCode.PERMISSION_DENIED: "Permission denied",
    ErrorCode.ACCESS_FORBIDDEN: "Access forbidden",
    
    ErrorCode.RESOURCE_NOT_FOUND: "Resource not found",
    ErrorCode.ENDPOINT_NOT_FOUND: "Endpoint not found",
    ErrorCode.METHOD_NOT_ALLOWED: "Method not allowed",
    
    ErrorCode.OPERATION_FAILED: "Operation failed",
    ErrorCode.RESOURCE_CONFLICT: "Resource conflict",
    ErrorCode.OPERATION_NOT_ALLOWED: "Operation not allowed",
    ErrorCode.RATE_LIMIT_EXCEEDED: "Rate limit exceeded",
    
    ErrorCode.INTERNAL_SERVER_ERROR: "Internal server error",
    ErrorCode.DATABASE_ERROR: "Database error",
    ErrorCode.EXTERNAL_SERVICE_ERROR: "External service error",
    ErrorCode.CONFIGURATION_ERROR: "Configuration error",
    ErrorCode.NETWORK_ERROR: "Network error",
    
    # User module errors
    ErrorCode.USER_USERNAME_INVALID: "Invalid username format",
    ErrorCode.USER_PASSWORD_INVALID: "Invalid password format",
    ErrorCode.USER_EMAIL_INVALID: "Invalid email format",
    ErrorCode.USER_PHONE_INVALID: "Invalid phone number format",
    ErrorCode.USER_NICKNAME_INVALID: "Invalid nickname format",
    
    ErrorCode.USER_LOGIN_REQUIRED: "Login required",
    ErrorCode.USER_LOGIN_FAILED: "Login failed",
    ErrorCode.USER_PASSWORD_WRONG: "Wrong password",
    ErrorCode.USER_ACCOUNT_DISABLED: "Account disabled",
    ErrorCode.USER_ACCOUNT_LOCKED: "Account locked",
    
    ErrorCode.USER_NOT_FOUND: "User not found",
    ErrorCode.USER_ROLE_NOT_FOUND: "Role not found",
    
    ErrorCode.USER_ALREADY_EXISTS: "User already exists",
    ErrorCode.USER_EMAIL_ALREADY_EXISTS: "Email already exists",
    ErrorCode.USER_CANNOT_DELETE_SELF: "Cannot delete yourself",
    ErrorCode.USER_CANNOT_MODIFY_ADMIN: "Cannot modify admin",
    
    # Environment module errors
    ErrorCode.ENV_NAME_INVALID: "Invalid environment name",
    ErrorCode.ENV_TYPE_INVALID: "Invalid environment type",
    ErrorCode.ENV_CONFIG_INVALID: "Invalid environment config",
    ErrorCode.ENV_HOST_INVALID: "Invalid host address",
    ErrorCode.ENV_PORT_INVALID: "Invalid port number",
    
    ErrorCode.ENV_ACCESS_DENIED: "Environment access denied",
    ErrorCode.ENV_OPERATION_FORBIDDEN: "Environment operation forbidden",
    
    ErrorCode.ENV_NOT_FOUND: "Environment not found",
    ErrorCode.ENV_CONFIG_NOT_FOUND: "Environment config not found",
    
    ErrorCode.ENV_NAME_ALREADY_EXISTS: "Environment name already exists",
    ErrorCode.ENV_CONNECTION_FAILED: "Environment connection failed",
    ErrorCode.ENV_IN_USE: "Environment is in use",
    ErrorCode.ENV_TEST_FAILED: "Environment test failed",
    
    # 其他模块的英文消息...（为了节省空间，这里省略部分）
}

# 支持的语言列表
SUPPORTED_LANGUAGES = ['zh', 'en']

# 默认语言
DEFAULT_LANGUAGE = 'zh'


class ErrorMessageManager:
    """错误消息管理器"""
    
    def __init__(self):
        self._messages = {
            'zh': ERROR_MESSAGES_ZH,
            'en': ERROR_MESSAGES_EN
        }
    
    def get_message(self, error_code: ErrorCode, language: str = DEFAULT_LANGUAGE) -> str:
        """
        获取错误消息
        
        Args:
            error_code: 错误码
            language: 语言代码
            
        Returns:
            错误消息
        """
        if language not in SUPPORTED_LANGUAGES:
            language = DEFAULT_LANGUAGE
        
        messages = self._messages.get(language, self._messages[DEFAULT_LANGUAGE])
        return messages.get(error_code, f"Unknown error: {error_code}")
    
    def add_custom_message(self, error_code: ErrorCode, message: str, language: str = DEFAULT_LANGUAGE):
        """
        添加自定义错误消息
        
        Args:
            error_code: 错误码
            message: 错误消息
            language: 语言代码
        """
        if language not in self._messages:
            self._messages[language] = {}
        
        self._messages[language][error_code] = message
    
    def get_supported_languages(self) -> list:
        """获取支持的语言列表"""
        return SUPPORTED_LANGUAGES.copy()


# 全局错误消息管理器实例
error_message_manager = ErrorMessageManager()
