<template>
  <div class="task-form-new">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="handleCancel" :icon="ArrowLeft">返回</el-button>
        <div class="header-info">
          <h1>{{ pageTitle }}</h1>
          <!-- <p>{{ pageDescription }}</p> -->
        </div>
      </div>
      <div class="header-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="submitting"
          :disabled="!canSubmit"
        >
          {{ submitButtonText }}
        </el-button>
      </div>
    </div>

    <!-- 分步表单 -->
    <div class="form-container">
      <el-steps :active="currentStep" align-center class="form-steps">
        <el-step title="基本信息" description="设置任务基本信息" />
        <el-step title="故障配置" description="配置故障注入参数" />
        <el-step title="执行计划" description="设置执行方式和时间" />
        <el-step title="确认提交" description="确认配置并提交任务" />
      </el-steps>

      <div class="step-content">
        <!-- 第一步：基本信息 -->
        <BasicInfoStep
          v-show="currentStep === 0"
          ref="basicStepRef"
          :form-data="formData"
          :environments="environments || []"
          :environments-loading="environmentsLoading"
        />

        <!-- 第二步：故障配置 -->
        <FaultConfigStep
          v-show="currentStep === 1"
          ref="faultStepRef"
          :form-data="formData"
          :scenarios="scenarios || []"
          v-model:selected-category="selectedCategory"
          @fault-type-change="handleFaultTypeChange"
        />

        <!-- 第三步：执行计划 -->
        <ScheduleStep
          v-show="currentStep === 2"
          ref="scheduleStepRef"
          :form-data="formData"
        />

        <!-- 第四步：确认提交 -->
        <ConfirmStep
          v-show="currentStep === 3"
          ref="confirmStepRef"
          :form-data="formData"
          :environments="environments || []"
          :scenarios="scenarios || []"
        />
      </div>

      <!-- 步骤导航 -->
      <div class="step-navigation">
        <el-button 
          v-if="currentStep > 0" 
          @click="handlePrevStep"
          :icon="ArrowLeft"
        >
          上一步
        </el-button>
        <el-button 
          v-if="currentStep < 3" 
          type="primary" 
          @click="handleNextStep"
          :icon="ArrowRight"
        >
          下一步
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

// 导入子组件
import BasicInfoStep from './components/BasicInfoStep.vue'
import FaultConfigStep from './components/FaultConfigStep.vue'
import ScheduleStep from './components/ScheduleStep.vue'
import ConfirmStep from './components/ConfirmStep.vue'

// 导入stores
import { useChaosTasksStore } from '@/store/business/chaos/tasks'
import { useChaosScenariosStore } from '@/store/business/chaos/scenarios'
import { useEnvironmentStore } from '@/store/business/environment'

// 导入类型
import type { ChaosTaskCreate } from '@/types/api/chaos'

const router = useRouter()
const route = useRoute()
const chaosTasksStore = useChaosTasksStore()
const scenariosStore = useChaosScenariosStore()
const environmentStore = useEnvironmentStore()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const environmentsLoading = ref(false)
const currentStep = ref(0)
const selectedCategory = ref('系统资源')

// 表单引用
const basicStepRef = ref()
const faultStepRef = ref()
const scheduleStepRef = ref()
const confirmStepRef = ref()

// 数据
const environments = ref<any[]>([])
const scenarios = ref<any[]>([])

// 表单数据
const formData = reactive<ChaosTaskCreate & {
  scheduled_time: string
  periodic_config: {
    minutes?: number
    hours?: number
    days?: number
  }
  cron_expression: string
}>({
  name: '',
  description: '',
  env_ids: [],
  fault_type: '',
  fault_params: {},
  execution_type: 'immediate',
  scheduled_time: '',
  periodic_config: {
    minutes: 0,
    hours: 0,
    days: 0
  },
  cron_expression: '',
  auto_destroy: true,
  max_duration: 300
})

// 计算属性
const taskId = computed(() => Number(route.params.id))
const copyTaskId = computed(() => Number(route.query.copy))
const isEditMode = computed(() => route.name === 'ChaosTaskEdit' && taskId.value)
const isCopyMode = computed(() => !!copyTaskId.value)

const pageTitle = computed(() => {
  if (isEditMode.value) return '编辑混沌测试任务'
  if (isCopyMode.value) return '复制混沌测试任务'
  return '创建混沌测试任务'
})

const pageDescription = computed(() => {
  if (isEditMode.value) return '修改现有的混沌测试任务配置'
  if (isCopyMode.value) return '基于现有任务创建新的混沌测试任务'
  return '创建新的混沌测试任务，配置故障注入参数'
})

const submitButtonText = computed(() => {
  return isEditMode.value ? '更新任务' : '创建任务'
})

const canSubmit = computed(() => {
  if (currentStep.value !== 3) return false
  return confirmStepRef.value?.canSubmit?.() || false
})

// 生命周期
onMounted(async () => {
  await loadInitialData()
  
  // 根据模式加载数据
  if (isEditMode.value && taskId.value) {
    await loadTaskForEdit(taskId.value)
  } else if (isCopyMode.value && copyTaskId.value) {
    await loadTaskForCopy(copyTaskId.value)
  }
})

// 方法
const loadInitialData = async () => {
  await Promise.all([
    loadEnvironments(),
    loadScenarios()
  ])
}

const loadEnvironments = async () => {
  environmentsLoading.value = true
  try {
    const result = await environmentStore.fetchEnvironments()
    if (result) {
      // 根据实际返回的数据结构设置环境数据
      environments.value = result.items || result.records || []
    }
  } catch (error) {
    ElMessage.error('加载环境列表失败')
  } finally {
    environmentsLoading.value = false
  }
}

const loadScenarios = async () => {
  try {
    const result = await scenariosStore.fetchScenarios()
    if (result) {
      scenarios.value = result.records || []
    }
  } catch (error) {
    ElMessage.error('加载场景列表失败')
  }
}

const loadTaskForEdit = async (id: number) => {
  loading.value = true
  try {
    const task = await chaosTasksStore.fetchTask(id)
    
    // 填充表单数据
    Object.assign(formData, {
      name: task.name,
      description: task.description,
      env_ids: task.env_ids || [],
      fault_type: task.fault_type,
      fault_params: task.fault_params || {},
      execution_type: task.execution_type,
      scheduled_time: task.scheduled_time || '',
      auto_destroy: task.auto_destroy,
      max_duration: task.max_duration
    })

    // 根据故障类型设置分类
    setSelectedCategoryByFaultType(task.fault_type)
  } catch (error) {
    ElMessage.error('加载任务数据失败')
    router.push('/chaos/tasks')
  } finally {
    loading.value = false
  }
}

const loadTaskForCopy = async (id: number) => {
  loading.value = true
  try {
    const task = await chaosTasksStore.fetchTask(id)
    
    // 复制任务数据，但重置名称
    Object.assign(formData, {
      name: `${task.name} - 副本`,
      description: task.description,
      env_ids: [...(task.env_ids || [])],
      fault_type: task.fault_type,
      fault_params: { ...(task.fault_params || {}) },
      execution_type: task.execution_type,
      auto_destroy: task.auto_destroy,
      max_duration: task.max_duration
    })

    setSelectedCategoryByFaultType(task.fault_type)
  } catch (error) {
    ElMessage.error('加载任务数据失败')
    router.push('/chaos/tasks')
  } finally {
    loading.value = false
  }
}

const setSelectedCategoryByFaultType = (faultType: string) => {
  const scenario = scenarios.value.find(s => s.fault_type === faultType)
  if (scenario?.category) {
    selectedCategory.value = scenario.category
  }
}

const handleFaultTypeChange = (faultType: string) => {
  // 子组件已处理参数重置
}

const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const handleNextStep = async () => {
  // 验证当前步骤
  let isValid = true
  
  switch (currentStep.value) {
    case 0:
      isValid = await basicStepRef.value?.validate?.() || false
      break
    case 1:
      isValid = await faultStepRef.value?.validate?.() || false
      break
    case 2:
      isValid = await scheduleStepRef.value?.validate?.() || false
      break
  }
  
  if (isValid && currentStep.value < 3) {
    currentStep.value++
  }
}

const handleSubmit = async () => {
  if (!canSubmit.value) {
    ElMessage.warning('请确认所有配置信息')
    return
  }

  submitting.value = true
  try {
    // 清理提交数据，移除空的可选字段
    const submitData = { ...formData }

    // 根据执行类型清理不需要的字段
    if (submitData.execution_type !== 'scheduled') {
      delete (submitData as any).scheduled_time
    }

    if (submitData.execution_type !== 'periodic') {
      delete (submitData as any).periodic_config
    } else {
      // 清理周期配置中的0值
      const config = submitData.periodic_config
      if (config) {
        if (!config.minutes) delete config.minutes
        if (!config.hours) delete config.hours
        if (!config.days) delete config.days
      }
    }

    if (submitData.execution_type !== 'cron') {
      delete (submitData as any).cron_expression
    }

    // 移除空的描述
    if (!submitData.description || submitData.description.trim() === '') {
      delete (submitData as any).description
    }

    if (isEditMode.value) {
      await chaosTasksStore.updateTask(taskId.value, submitData)
      ElMessage.success('任务更新成功')
    } else {
      await chaosTasksStore.createTask(submitData)
      ElMessage.success('任务创建成功')
    }

    router.push('/chaos/tasks')
  } catch (error) {
    ElMessage.error(isEditMode.value ? '任务更新失败' : '任务创建失败')
  } finally {
    submitting.value = false
  }
}

const handleCancel = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消吗？未保存的更改将丢失。',
      '确认取消',
      { type: 'warning' }
    )
    router.push('/chaos/tasks')
  } catch {
    // 用户取消
  }
}
</script>

<style scoped>
.task-form-new {
  min-height: 100vh;
}

.page-header {
  padding-bottom:  15px;
  border-bottom: 1px solid var(--el-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-info h1 {
  /* margin: 0 0 4px 0; */
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

.header-info p {
  margin: 0;
  color: #606266;
  font-size: 10px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.form-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px;
}

.form-steps {
  margin-bottom: 40px;
}

.step-content {
  border-radius: 8px;
  border: 1px solid var(--el-border-color);
  min-height: 500px;
}

.step-navigation {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
}
</style>
