"""
用户相关 Pydantic 模式
参考示例代码的简洁设计，移除复杂的前端适配
"""
from typing import List, Optional
from pydantic import BaseModel, Field, EmailStr, validator
from datetime import datetime

from app.schemas.base import BaseResponseSchema, BaseQuery, BasePageResponse


# ------------------------------
# 基础模型（公共字段）
# ------------------------------
class UserBase(BaseModel):
    """用户基础模型（包含公共字段）"""
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱")
    nickname: Optional[str] = Field(None, max_length=50, description="昵称")
    avatar: Optional[str] = Field(None, description="头像URL")
    description: Optional[str] = Field(None, description="个人简介")
    is_active: Optional[bool] = Field(None, description="是否激活")


# ------------------------------
# 请求模型（接收客户端数据）
# ------------------------------
class UserCreate(UserBase):
    """创建用户请求模型（必填字段）"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱")
    password: str = Field(..., min_length=6, max_length=50, description="密码")  # 明文密码，服务层加密
    is_superuser: bool = Field(default=False, description="是否超级用户")

    @validator('username')
    def validate_username(cls, v):
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('用户名只能包含字母、数字、下划线和连字符')
        return v


class UserUpdate(UserBase):
    """更新用户请求模型（可选字段）"""
    password: Optional[str] = Field(None, min_length=6, max_length=50, description="新密码（可选）")
    is_superuser: Optional[bool] = Field(None, description="是否超级用户")


class UserQuery(BaseQuery):
    """用户查询参数模型"""
    status: Optional[str] = Field(None, description="用户状态筛选")
    is_active: Optional[bool] = Field(None, description="激活状态筛选")


# ------------------------------
# 响应模型（返回给客户端的数据）
# ------------------------------
class UserResponse(UserBase, BaseResponseSchema):
    """用户详情响应模型"""
    id: int = Field(..., description="用户ID")
    is_superuser: bool = Field(..., description="是否超级管理员")
    status: str = Field(default="1", description="用户状态")
    last_login_at: Optional[str] = Field(None, description="最后登录时间")
    roles: List[str] = Field(default=[], description="角色代码列表")

    class Config:
        from_attributes = True  # Pydantic V2 语法，支持从ORM模型转换


class UserPageResponse(BasePageResponse[UserResponse]):
    """用户列表分页响应模型"""
    pass


class UserUpdatePassword(BaseModel):
    """用户密码更新模式"""
    old_password: str = Field(..., description="旧密码")
    new_password: str = Field(..., min_length=6, max_length=50, description="新密码")

