"""
环境管理API路由
"""
from typing import List, Dict, Any
from fastapi import APIRouter, Depends

from app.core.dependencies import EnvironmentServiceType
from app.core.responses import response_builder
from app.schemas.env.env import EnvironmentCreate, EnvironmentUpdate, EnvironmentResponse, EnvironmentQuery, EnvironmentPageResponse, ConnectionTestRequest, ConnectionTestResponse
from app.models.user.user import User
from app.api.deps import get_current_active_user

router = APIRouter()





@router.post("", response_model=EnvironmentResponse, status_code=201, summary="创建环境")
async def create_environment(
    service: EnvironmentServiceType,
    env_data: EnvironmentCreate,
    current_user: User = Depends(get_current_active_user)
):
    """创建新环境，需要管理员权限"""
    environment = await service.create_environment(env_data, str(current_user.id))
    return response_builder.created(environment)


@router.get("", response_model=EnvironmentPageResponse, summary="查询环境列表")
async def list_environments(
    service: EnvironmentServiceType,
    query: EnvironmentQuery = Depends(),  # 自动接收并校验查询参数
    _current_user: User = Depends(get_current_active_user)
):
    """查询环境列表，支持关键词搜索、类型筛选和分页"""
    result = await service.list_environments(query)
    return response_builder.success(result)


@router.get("/{env_id}", response_model=EnvironmentResponse, summary="获取环境详情")
async def get_environment(
    service: EnvironmentServiceType,
    env_id: int,
    _current_user: User = Depends(get_current_active_user)
):
    """获取指定ID的环境详情"""
    environment = await service.get_environment_by_id(env_id)
    return response_builder.success(environment)


@router.put("/{env_id}", response_model=EnvironmentResponse, summary="更新环境")
async def update_environment(
    service: EnvironmentServiceType,
    env_id: int,
    env_data: EnvironmentUpdate,
    current_user: User = Depends(get_current_active_user)
):
    """更新环境信息，支持修改基本信息和配置"""
    environment = await service.update_environment(env_id, env_data, str(current_user.id))
    return response_builder.success(environment)


@router.delete("/{env_id}", status_code=204, summary="删除环境")
async def delete_environment(
    service: EnvironmentServiceType,
    env_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """删除环境"""
    await service.delete_environment(env_id)

@router.post("/test", response_model=ConnectionTestResponse, summary="测试环境连接")
async def test_connection(
    service: EnvironmentServiceType,
    test_request: ConnectionTestRequest,
    _current_user: User = Depends(get_current_active_user)
):
    """测试环境连接（通用接口，不更新环境状态）"""
    result = await service.test_connection(test_request)
    return response_builder.success(result)


@router.post("/{env_id}/test", response_model=ConnectionTestResponse, summary="测试指定环境连接")
async def test_environment_connection(
    service: EnvironmentServiceType,
    env_id: int,
    test_request: ConnectionTestRequest = ConnectionTestRequest(),
    _current_user: User = Depends(get_current_active_user)
):
    """测试指定环境的连接（会更新环境状态）"""
    result = await service.test_environment_connection(env_id, test_request)
    return response_builder.success(result)


@router.post("/batch-test", response_model=List[dict], summary="批量测试环境连接")
async def batch_test_connections(
    service: EnvironmentServiceType,
    env_ids: List[int],
    _current_user: User = Depends(get_current_active_user)
):
    """批量测试环境连接"""
    results = await service.batch_test_connections(env_ids)
    return response_builder.success(results)


@router.get("/stats/overview", response_model=dict, summary="获取环境统计信息")
async def get_environment_stats(
    service: EnvironmentServiceType,
    _current_user: User = Depends(get_current_active_user)
):
    """获取环境统计信息"""
    stats = await service.get_environment_stats()
    return response_builder.success(stats)


@router.get("/types/supported", response_model=List[Dict[str, Any]], summary="获取支持的环境类型")
async def get_supported_types(
    service: EnvironmentServiceType,
    _current_user: User = Depends(get_current_active_user)
):
    """获取系统支持的环境类型"""
    types = await service.get_supported_types()
    return response_builder.success(types)