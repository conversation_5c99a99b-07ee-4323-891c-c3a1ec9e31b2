"""
全局调度管理器实例
提供统一的调度器访问接口
"""
import logging
from datetime import datetime
from typing import Optional, Callable, Any, Dict, List, Union

from .schedule_base import ScheduleManager, ScheduleTask

logger = logging.getLogger(__name__)

# 全局调度管理器实例
schedule_manager = ScheduleManager()


async def start_schedule_manager():
    """启动全局调度管理器"""
    try:
        await schedule_manager.start()
        logger.info("全局调度管理器启动成功")
    except Exception as e:
        logger.error(f"启动全局调度管理器失败: {str(e)}")
        raise


async def shutdown_schedule_manager(wait: bool = True):
    """关闭全局调度管理器"""
    try:
        await schedule_manager.shutdown(wait)
        logger.info("全局调度管理器已关闭")
    except Exception as e:
        logger.error(f"关闭全局调度管理器失败: {str(e)}")


# 便捷函数
def create_task(
    task_id: str,
    name: str,
    func: Callable,
    task_type: str = "general",
    description: Optional[str] = None,
    max_instances: int = 1,
    misfire_grace_time: int = 300,
    args: Optional[tuple] = None,
    kwargs: Optional[dict] = None
) -> ScheduleTask:
    """创建调度任务"""
    return ScheduleTask(
        task_id=task_id,
        name=name,
        func=func,
        task_type=task_type,
        description=description,
        max_instances=max_instances,
        misfire_grace_time=misfire_grace_time,
        args=args,
        kwargs=kwargs
    )


def add_date_task(
    task_id: str,
    name: str,
    func: Callable,
    run_date: datetime,
    task_type: str = "general",
    description: Optional[str] = None,
    args: Optional[tuple] = None,
    kwargs: Optional[dict] = None,
    **task_options
) -> str:
    """添加单次执行任务的便捷函数"""
    task = create_task(
        task_id=task_id,
        name=name,
        func=func,
        task_type=task_type,
        description=description,
        args=args,
        kwargs=kwargs,
        **task_options
    )
    return schedule_manager.add_date_task(task, run_date)


def add_interval_task(
    task_id: str,
    name: str,
    func: Callable,
    seconds: int = 0,
    minutes: int = 0,
    hours: int = 0,
    days: int = 0,
    task_type: str = "general",
    description: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    args: Optional[tuple] = None,
    kwargs: Optional[dict] = None,
    **task_options
) -> str:
    """添加间隔执行任务的便捷函数"""
    task = create_task(
        task_id=task_id,
        name=name,
        func=func,
        task_type=task_type,
        description=description,
        args=args,
        kwargs=kwargs,
        **task_options
    )
    return schedule_manager.add_interval_task(
        task, seconds, minutes, hours, days, start_date, end_date
    )


def add_cron_task(
    task_id: str,
    name: str,
    func: Callable,
    year: Union[int, str] = None,
    month: Union[int, str] = None,
    day: Union[int, str] = None,
    week: Union[int, str] = None,
    day_of_week: Union[int, str] = None,
    hour: Union[int, str] = None,
    minute: Union[int, str] = None,
    second: Union[int, str] = None,
    task_type: str = "general",
    description: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    args: Optional[tuple] = None,
    kwargs: Optional[dict] = None,
    **task_options
) -> str:
    """添加Cron表达式任务的便捷函数"""
    task = create_task(
        task_id=task_id,
        name=name,
        func=func,
        task_type=task_type,
        description=description,
        args=args,
        kwargs=kwargs,
        **task_options
    )
    return schedule_manager.add_cron_task(
        task, year, month, day, week, day_of_week, hour, minute, second, start_date, end_date
    )


def remove_task(task_id: str) -> bool:
    """移除任务的便捷函数"""
    return schedule_manager.remove_task(task_id)


def pause_task(task_id: str) -> bool:
    """暂停任务的便捷函数"""
    return schedule_manager.pause_task(task_id)


def resume_task(task_id: str) -> bool:
    """恢复任务的便捷函数"""
    return schedule_manager.resume_task(task_id)


def get_tasks() -> List[ScheduleTask]:
    """获取所有任务的便捷函数"""
    return schedule_manager.get_tasks()


def get_task(task_id: str) -> Optional[ScheduleTask]:
    """获取指定任务的便捷函数"""
    return schedule_manager.get_task(task_id)


def get_jobs() -> list:
    """获取所有作业的便捷函数"""
    return schedule_manager.get_jobs()


def get_job(job_id: str):
    """获取指定作业的便捷函数"""
    return schedule_manager.get_job(job_id)


def is_running() -> bool:
    """检查调度器是否运行中"""
    return schedule_manager.is_running


def remove_job(task_id: str) -> bool:
    """移除指定的作业"""
    return schedule_manager.remove_task(task_id)
