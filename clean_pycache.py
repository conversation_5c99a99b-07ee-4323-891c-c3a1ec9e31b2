import os
import shutil

def clean_pycache():
    """清理项目中的所有__pycache__目录和.pyc/.pyo文件"""
    # 从当前脚本获取执行脚本的位置开始遍历（通常是项目根目录）
    root_dir = os.path.dirname(os.path.os.path.abspath(__file__))
    
    for dirpath, dirnames, filenames in os.walk(root_dir):
        # 清理__pycache__目录
        if '__pycache__' in dirnames:
            pycache_path = os.path.join(dirpath, '__pycache__')
            try:
                shutil.rmtree(pycache_path)
                print(f"已删除: {pycache_path}")
            except Exception as e:
                print(f"删除失败 {pycache_path}: {e}")
        
        # 清理单独的.pyc和.pyo文件
        for filename in filenames:
            if filename.endswith(('.pyc', '.pyo')):
                pyc_path = os.path.join(dirpath, filename)
                try:
                    os.remove(pyc_path)
                    print(f"已删除: {pyc_path}")
                except Exception as e:
                    print(f"删除失败 {pyc_path}: {e}")

if __name__ == "__main__":
    print("开始清理__pycache__和编译文件...")
    clean_pycache()
    print("清理完成!")
    