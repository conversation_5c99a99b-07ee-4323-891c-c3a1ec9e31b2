"""
环境管理仓储层
实现环境数据的数据库访问逻辑
"""
from typing import List, Optional, Dict, Any
from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.repositories.base import BaseRepository
from app.models.env.env import Environment
from app.schemas.env.env import EnvironmentCreate, EnvironmentUpdate


class EnvironmentRepository(BaseRepository[Environment, EnvironmentCreate, EnvironmentUpdate]):
    """环境管理仓储"""

    def __init__(self, db: AsyncSession):
        super().__init__(Environment, db)

    async def get_by_name(self, name: str) -> Optional[Environment]:
        """
        根据环境名称获取环境
        
        Args:
            name: 环境名称
            
        Returns:
            Optional[Environment]: 环境实例
        """
        stmt = select(Environment).where(Environment.name == name)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_type(self, env_type: str) -> List[Environment]:
        """
        根据环境类型获取环境列表
        
        Args:
            env_type: 环境类型
            
        Returns:
            List[Environment]: 环境列表
        """
        stmt = select(Environment).where(Environment.type == env_type).order_by(Environment.name)
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def list(
        self,
        keyword: Optional[str] = None,
        env_type: Optional[str] = None,
        status: Optional[str] = None,
        tags: Optional[str] = None,
        offset: int = 0,
        limit: int = 10
    ) -> tuple[List[Environment], int]:
        """
        查询环境列表（带筛选和分页）

        Args:
            keyword: 关键词搜索（名称、描述、主机）
            env_type: 环境类型筛选
            status: 状态筛选
            tags: 标签筛选
            offset: 偏移量
            limit: 限制数量

        Returns:
            tuple[List[Environment], int]: (环境列表, 总数)
        """
        # 构建查询条件
        conditions = []
        
        if keyword:
            keyword_condition = or_(
                Environment.name.ilike(f"%{keyword}%"),
                Environment.description.ilike(f"%{keyword}%"),
                Environment.host.ilike(f"%{keyword}%")
            )
            conditions.append(keyword_condition)
        
        if env_type:
            conditions.append(Environment.type == env_type)
        
        if status:
            conditions.append(Environment.status == status)
        
        if tags:
            # 支持单个标签字符串的搜索
            conditions.append(Environment.tags.ilike(f"%{tags}%"))
        
        # 构建基础查询
        base_stmt = select(Environment)
        if conditions:
            base_stmt = base_stmt.where(and_(*conditions))
        
        # 获取总数
        count_stmt = select(func.count(Environment.id))
        if conditions:
            count_stmt = count_stmt.where(and_(*conditions))
        
        count_result = await self.db.execute(count_stmt)
        total = count_result.scalar()
        
        # 获取分页数据
        data_stmt = base_stmt.order_by(Environment.created_at.desc()).limit(limit).offset(offset)
        data_result = await self.db.execute(data_stmt)
        environments = data_result.scalars().all()

        return environments, total

    async def get_env_types_stats(self) -> Dict[str, int]:
        """
        获取环境类型统计
        
        Returns:
            Dict[str, int]: 环境类型统计 {类型: 数量}
        """
        stmt = select(Environment.type, func.count(Environment.id)).group_by(Environment.type)
        result = await self.db.execute(stmt)
        return {env_type: count for env_type, count in result.all()}

    async def get_status_stats(self) -> Dict[str, int]:
        """
        获取环境状态统计

        Returns:
            Dict[str, int]: 环境状态统计 {状态: 数量}
        """
        stmt = select(Environment.status, func.count(Environment.id)).group_by(Environment.status)
        result = await self.db.execute(stmt)
        return {status: count for status, count in result.all()}

    async def get_detailed_type_stats(self) -> List[Dict[str, Any]]:
        """
        获取详细的环境类型统计

        Returns:
            List[Dict[str, Any]]: 详细类型统计
        """
        from sqlalchemy import case

        result = await self.db.execute(
            select(
                Environment.type,
                func.count(Environment.id).label('total'),
                func.sum(case((Environment.status == 'connected', 1), else_=0)).label('connected'),
                func.sum(case((Environment.status == 'failed', 1), else_=0)).label('failed'),
                func.sum(case((Environment.status == 'unknown', 1), else_=0)).label('unknown')
            ).group_by(Environment.type)
        )

        stats = []
        for row in result:
            stats.append({
                'type': row.type,
                'total': row.total,
                'connected': row.connected or 0,
                'failed': row.failed or 0,
                'unknown': row.unknown or 0
            })

        return stats

    async def update_status(self, env_id: int, status: str, last_test_time: Optional[str] = None) -> bool:
        """
        更新环境状态
        
        Args:
            env_id: 环境ID
            status: 新状态
            last_test_time: 最后测试时间
            
        Returns:
            bool: 是否更新成功
        """
        update_data = {"status": status}
        if last_test_time:
            update_data["last_test_time"] = last_test_time
        
        stmt = update(Environment).where(Environment.id == env_id).values(**update_data)
        result = await self.db.execute(stmt)
        await self.db.commit()
        
        return result.rowcount > 0

    async def batch_update_status(self, status_updates: List[Dict[str, Any]]) -> int:
        """
        批量更新环境状态
        
        Args:
            status_updates: 状态更新列表 [{"id": 1, "status": "connected", "last_test_time": "..."}]
            
        Returns:
            int: 更新的记录数
        """
        if not status_updates:
            return 0
        
        updated_count = 0
        for update_data in status_updates:
            env_id = update_data.pop("id")
            stmt = update(Environment).where(Environment.id == env_id).values(**update_data)
            result = await self.db.execute(stmt)
            updated_count += result.rowcount
        
        await self.db.commit()
        return updated_count

    async def get_environments_by_ids(self, env_ids: List[int]) -> List[Environment]:
        """
        根据ID列表获取环境
        
        Args:
            env_ids: 环境ID列表
            
        Returns:
            List[Environment]: 环境列表
        """
        if not env_ids:
            return []
        
        stmt = select(Environment).where(Environment.id.in_(env_ids))
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def check_name_exists(self, name: str, exclude_id: Optional[int] = None) -> bool:
        """
        检查环境名称是否已存在
        
        Args:
            name: 环境名称
            exclude_id: 排除的环境ID（用于更新时检查）
            
        Returns:
            bool: 是否存在
        """
        conditions = [Environment.name == name]
        if exclude_id:
            conditions.append(Environment.id != exclude_id)
        
        stmt = select(Environment.id).where(and_(*conditions))
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none() is not None

    async def get_recent_environments(self, limit: int = 10) -> List[Environment]:
        """
        获取最近更新的环境
        
        Args:
            limit: 限制数量
            
        Returns:
            List[Environment]: 最近更新的环境列表
        """
        stmt = select(Environment).order_by(Environment.updated_at.desc()).limit(limit)
        result = await self.db.execute(stmt)
        return result.scalars().all() 