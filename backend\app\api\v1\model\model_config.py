"""
模型配置管理API路由
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, Query
from fastapi.responses import StreamingResponse

from app.core.dependencies import ModelConfigServiceType
from app.core.responses import response_builder
from app.schemas.model.model_config import (
    ModelConfigCreate, ModelConfigUpdate, ModelConfigResponse,
    ModelHealthCheckRequest, ModelHealthCheckResponse,
    ModelCallRequest, ModelCallResponse, ModelConfigQuery, ModelConfigPageResponse
)
from app.models.user.user import User
from app.api.deps import get_current_active_user

router = APIRouter()


@router.post("", response_model=ModelConfigResponse, status_code=201, summary="创建模型配置")
async def create_model_config(
    service: ModelConfigServiceType,
    model_data: ModelConfigCreate,
    current_user: User = Depends(get_current_active_user)
):
    """创建新的模型配置，需要管理员权限"""
    model_config = await service.create_model_config(model_data, str(current_user.id))
    return response_builder.created(model_config)


@router.get("", response_model=ModelConfigPageResponse, summary="查询模型配置列表")
async def list_model_configs(
    service: ModelConfigServiceType,
    query: ModelConfigQuery = Depends(),  # 自动接收并校验查询参数
    _current_user: User = Depends(get_current_active_user)
):
    """查询模型配置列表，支持关键词搜索、平台筛选和分页"""
    result = await service.list_model_configs(query)
    return response_builder.success(result)


@router.get("/{model_id}", response_model=ModelConfigResponse, summary="获取模型配置详情")
async def get_model_config(
    service: ModelConfigServiceType,
    model_id: int,
    _current_user: User = Depends(get_current_active_user)
):
    """获取指定ID的模型配置详情"""
    model_config = await service.get_by_id(model_id)
    return response_builder.success(model_config)


@router.put("/{model_id}", response_model=ModelConfigResponse, summary="更新模型配置")
async def update_model_config(
    service: ModelConfigServiceType,
    model_id: int,
    model_data: ModelConfigUpdate,
    current_user: User = Depends(get_current_active_user)
):
    """更新模型配置信息，支持修改基本信息和配置"""
    model_config = await service.update_model_config(model_id, model_data, str(current_user.id))
    return response_builder.success(model_config)


@router.delete("/{model_id}", status_code=204, summary="删除模型配置")
async def delete_model_config(
    service: ModelConfigServiceType,
    model_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """删除模型配置"""
    await service.delete(model_id)
    # HTTP 204 No Content - 删除成功，无响应体


@router.post("/{model_id}/enable", response_model=ModelConfigResponse, summary="启用模型")
async def enable_model(
    service: ModelConfigServiceType,
    model_id: int,
    _current_user: User = Depends(get_current_active_user)
):
    """启用模型"""
    model_config = await service.enable_model(model_id)
    return response_builder.success(model_config)


@router.post("/{model_id}/disable", response_model=ModelConfigResponse, summary="停用模型")
async def disable_model(
    service: ModelConfigServiceType,
    model_id: int,
    _current_user: User = Depends(get_current_active_user)
):
    """停用模型"""
    model_config = await service.disable_model(model_id)
    return response_builder.success(model_config)


@router.get("/available/list", response_model=List[ModelConfigResponse], summary="获取可用模型列表")
async def get_available_models(
    service: ModelConfigServiceType
):
    """获取所有可用的模型配置（启用且健康）"""
    models = await service.get_available_models()
    return response_builder.success(models)


@router.post("/{model_id}/health-check", response_model=ModelHealthCheckResponse, summary="检查指定模型健康状态")
async def health_check_model(
    service: ModelConfigServiceType,
    model_id: int,
    timeout_seconds: int = Query(None, ge=1, le=60, description="检查超时时间（秒）"),
    _current_user: User = Depends(get_current_active_user)
):
    """检查指定模型的健康状态"""
    result = await service.health_check_model(model_id, timeout_seconds)
    return response_builder.success(result)


@router.post("/health-check/batch", response_model=List[ModelHealthCheckResponse], summary="批量健康检查")
async def batch_health_check(
    service: ModelConfigServiceType,
    request: ModelHealthCheckRequest
):
    """批量检查模型健康状态"""
    results = await service.batch_health_check(request.model_ids, request.timeout_seconds)
    return response_builder.success(results)


@router.post("/test", response_model=dict, summary="测试模型配置连接")
async def test_model_config(
    model_data: ModelConfigCreate,
    service: ModelConfigServiceType
):
    """测试模型配置连接"""
    result = await service.test_model_connection(model_data)
    return response_builder.success(result)


@router.post("/call", response_model=ModelCallResponse, summary="调用模型")
async def call_model(
    request: ModelCallRequest,
    service: ModelConfigServiceType
):
    """统一模型调用接口"""
    result = await service.call_model(request)
    return response_builder.success(result)


@router.post("/call/stream", summary="调用模型（流式）")
async def call_model_stream(
    request: ModelCallRequest,
    service: ModelConfigServiceType
    # 临时移除认证，方便测试
    # _current_user: User = Depends(get_current_active_user)
):
    """统一模型调用接口（流式）"""
    # 业务逻辑交给Service层处理，API层只负责返回StreamingResponse
    # 注意：这里不能用await，因为返回的是异步生成器
    stream_generator = service.call_model_stream_response(request)

    return StreamingResponse(
        stream_generator,
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/plain; charset=utf-8"
        }
    )


@router.get("/stats/overview", response_model=dict, summary="获取模型统计信息")
async def get_model_stats(
    service: ModelConfigServiceType
):
    """获取模型统计信息"""
    stats = await service.get_platform_stats()
    return response_builder.success(stats)
