"""
统一异常处理系统
定义业务异常和异常处理器，集成错误码体系
"""
from typing import Any, Dict, Optional
from fastapi import HTTPException, status
from datetime import datetime
import uuid

from .error_codes import ErrorCode, get_http_status_code
from .error_messages import error_message_manager


class BusinessException(Exception):
    """
    业务异常基类
    用于业务逻辑层抛出的异常，集成统一错误码体系
    """

    def __init__(
        self,
        error_code: ErrorCode,
        message: Optional[str] = None,
        details: Any = None,
        language: str = 'zh'
    ):
        self.error_code = error_code
        self.http_code = get_http_status_code(error_code)
        self.message = message or error_message_manager.get_message(error_code, language)
        self.details = details
        self.timestamp = datetime.utcnow().isoformat()
        self.request_id = str(uuid.uuid4())[:8]
        super().__init__(self.message)


class ValidationError(BusinessException):
    """数据验证异常"""
    def __init__(self, error_code: ErrorCode = ErrorCode.FIELD_VALUE_INVALID, message: Optional[str] = None, details: Any = None):
        super().__init__(error_code, message, details)


class NotFoundError(BusinessException):
    """资源不存在异常"""
    def __init__(self, error_code: ErrorCode = ErrorCode.RESOURCE_NOT_FOUND, message: Optional[str] = None, details: Any = None):
        super().__init__(error_code, message, details)


class PermissionError(BusinessException):
    """权限不足异常"""
    def __init__(self, error_code: ErrorCode = ErrorCode.PERMISSION_DENIED, message: Optional[str] = None, details: Any = None):
        super().__init__(error_code, message, details)


class ConflictError(BusinessException):
    """资源冲突异常"""
    def __init__(self, error_code: ErrorCode = ErrorCode.RESOURCE_CONFLICT, message: Optional[str] = None, details: Any = None):
        super().__init__(error_code, message, details)


class UnauthorizedError(BusinessException):
    """认证失败异常"""
    def __init__(self, error_code: ErrorCode = ErrorCode.UNAUTHORIZED, message: Optional[str] = None, details: Any = None):
        super().__init__(error_code, message, details)


class ExceptionHandler:
    """
    异常处理器
    将业务异常转换为HTTP异常
    """
    
    @staticmethod
    def to_http_exception(exc: BusinessException) -> HTTPException:
        """
        将业务异常转换为HTTP异常

        Args:
            exc: 业务异常

        Returns:
            HTTP异常
        """
        return HTTPException(
            status_code=exc.http_code,
            detail={
                "message": exc.message,
                "error_code": exc.error_code.value,
                "http_code": exc.http_code,
                "details": exc.details,
                "timestamp": exc.timestamp,
                "request_id": exc.request_id
            }
        )
    
    @staticmethod
    def create_exception(
        exc_type: type,
        message: str,
        data: Any = None
    ) -> BusinessException:
        """
        创建业务异常

        Args:
            exc_type: 异常类型
            message: 异常消息
            data: 异常数据

        Returns:
            业务异常实例
        """
        # 使用通用错误码创建异常，确保符合新的构造函数签名
        return exc_type(ErrorCode.OPERATION_FAILED, message, data)


# 全局异常处理器实例
exception_handler = ExceptionHandler()


# 快捷异常创建函数 - 使用新的错误码体系
def raise_not_found(error_code: ErrorCode = ErrorCode.RESOURCE_NOT_FOUND, message: Optional[str] = None, details: Any = None) -> None:
    """抛出资源不存在异常"""
    raise NotFoundError(error_code, message, details)


def raise_validation_error(error_code: ErrorCode = ErrorCode.FIELD_VALUE_INVALID, message: Optional[str] = None, details: Any = None) -> None:
    """抛出数据验证异常"""
    raise ValidationError(error_code, message, details)


def raise_permission_error(error_code: ErrorCode = ErrorCode.PERMISSION_DENIED, message: Optional[str] = None, details: Any = None) -> None:
    """抛出权限不足异常"""
    raise PermissionError(error_code, message, details)


def raise_conflict_error(error_code: ErrorCode = ErrorCode.RESOURCE_CONFLICT, message: Optional[str] = None, details: Any = None) -> None:
    """抛出资源冲突异常"""
    raise ConflictError(error_code, message, details)


def raise_unauthorized_error(error_code: ErrorCode = ErrorCode.UNAUTHORIZED, message: Optional[str] = None, details: Any = None) -> None:
    """抛出认证失败异常"""
    raise UnauthorizedError(error_code, message, details)


# 便捷的业务异常抛出函数
def raise_business_error(error_code: ErrorCode, message: Optional[str] = None, details: Any = None) -> None:
    """抛出业务异常"""
    raise BusinessException(error_code, message, details)


# 用户模块专用异常抛出函数
def raise_user_not_found(user_id: Any = None) -> None:
    """抛出用户不存在异常"""
    message = f"用户ID {user_id} 不存在" if user_id else None
    raise_business_error(ErrorCode.USER_NOT_FOUND, message)


def raise_user_already_exists(username: str = None) -> None:
    """抛出用户已存在异常"""
    message = f"用户名 '{username}' 已存在" if username else None
    raise_business_error(ErrorCode.USER_ALREADY_EXISTS, message)


def raise_user_login_failed(reason: str = None) -> None:
    """抛出登录失败异常"""
    message = f"登录失败: {reason}" if reason else None
    raise_business_error(ErrorCode.USER_LOGIN_FAILED, message)


# 环境模块专用异常抛出函数
def raise_env_not_found(env_id: Any = None) -> None:
    """抛出环境不存在异常"""
    message = f"环境ID {env_id} 不存在" if env_id else None
    raise_business_error(ErrorCode.ENV_NOT_FOUND, message)


def raise_env_connection_failed(host: str = None) -> None:
    """抛出环境连接失败异常"""
    message = f"环境连接失败: {host}" if host else None
    raise_business_error(ErrorCode.ENV_CONNECTION_FAILED, message)


# 模型配置模块专用异常抛出函数
def raise_model_not_found(model_id: Any = None) -> None:
    """抛出模型不存在异常"""
    message = f"模型ID {model_id} 不存在" if model_id else None
    raise_business_error(ErrorCode.MODEL_NOT_FOUND, message)


def raise_model_call_failed(error_msg: str = None) -> None:
    """抛出模型调用失败异常"""
    message = f"模型调用失败: {error_msg}" if error_msg else None
    raise_business_error(ErrorCode.MODEL_CALL_FAILED, message)