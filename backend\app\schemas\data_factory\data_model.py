"""
数据模型相关Schema定义
"""
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator,ConfigDict
from app.schemas.base import BaseCreateSchema, BaseUpdateSchema, BaseResponseSchema


class FieldConfig(BaseModel):
    """字段配置Schema"""
    name: str = Field(..., min_length=1, max_length=50, description="字段名称")
    type: str = Field(..., description="字段类型：string/integer/decimal/boolean/date/datetime")
    generator: str = Field(..., description="生成器类型：uuid/name/phone/email/range/sequence等")
    description: Optional[str] = Field(None, max_length=200, description="字段描述")
    required: bool = Field(default=True, description="是否必填")
    options: Optional[Dict[str, Any]] = Field(None, description="生成器选项配置")
    
    @validator('name')
    def validate_field_name(cls, v):
        """验证字段名称格式"""
        import re
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', v):
            raise ValueError('字段名称只能包含字母、数字和下划线，且不能以数字开头')
        return v
    
    @validator('type')
    def validate_field_type(cls, v):
        """验证字段类型"""
        allowed_types = ['string', 'integer', 'decimal', 'boolean', 'date', 'datetime']
        if v not in allowed_types:
            raise ValueError(f'字段类型必须是以下之一：{", ".join(allowed_types)}')
        return v


class DataModelBase(BaseModel):
    """数据模型基础Schema"""
    name: str = Field(..., min_length=1, max_length=100, description="模型名称")
    description: Optional[str] = Field(None, max_length=1000, description="模型描述")
    version: str = Field(default="1.0.0", max_length=20, description="版本号")
    category: Optional[str] = Field(None, max_length=50, description="模型分类")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    fields_config: List[FieldConfig] = Field(..., min_items=1, description="字段配置列表")
    status: str = Field(default="1", pattern="^[1-2]$", description="状态：1-启用，2-禁用")
    
    @validator('name')
    def validate_model_name(cls, v):
        """验证模型名称格式"""
        import re
        if not re.match(r'^[a-zA-Z0-9_\u4e00-\u9fa5]+$', v):
            raise ValueError('模型名称只能包含字母、数字、下划线和中文字符')
        return v
    
    @validator('fields_config')
    def validate_fields_unique(cls, v):
        """验证字段名称唯一性"""
        field_names = [field.name for field in v]
        if len(field_names) != len(set(field_names)):
            raise ValueError('字段名称不能重复')
        return v


class DataModelCreate(DataModelBase, BaseCreateSchema):
    """创建数据模型Schema"""
    pass


class DataModelUpdate(BaseUpdateSchema):
    """更新数据模型Schema"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="模型名称")
    description: Optional[str] = Field(None, max_length=1000, description="模型描述")
    version: Optional[str] = Field(None, max_length=20, description="版本号")
    category: Optional[str] = Field(None, max_length=50, description="模型分类")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    fields_config: Optional[List[FieldConfig]] = Field(None, description="字段配置列表")
    status: Optional[str] = Field(None, pattern="^[1-2]$", description="状态：1-启用，2-禁用")


class DataModelResponse(BaseResponseSchema):
    """数据模型响应Schema"""
    name: str = Field(description="模型名称")
    description: Optional[str] = Field(description="模型描述")
    version: str = Field(description="版本号")
    category: Optional[str] = Field(description="模型分类")
    tags: Optional[List[str]] = Field(description="标签列表")
    fields_config: List[FieldConfig] = Field(description="字段配置列表")
    usage_count: int = Field(description="使用次数")
    status: str = Field(description="状态")


class DataModelPreviewRequest(BaseModel):
    """数据预览请求Schema"""
    model_id: int = Field(..., description="模型ID")
    count: int = Field(default=5, ge=1, le=20, description="预览数据条数")
    model_config = ConfigDict(protected_namespaces=())
    

class DataModelPreviewResponse(BaseModel):
    
    """数据预览响应Schema"""
    model_id: int = Field(description="模型ID")
    model_name: str = Field(description="模型名称")
    preview_data: List[Dict[str, Any]] = Field(description="预览数据")
    field_count: int = Field(description="字段数量")
    record_count: int = Field(description="记录数量")


    model_config = ConfigDict(protected_namespaces=())
