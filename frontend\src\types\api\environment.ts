/**
 * 环境管理相关类型定义
 * 统一管理环境模块的所有类型
 */

// ==================== 基础类型 ====================

/** 环境类型枚举 */
export type EnvironmentType = 
  | 'database' 
  | 'redis' 
  | 'ssh' 
  | 'k8s' 
  | 'api' 
  | 'kafka'
  | 'mongodb'
  | 'elasticsearch'
  | 'rabbitmq'
  | 'minio'
  | 'opensearch'

/** 环境状态枚举 */
export type EnvironmentStatus = 'connected' | 'failed' | 'unknown'

/** 环境基础信息 */
export interface EnvironmentBase {
  /** 环境名称 */
  name: string
  /** 环境类型 */
  type: EnvironmentType
  /** 环境描述 */
  description?: string
  /** 主机地址 */
  host?: string
  /** 端口号 */
  port?: number
  /** 环境配置 */
  config?: Record<string, any>
  /** 标签（逗号分隔） */
  tags?: string
}

// ==================== 请求类型 ====================

/** 创建环境请求 */
export interface EnvironmentCreateRequest extends EnvironmentBase {
  // 创建时的特定字段可以在这里添加
}

/** 更新环境请求 */
export interface EnvironmentUpdateRequest extends Partial<EnvironmentBase> {
  // 更新时所有字段都是可选的
}

/** 环境列表查询参数 */
export interface EnvironmentListParams {
  /** 当前页码 */
  current?: number
  /** 每页条数 */
  size?: number
  /** 搜索关键词 */
  keyword?: string
  /** 环境类型过滤 */
  type?: EnvironmentType
  /** 状态过滤 */
  status?: EnvironmentStatus
  /** 标签过滤 */
  tags?: string
}

/** 连接测试请求 */
export interface ConnectionTestRequest {
  /** 环境类型 */
  type: EnvironmentType
  /** 连接配置 */
  config: Record<string, any>
  /** 超时时间（秒） */
  timeout?: number
}

// ==================== 响应类型 ====================

/** 环境详情响应 */
export interface EnvironmentResponse extends EnvironmentBase {
  /** 环境ID */
  id: number
  /** 连接状态 */
  status: EnvironmentStatus
  /** 最后测试时间 */
  last_test_time?: string
  /** 创建时间 */
  created_at: string
  /** 更新时间 */
  updated_at: string
  /** 创建者 */
  created_by?: string
  /** 更新者 */
  updated_by?: string
  /** 标签列表 */
  tag_list: string[]
}

/** 环境列表项（用于表格显示） */
export interface EnvironmentListItem extends EnvironmentResponse {
  /** 是否正在测试连接 */
  testing?: boolean
}

/** 环境列表响应 - 与后端EnvironmentPageResponse匹配 */
export interface EnvironmentListData {
  /** 当前页环境列表 */
  items: EnvironmentResponse[]
  /** 总条数 */
  total: number
  /** 当前页码 */
  page: number
  /** 每页条数 */
  size: number
  /** 总页数 */
  pages: number
}

/** 环境列表响应（兼容旧格式） */
export interface EnvironmentListResponse {
  /** 环境列表 */
  records: EnvironmentResponse[]
  /** 总数 */
  total: number
  /** 当前页 */
  current: number
  /** 每页条数 */
  size: number
}

/** 连接测试响应 */
export interface ConnectionTestResponse {
  /** 是否成功 */
  success: boolean
  /** 响应消息 */
  message: string
  /** 耗时（秒） */
  duration: number
  /** 详细信息 */
  details?: Record<string, any>
  /** 环境ID（用于批量测试时标识） */
  env_id?: number
}

/** 支持的环境类型信息 */
export interface SupportedEnvironmentType {
  /** 类型标识 */
  type: EnvironmentType
  /** 显示名称 */
  displayName: string
  /** 描述 */
  description: string
  /** 默认端口 */
  defaultPort?: number
  /** 必需字段 */
  requiredFields: string[]
  /** 可选字段 */
  optionalFields: string[]
  /** 图标 */
  icon?: string
  /** 分类 */
  category: string
}

// ==================== 表单类型 ====================

/** 环境表单数据 */
export interface EnvironmentFormData extends EnvironmentBase {
  /** 环境ID（编辑时） */
  id?: number
}

/** 环境表单验证规则 */
export interface EnvironmentFormRules {
  name: Array<{ required: boolean; message: string; trigger: string }>
  envType: Array<{ required: boolean; message: string; trigger: string }>
  host: Array<{ required: boolean; message: string; trigger: string }>
  port: Array<{ type: string; min: number; max: number; message: string; trigger: string }>
}

// ==================== 配置类型 ====================

/** 环境状态配置 */
export interface EnvironmentStatusConfig {
  type: 'success' | 'danger' | 'info' | 'warning'
  text: string
  color?: string
}

/** 环境类型配置 */
export interface EnvironmentTypeConfig {
  type: 'primary' | 'success' | 'info' | 'warning' | 'danger'
  text: string
  icon?: string
}

// ==================== 工具类型 ====================

/** 环境操作类型 */
export type EnvironmentAction = 'create' | 'edit' | 'delete' | 'test' | 'view'

/** 环境批量操作参数 */
export interface EnvironmentBatchParams {
  /** 环境ID列表 */
  ids: number[]
  /** 操作类型 */
  action: 'delete' | 'test' | 'enable' | 'disable'
}

/** 环境导出参数 */
export interface EnvironmentExportParams {
  /** 导出格式 */
  format: 'json' | 'csv' | 'excel'
  /** 是否包含配置信息 */
  includeConfig?: boolean
  /** 过滤条件 */
  filters?: EnvironmentListParams
}

// ==================== 统计类型 ====================

/** 环境类型统计 */
export interface EnvironmentTypeStats {
  /** 环境类型 */
  type: string
  /** 总数 */
  total: number
  /** 已连接数 */
  connected: number
  /** 失败数 */
  failed: number
}

/** 环境状态统计 */
export interface EnvironmentStatusStats {
  /** 状态 */
  status: string
  /** 数量 */
  count: number
}

/** 环境统计响应 */
export interface EnvironmentStats {
  /** 类型统计 */
  type_stats: Record<string, number>
  /** 状态统计 */
  status_stats: Record<string, number>
  /** 最近环境 */
  recent_environments: EnvironmentResponse[]
}

// ==================== 类型守卫 ====================

/** 检查是否为有效的环境类型 */
export function isValidEnvironmentType(type: string): type is EnvironmentType {
  const validTypes: EnvironmentType[] = [
    'database', 'redis', 'ssh', 'k8s', 'api', 'kafka',
    'mongodb', 'elasticsearch', 'rabbitmq', 'minio', 'opensearch'
  ]
  return validTypes.includes(type as EnvironmentType)
}

/** 检查是否为有效的环境状态 */
export function isValidEnvironmentStatus(status: string): status is EnvironmentStatus {
  const validStatuses: EnvironmentStatus[] = ['connected', 'failed', 'unknown']
  return validStatuses.includes(status as EnvironmentStatus)
}

// ==================== 默认值 ====================

/** 默认环境表单数据 */
export const defaultEnvironmentFormData: EnvironmentFormData = {
  name: '',
  type: 'database',
  description: '',
  host: '',
  port: undefined,
  config: {},
  tags: ''
}

/** 默认查询参数 */
export const defaultEnvironmentListParams: EnvironmentListParams = {
  current: 1,
  size: 20,
  keyword: '',
  type: undefined,
  status: undefined,
  tags: ''
}

// ==================== 常量配置 ====================

/** 环境类型配置 */
export const ENVIRONMENT_TYPE_CONFIG = {
  database: { type: 'primary' as const, text: '数据库', icon: 'database' },
  redis: { type: 'danger' as const, text: 'Redis', icon: 'redis' },
  ssh: { type: 'warning' as const, text: 'SSH', icon: 'ssh' },
  k8s: { type: 'success' as const, text: 'Kubernetes', icon: 'k8s' },
  api: { type: 'info' as const, text: 'API', icon: 'api' },
  kafka: { type: 'primary' as const, text: 'Kafka', icon: 'kafka' },
  mongodb: { type: 'success' as const, text: 'MongoDB', icon: 'mongodb' },
  elasticsearch: { type: 'warning' as const, text: 'Elasticsearch', icon: 'elasticsearch' },
  rabbitmq: { type: 'danger' as const, text: 'RabbitMQ', icon: 'rabbitmq' },
  minio: { type: 'info' as const, text: 'MinIO', icon: 'minio' },
  opensearch: { type: 'primary' as const, text: 'OpenSearch', icon: 'opensearch' }
} as const

/** 环境状态配置 */
export const ENVIRONMENT_STATUS_CONFIG = {
  connected: { type: 'success' as const, text: '已连接' },
  failed: { type: 'danger' as const, text: '连接失败' },
  unknown: { type: 'info' as const, text: '未知' }
} as const
