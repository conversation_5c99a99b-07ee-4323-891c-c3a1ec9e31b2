"""
认证相关 Pydantic 模式
"""
from pydantic import BaseModel, Field


class LoginRequest(BaseModel):
    """
    登录请求模式
    """
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class LoginResponse(BaseModel):
    """
    登录响应模式
    """
    token: str = Field(..., description="访问令牌")
    refreshToken: str = Field(..., description="刷新令牌")


class TokenResponse(BaseModel):
    """
    Token响应模式
    """
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")


class TokenData(BaseModel):
    """
    Token 数据模式
    """
    username: str = Field(..., description="用户名")
    user_id: int = Field(..., description="用户ID")
    is_superuser: bool = Field(default=False, description="是否超级用户")
    roles: list = Field(default=[], description="角色列表")


class RefreshTokenRequest(BaseModel):
    """
    刷新令牌请求模式
    """
    refresh_token: str = Field(..., description="刷新令牌")


class RegisterRequest(BaseModel):
    """
    注册请求模式
    """
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    password: str = Field(..., min_length=6, max_length=50, description="密码")
    email: str = Field(..., description="邮箱")
    nickname: str = Field(default="", description="昵称") 