"""
数据生成任务管理API
提供数据生成任务的管理接口
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, Query, Path, HTTPException
from fastapi.responses import FileResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_user, get_db
from app.core.responses import response_builder
from app.schemas.base import PaginationResponse
from app.schemas.common import SearchParams
from app.schemas.data_factory.generation_task import (
    GenerationTaskCreate,
    GenerationTaskUpdate,
    GenerationTaskResponse,
    TaskStatistics
)
from app.services.data_factory.generation import DataGenerationService
from app.services.data_factory.export import DataExportService
from app.models.user.user import User

router = APIRouter()


@router.get("/", response_model=PaginationResponse[GenerationTaskResponse], summary="获取任务列表")
async def get_generation_tasks(
    current: int = Query(1, ge=1, description="当前页码"),
    size: int = Query(20, ge=1, le=100, description="每页条数"),
    keyword: str = Query(None, description="搜索关键词"),
    status: str = Query(None, description="任务状态筛选"),
    model_id: Optional[str] = Query(None, description="模型ID筛选"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取数据生成任务列表，支持分页和筛选"""
    service = DataGenerationService(db)

    # 处理可选的整数参数
    parsed_model_id = None
    if model_id and model_id.strip():
        try:
            parsed_model_id = int(model_id)
        except ValueError:
            parsed_model_id = None

    # 构建搜索参数
    search_params = SearchParams(
        current=current,
        size=size,
        keyword=keyword
    )

    # 使用BaseService的标准分页方法
    result = await service.list_with_pagination(search_params)

    return response_builder.paginated(
        result.records, result.total, result.current, result.size
    )


@router.post("/", response_model=GenerationTaskResponse, status_code=201, summary="创建生成任务")
async def create_generation_task(
    task_data: GenerationTaskCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建新的数据生成任务"""
    service = DataGenerationService(db)
    task = await service.create_generation_task(task_data, str(current_user.id))
    return response_builder.created(task)


@router.get("/{task_id}", response_model=GenerationTaskResponse, summary="获取任务详情")
async def get_generation_task(
    task_id: int = Path(..., description="任务ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指定任务的详细信息"""
    service = DataGenerationService(db)
    task = await service.get_task_with_model(task_id)
    return response_builder.success(task)


@router.put("/{task_id}", response_model=GenerationTaskResponse, summary="更新任务")
async def update_generation_task(
    task_data: GenerationTaskUpdate,
    task_id: int = Path(..., description="任务ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新指定的生成任务"""
    service = DataGenerationService(db)
    task = await service.update(task_id, task_data, str(current_user.id))
    return response_builder.success(task)


@router.delete("/{task_id}", status_code=204, summary="删除任务")
async def delete_generation_task(
    task_id: int = Path(..., description="任务ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除指定的生成任务"""
    service = DataGenerationService(db)
    await service.delete(task_id)
    # HTTP 204 No Content - 无返回语句


@router.post("/{task_id}/cancel", response_model=GenerationTaskResponse, summary="取消任务")
async def cancel_generation_task(
    task_id: int = Path(..., description="任务ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """取消正在执行的任务"""
    service = DataGenerationService(db)
    task = await service.cancel_task(task_id)
    return response_builder.success(task)


@router.post("/{task_id}/retry", response_model=GenerationTaskResponse, summary="重试任务")
async def retry_generation_task(
    task_id: int = Path(..., description="任务ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """重试失败的任务"""
    service = DataGenerationService(db)
    task = await service.retry_task(task_id)
    return response_builder.success(task)


@router.get("/{task_id}/download", summary="下载任务结果")
async def download_task_result(
    task_id: int = Path(..., description="任务ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """下载任务生成的结果文件"""
    export_service = DataExportService(db)
    file_info = await export_service.download_task_result(task_id)
    
    return FileResponse(
        path=file_info['file_path'],
        filename=file_info['file_name'],
        media_type='application/octet-stream'
    )


@router.get("/statistics/overview", response_model=TaskStatistics, summary="获取任务统计")
async def get_task_statistics(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务统计信息"""
    service = DataGenerationService(db)
    statistics = await service.get_task_statistics()
    return response_builder.success(statistics)


@router.get("/running/list", response_model=List[GenerationTaskResponse], summary="获取运行中任务")
async def get_running_tasks(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取当前正在运行的任务列表"""
    service = DataGenerationService(db)
    tasks = await service.get_running_tasks()
    return response_builder.success(tasks)


@router.get("/export/formats", response_model=List[Dict[str, Any]], summary="获取支持的导出格式")
async def get_export_formats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取支持的导出格式列表"""
    export_service = DataExportService(db)
    formats = await export_service.get_supported_formats()
    return response_builder.success(formats)
