"""
样本数据解析引擎
用于解析JSON样本数据，自动推断字段类型和生成器
"""
import json
import re
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from app.utils.logger import setup_logger

logger = setup_logger()


class SampleDataParser:
    """样本数据解析器"""
    
    def __init__(self):
        self.field_analysis = {}
    
    def parse_json_samples(self, json_data: Union[str, Dict, List]) -> Dict[str, Any]:
        """
        解析JSON样本数据
        
        Args:
            json_data: JSON字符串、字典或列表
            
        Returns:
            解析结果，包含字段信息和推荐配置
        """
        try:
            # 如果是字符串，先解析为Python对象
            if isinstance(json_data, str):
                data = json.loads(json_data)
            else:
                data = json_data
            
            # 确保数据是列表格式
            if isinstance(data, dict):
                samples = [data]
            elif isinstance(data, list):
                samples = data
            else:
                raise ValueError("不支持的数据格式")
            
            if not samples:
                raise ValueError("样本数据为空")
            
            # 分析字段
            fields_info = self._analyze_fields(samples)
            
            # 生成推荐配置
            recommended_config = self._generate_recommended_config(fields_info)
            
            return {
                'success': True,
                'sample_count': len(samples),
                'fields': fields_info,
                'recommended_config': recommended_config,
                'raw_samples': samples[:5]  # 只返回前5个样本
            }
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return {
                'success': False,
                'error': f'JSON格式错误: {str(e)}',
                'error_type': 'json_parse_error'
            }
        except Exception as e:
            logger.error(f"样本数据解析失败: {str(e)}")
            return {
                'success': False,
                'error': f'解析失败: {str(e)}',
                'error_type': 'parse_error'
            }
    
    def _analyze_fields(self, samples: List[Dict]) -> Dict[str, Dict]:
        """分析字段信息"""
        field_stats = {}

        # 收集所有字段的统计信息（包括扁平化字段）
        for sample in samples:
            # 扁平化当前样本
            flattened = self._flatten_dict(sample)

            for field_name, value in flattened.items():
                if field_name not in field_stats:
                    field_stats[field_name] = {
                        'name': field_name,
                        'values': [],
                        'types': set(),
                        'null_count': 0,
                        'total_count': 0
                    }

                stats = field_stats[field_name]
                stats['total_count'] += 1

                if value is None:
                    stats['null_count'] += 1
                else:
                    # 记录值和类型
                    if len(stats['values']) < 10:  # 最多记录10个样本值
                        stats['values'].append(value)

                    # 推断数据类型
                    inferred_type = self._infer_data_type(value)
                    stats['types'].add(inferred_type)
        
        # 生成字段信息
        fields_info = {}
        for field_name, stats in field_stats.items():
            fields_info[field_name] = {
                'name': field_name,
                'inferred_type': self._determine_primary_type(stats['types']),
                'sample_values': stats['values'],
                'null_rate': stats['null_count'] / stats['total_count'],
                'value_count': len(set(str(v) for v in stats['values'])),  # 唯一值数量
                'recommended_generator': self._recommend_generator(field_name, stats['values']),
                'is_required': stats['null_count'] == 0  # 如果没有null值，则认为是必填
            }
        
        return fields_info

    def _flatten_dict(self, data: Dict, parent_key: str = '', sep: str = '.', max_depth: int = 3, current_depth: int = 0) -> Dict:
        """
        扁平化字典，将嵌套结构转换为点分隔的键值对

        Args:
            data: 要扁平化的字典
            parent_key: 父级键名
            sep: 分隔符
            max_depth: 最大深度
            current_depth: 当前深度

        Returns:
            扁平化后的字典
        """
        items = []

        if current_depth >= max_depth:
            # 达到最大深度，直接返回当前值
            return {parent_key: data} if parent_key else {'root': data}

        for key, value in data.items():
            new_key = f"{parent_key}{sep}{key}" if parent_key else key

            if isinstance(value, dict) and value:  # 非空字典
                # 递归扁平化嵌套字典
                items.extend(self._flatten_dict(
                    value, new_key, sep, max_depth, current_depth + 1
                ).items())
            elif isinstance(value, list) and value:  # 非空数组
                # 处理数组：提取第一个元素作为模板
                first_item = value[0]
                if isinstance(first_item, dict):
                    # 如果数组元素是字典，扁平化第一个元素
                    array_key = f"{new_key}[0]"
                    items.extend(self._flatten_dict(
                        first_item, array_key, sep, max_depth, current_depth + 1
                    ).items())
                else:
                    # 如果数组元素是基本类型，直接使用
                    items.append((new_key, first_item))
            else:
                # 基本类型或空值，直接添加
                items.append((new_key, value))

        return dict(items)
    
    def _infer_data_type(self, value: Any) -> str:
        """推断数据类型"""
        if value is None:
            return 'null'
        
        # Python类型映射
        if isinstance(value, bool):
            return 'boolean'
        elif isinstance(value, int):
            return 'integer'
        elif isinstance(value, float):
            return 'decimal'
        elif isinstance(value, str):
            # 进一步分析字符串类型
            return self._analyze_string_type(value)
        elif isinstance(value, (list, tuple)):
            return 'array'
        elif isinstance(value, dict):
            return 'object'
        else:
            return 'string'
    
    def _analyze_string_type(self, value: str) -> str:
        """分析字符串的具体类型"""
        # 日期时间检测
        if self._is_datetime(value):
            return 'datetime'
        elif self._is_date(value):
            return 'date'
        elif self._is_time(value):
            return 'time'
        
        # 其他特殊格式检测
        if self._is_email(value):
            return 'email'
        elif self._is_phone(value):
            return 'phone'
        elif self._is_url(value):
            return 'url'
        elif self._is_uuid(value):
            return 'uuid'
        
        return 'string'
    
    def _is_datetime(self, value: str) -> bool:
        """检测是否为日期时间格式"""
        datetime_patterns = [
            r'^\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}',  # ISO格式
            r'^\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}',     # 常见格式
            r'^\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2}',     # 美式格式
        ]
        return any(re.match(pattern, value) for pattern in datetime_patterns)
    
    def _is_date(self, value: str) -> bool:
        """检测是否为日期格式"""
        date_patterns = [
            r'^\d{4}-\d{2}-\d{2}$',  # YYYY-MM-DD
            r'^\d{4}/\d{2}/\d{2}$',  # YYYY/MM/DD
            r'^\d{2}/\d{2}/\d{4}$',  # MM/DD/YYYY
            r'^\d{2}-\d{2}-\d{4}$',  # MM-DD-YYYY
        ]
        return any(re.match(pattern, value) for pattern in date_patterns)
    
    def _is_time(self, value: str) -> bool:
        """检测是否为时间格式"""
        time_patterns = [
            r'^\d{2}:\d{2}:\d{2}$',     # HH:MM:SS
            r'^\d{2}:\d{2}$',           # HH:MM
            r'^\d{1,2}:\d{2}:\d{2}$',   # H:MM:SS
        ]
        return any(re.match(pattern, value) for pattern in time_patterns)
    
    def _is_email(self, value: str) -> bool:
        """检测是否为邮箱格式"""
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(email_pattern, value) is not None
    
    def _is_phone(self, value: str) -> bool:
        """检测是否为手机号格式"""
        phone_patterns = [
            r'^1[3-9]\d{9}$',           # 中国手机号
            r'^\+\d{1,3}\d{10,11}$',    # 国际格式
            r'^\d{3}-\d{3}-\d{4}$',     # 美式格式
            r'^\(\d{3}\)\s?\d{3}-\d{4}$' # 美式括号格式
        ]
        return any(re.match(pattern, value) for pattern in phone_patterns)
    
    def _is_url(self, value: str) -> bool:
        """检测是否为URL格式"""
        url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        return re.match(url_pattern, value) is not None
    
    def _is_uuid(self, value: str) -> bool:
        """检测是否为UUID格式"""
        uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
        return re.match(uuid_pattern, value.lower()) is not None
    
    def _determine_primary_type(self, types: set) -> str:
        """确定主要数据类型"""
        if not types:
            return 'string'
        
        # 移除null类型
        non_null_types = types - {'null'}
        if not non_null_types:
            return 'string'
        
        # 如果只有一种类型，直接返回
        if len(non_null_types) == 1:
            return list(non_null_types)[0]
        
        # 类型优先级（从高到低）
        type_priority = [
            'datetime', 'date', 'time',
            'email', 'phone', 'url', 'uuid',
            'boolean', 'integer', 'decimal',
            'string'
        ]
        
        for type_name in type_priority:
            if type_name in non_null_types:
                return type_name
        
        return 'string'
    
    def _recommend_generator(self, field_name: str, sample_values: List[Any]) -> str:
        """推荐生成器"""
        if not sample_values:
            return 'sample'
        
        field_lower = field_name.lower()
        first_value = sample_values[0]
        
        # 基于字段名推荐
        if 'id' in field_lower and field_lower != 'id':
            return 'uuid'
        elif any(keyword in field_lower for keyword in ['name', '姓名', '用户名', 'username']):
            return 'name'
        elif any(keyword in field_lower for keyword in ['phone', 'mobile', '手机', '电话']):
            return 'phone'
        elif any(keyword in field_lower for keyword in ['email', 'mail', '邮箱']):
            return 'email'
        elif any(keyword in field_lower for keyword in ['age', '年龄', 'count', 'num']):
            return 'range'
        elif any(keyword in field_lower for keyword in ['date', 'time', '日期', '时间']):
            return 'date'
        
        # 基于值的格式推荐
        if isinstance(first_value, str):
            if self._is_email(first_value):
                return 'email'
            elif self._is_phone(first_value):
                return 'phone'
            elif self._is_uuid(first_value):
                return 'uuid'
            elif self._is_datetime(first_value) or self._is_date(first_value):
                return 'date'
        elif isinstance(first_value, (int, float)):
            return 'range'
        
        # 默认使用样本生成器
        return 'sample'
    
    def _generate_recommended_config(self, fields_info: Dict[str, Dict]) -> List[Dict]:
        """生成推荐的字段配置"""
        config = []
        
        for field_name, info in fields_info.items():
            field_config = {
                'name': field_name,
                'type': info['inferred_type'],
                'required': info['is_required'],
                'generator': info['recommended_generator'],
                'options': {}
            }
            
            # 根据生成器类型设置选项
            if info['recommended_generator'] == 'sample':
                field_config['options'] = {
                    'samples': info['sample_values']
                }
            elif info['recommended_generator'] == 'range' and info['sample_values']:
                # 为数值范围生成器设置范围
                numeric_values = [v for v in info['sample_values'] if isinstance(v, (int, float))]
                if numeric_values:
                    field_config['options'] = {
                        'min': int(min(numeric_values)),
                        'max': int(max(numeric_values))
                    }
            
            config.append(field_config)
        
        return config


def test_custom_generator_code(code: str, samples: List[Any], test_count: int = 5) -> Dict[str, Any]:
    """
    测试自定义生成器代码
    
    Args:
        code: 自定义生成器代码
        samples: 样本数据
        test_count: 测试生成数量
        
    Returns:
        测试结果
    """
    try:
        # 简化版本：直接使用exec（在生产环境中应该使用RestrictedPython）
        import random
        import datetime
        import math

        # 创建安全的执行环境
        exec_globals = {
            '__builtins__': {
                'len': len, 'str': str, 'int': int, 'float': float,
                'bool': bool, 'list': list, 'dict': dict, 'range': range,
                'enumerate': enumerate, 'zip': zip, 'min': min, 'max': max,
                'sum': sum, 'abs': abs, 'round': round,
            },
            'random': random,
            'datetime': datetime,
            'math': math,
        }

        # 执行代码
        exec(code, exec_globals)
        
        # 检查generate函数
        if 'generate' not in exec_globals:
            return {
                'success': False,
                'error': '未找到generate函数，请确保定义了generate(samples, index, context)函数'
            }
        
        generate_func = exec_globals['generate']
        
        # 测试生成
        results = []
        for i in range(test_count):
            try:
                result = generate_func(samples, i, {})
                results.append(str(result))
            except Exception as e:
                return {
                    'success': False,
                    'error': f'执行generate函数时出错: {str(e)}'
                }
        
        return {
            'success': True,
            'results': results
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': f'测试失败: {str(e)}'
        }
