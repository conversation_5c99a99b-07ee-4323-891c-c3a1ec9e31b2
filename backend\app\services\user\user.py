"""
用户服务层
参考示例代码的简洁设计，处理用户相关的业务逻辑
"""
import time
from typing import Optional, Dict, Any, Type
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService
from app.repositories.user.user import UserRepository
from app.schemas.user.user import UserCreate, UserUpdate, UserResponse, UserQuery, UserPageResponse
from app.models.user.user import User
from app.utils.security import get_password_hash
from app.core.exceptions import raise_user_already_exists, raise_user_not_found, raise_business_error
from app.core.error_codes import ErrorCode
from app.utils.logger import operation_logger


class UserService(BaseService[User, UserCreate, UserUpdate, UserResponse]):
    """用户业务服务"""

    def __init__(self, db: AsyncSession):
        repository = UserRepository(db)
        super().__init__(db, repository)

    @property
    def model_class(self) -> Type[User]:
        """返回用户模型类"""
        return User

    @property
    def response_schema_class(self) -> Type[UserResponse]:
        """返回用户响应Schema类"""
        return UserResponse


    async def _validate_before_create(self, create_data: UserCreate, **_kwargs) -> None:
        """创建用户前的验证"""
        # 检查用户名是否重复
        existing_user = await self.repository.get_by_username(create_data.username)
        if existing_user:
            raise_user_already_exists(create_data.username)

        # 检查邮箱是否重复
        if create_data.email:
            existing_email = await self.repository.get_by_email(create_data.email)
            if existing_email:
                raise_business_error(ErrorCode.USER_EMAIL_ALREADY_EXISTS, f"邮箱 '{create_data.email}' 已被使用")

    async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
        """创建用户前的处理"""
        # 加密密码
        if 'password' in create_dict:
            password = create_dict.pop('password')
            create_dict['hashed_password'] = get_password_hash(password)

        return create_dict

    async def _validate_before_update(self, user: User, update_data: UserUpdate, **_kwargs) -> None:
        """更新用户前的验证"""
        # 如果要更新用户名，检查是否重复
        if update_data.username and update_data.username != user.username:
            existing = await self.repository.get_by_username(update_data.username)
            if existing:
                raise_user_already_exists(update_data.username)

        # 如果要更新邮箱，检查是否重复
        if update_data.email and update_data.email != user.email:
            existing = await self.repository.get_by_email(update_data.email)
            if existing:
                raise_business_error(ErrorCode.USER_EMAIL_ALREADY_EXISTS, f"邮箱 '{update_data.email}' 已被使用")

    async def _process_before_update(self, _user: User, update_dict: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户前的处理"""
        # 如果更新密码，进行加密并记录密码修改日志
        if 'password' in update_dict:
            password = update_dict.pop('password')
            update_dict['hashed_password'] = get_password_hash(password)

            # 记录密码修改审计日志
            await operation_logger.log_audit_operation(
                operation="password_change",
                user_id=_user.id,
                username=_user.username,
                resource_type="user",
                resource_id=_user.id,
                old_values={"password": "***"},
                new_values={"password": "***"}
            )

        return update_dict

    def _convert_to_response(self, user: User) -> UserResponse:
        """将User对象转换为UserResponse"""
        # 安全获取角色信息，避免延迟加载问题
        try:
            roles = [role.code for role in user.roles] if hasattr(user, 'roles') and user.roles else []
        except Exception:
            roles = []

        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            nickname=user.nickname,
            avatar=user.avatar,
            description=user.description,
            is_active=user.is_active if user.is_active is not None else True,
            is_superuser=user.is_superuser if user.is_superuser is not None else False,
            status=user.status if user.status is not None else "1",
            last_login_at=user.last_login_at,
            roles=roles,
            created_at=user.created_at.strftime("%Y-%m-%d %H:%M:%S") if user.created_at else None,
            updated_at=user.updated_at.strftime("%Y-%m-%d %H:%M:%S") if user.updated_at else None,
            created_by=user.created_by,
            updated_by=user.updated_by
        )

    # ==================== 业务方法使用基类通用方法 ====================

    async def get_current_user_info(self, current_user: User) -> UserResponse:
        """获取当前用户信息"""
        return self._convert_to_response(current_user)

    async def list_users(self, query: UserQuery, current_user_id: Optional[int] = None) -> UserPageResponse:
        """查询用户列表，支持关键词搜索、状态筛选和分页"""
        start_time = time.time()

        try:
            users, total = await self.repository.list(
                keyword=query.keyword,
                status=query.status,
                is_active=query.is_active,
                offset=query.offset,
                limit=query.size
            )

            # 转换为响应格式
            user_responses = [self._convert_to_response(user) for user in users]

            # 计算总页数
            pages = (total + query.size - 1) // query.size

            # 记录查询日志
            duration_ms = (time.time() - start_time) * 1000
            await operation_logger.log_operation(
                operation="user_list_query",
                user_id=current_user_id,
                username=f"user_{current_user_id}" if current_user_id else "anonymous",
                request_data={
                    "keyword": query.keyword,
                    "status": query.status,
                    "page": query.page,
                    "size": query.size,
                    "total_found": total
                },
                result="success",
                duration_ms=duration_ms
            )

            return UserPageResponse(
                items=user_responses,
                total=total,
                page=query.page,
                size=query.size,
                pages=pages
            )

        except Exception as e:
            # 记录查询失败日志
            duration_ms = (time.time() - start_time) * 1000
            await operation_logger.log_operation(
                operation="user_list_query",
                user_id=current_user_id,
                username=f"user_{current_user_id}" if current_user_id else "anonymous",
                request_data=query.model_dump(),
                result="failed",
                error_msg=str(e),
                duration_ms=duration_ms
            )
            raise

    async def get_by_id(self, user_id: int) -> UserResponse:
        """根据ID获取用户信息 - 重写基类方法以预加载角色关系"""
        user = await self.repository.get_with_roles(user_id)
        if not user:
            raise_user_not_found(user_id)
        return self._convert_to_response(user)

    async def _validate_before_delete(self, obj_id: int, current_user_id: str, **kwargs) -> None:
        """删除前验证 - 防止删除自己和超级管理员"""
        # 防止用户删除自己
        if obj_id == int(current_user_id):
            raise_business_error(ErrorCode.USER_CANNOT_DELETE_SELF, "不能删除自己")

        # 防止删除超级管理员
        user = await self.repository.get(obj_id)
        if user and user.is_superuser:
            raise_business_error(ErrorCode.USER_CANNOT_DELETE_SUPERUSER, "不能删除超级管理员")

