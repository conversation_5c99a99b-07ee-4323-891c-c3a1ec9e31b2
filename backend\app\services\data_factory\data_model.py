"""
数据模型服务
提供数据模型的业务逻辑处理
"""
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService
from app.repositories.data_factory.data_model import DataModelRepository
from app.models.data_factory.data_model import DataModel
from app.schemas.data_factory.data_model import (
    DataModelCreate, 
    DataModelUpdate, 
    DataModelResponse,
    DataModelPreviewResponse
)
from app.core.exceptions import raise_validation_error, raise_not_found
from app.utils.data_factory.generators import DataGeneratorEngine


class DataModelService(BaseService[DataModel, DataModelCreate, DataModelUpdate, DataModelResponse]):
    """
    数据模型服务类
    继承BaseService，提供数据模型的业务逻辑处理
    """

    def __init__(self, db: AsyncSession):
        repository = DataModelRepository(db)
        super().__init__(db, repository)
        self.generator_engine = DataGeneratorEngine()

    @property
    def model_class(self):
        """返回模型类"""
        return DataModel

    @property
    def response_schema_class(self):
        """返回响应Schema类"""
        return DataModelResponse

    async def _validate_before_create(self, create_data: DataModelCreate, **kwargs) -> None:
        """
        创建前验证
        检查模型名称唯一性和字段配置有效性
        """
        # 检查模型名称唯一性
        existing_model = await self.repository.get_by_name(create_data.name)
        if existing_model:
            raise_validation_error(f"模型名称 '{create_data.name}' 已存在")
        
        # 验证字段配置
        await self._validate_fields_config(create_data.fields_config)

    async def _validate_before_update(self, obj: DataModel, update_data: DataModelUpdate, **kwargs) -> None:
        """
        更新前验证
        """
        # 如果更新名称，检查唯一性
        if update_data.name and update_data.name != obj.name:
            existing_model = await self.repository.get_by_name(update_data.name)
            if existing_model:
                raise_validation_error(f"模型名称 '{update_data.name}' 已存在")
        
        # 如果更新字段配置，验证有效性
        if update_data.fields_config:
            await self._validate_fields_config(update_data.fields_config)

    async def _validate_fields_config(self, fields_config: List[Dict[str, Any]]) -> None:
        """
        验证字段配置的有效性
        
        Args:
            fields_config: 字段配置列表
        """
        if not fields_config:
            raise_validation_error("字段配置不能为空")
        
        # 验证每个字段的生成器是否支持
        for field in fields_config:
            generator_type = field.get('generator')
            if not self.generator_engine.is_generator_supported(generator_type):
                raise_validation_error(f"不支持的生成器类型: {generator_type}")
            
            # 验证生成器选项
            options = field.get('options', {})
            if not self.generator_engine.validate_generator_options(generator_type, options):
                raise_validation_error(f"生成器 '{generator_type}' 的选项配置无效")

    async def get_by_category(self, category: str, skip: int = 0, limit: int = 100) -> List[DataModelResponse]:
        """
        根据分类获取数据模型列表
        
        Args:
            category: 模型分类
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            数据模型响应列表
        """
        models = await self.repository.get_by_category(category, skip, limit)
        return [self._convert_to_response(model) for model in models]

    async def search_models(self, keyword: str, skip: int = 0, limit: int = 100) -> List[DataModelResponse]:
        """
        搜索数据模型
        
        Args:
            keyword: 搜索关键词
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            数据模型响应列表
        """
        models = await self.repository.search_by_keyword(keyword, skip, limit)
        return [self._convert_to_response(model) for model in models]

    async def get_active_models(self, skip: int = 0, limit: int = 100) -> List[DataModelResponse]:
        """
        获取启用状态的数据模型列表
        
        Args:
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            启用的数据模型响应列表
        """
        models = await self.repository.get_active_models(skip, limit)
        return [self._convert_to_response(model) for model in models]

    async def preview_data(self, model_id: int, count: int = 5) -> DataModelPreviewResponse:
        """
        预览数据模型生成的数据
        
        Args:
            model_id: 模型ID
            count: 预览数据条数
            
        Returns:
            数据预览响应
        """
        # 获取数据模型
        model = await self.get_by_id(model_id)
        
        # 生成预览数据
        preview_data = await self.generator_engine.generate_data(
            model.fields_config, count
        )
        
        # 增加使用次数
        await self.repository.increment_usage_count(model_id)
        
        return DataModelPreviewResponse(
            model_id=model_id,
            model_name=model.name,
            preview_data=preview_data,
            field_count=len(model.fields_config),
            record_count=len(preview_data)
        )

    async def get_statistics(self) -> Dict[str, Any]:
        """
        获取数据模型统计信息
        
        Returns:
            统计信息字典
        """
        return await self.repository.get_statistics()

    async def duplicate_model(self, model_id: int, new_name: str, current_user: str) -> DataModelResponse:
        """
        复制数据模型
        
        Args:
            model_id: 源模型ID
            new_name: 新模型名称
            current_user: 当前用户
            
        Returns:
            新创建的数据模型响应
        """
        # 获取源模型
        source_model = await self.get_by_id(model_id)
        
        # 检查新名称是否已存在
        existing_model = await self.repository.get_by_name(new_name)
        if existing_model:
            raise_validation_error(f"模型名称 '{new_name}' 已存在")
        
        # 创建新模型数据
        new_model_data = DataModelCreate(
            name=new_name,
            description=f"复制自 {source_model.name}",
            version="1.0.0",
            category=source_model.category,
            tags=source_model.tags,
            fields_config=source_model.fields_config,
            status=source_model.status
        )
        
        # 创建新模型
        return await self.create(new_model_data, current_user)
