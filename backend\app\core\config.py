"""
核心配置管理模块
使用 Pydantic Settings 管理应用配置
"""
from typing import List
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""

    # 应用基础配置
    app_name: str = Field(default="DpTestPlatform API", description="应用名称")
    app_version: str = Field(default="1.0.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    host: str = Field(default="0.0.0.0", description="服务器地址")
    port: int = Field(default=8000, description="服务器端口")

    # 数据库配置 - 必须通过环境变量设置
    DATABASE_URL: str = Field(..., env="DATABASE_URL", description="数据库连接URL")

    # 数据库连接池配置
    DB_POOL_SIZE: int = Field(default=20, env="DB_POOL_SIZE", description="数据库连接池大小")
    DB_MAX_OVERFLOW: int = Field(default=30, env="DB_MAX_OVERFLOW", description="数据库连接池最大溢出")
    DB_POOL_TIMEOUT: int = Field(default=30, env="DB_POOL_TIMEOUT", description="数据库连接超时时间")
    DB_POOL_RECYCLE: int = Field(default=3600, env="DB_POOL_RECYCLE", description="数据库连接回收时间")

    # JWT 认证配置 - 必须通过环境变量设置
    SECRET_KEY: str = Field(..., env="SECRET_KEY", description="JWT密钥")
    ALGORITHM: str = Field(default="HS256", env="ALGORITHM", description="JWT算法")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=1440, env="ACCESS_TOKEN_EXPIRE_MINUTES", description="访问令牌过期时间(分钟)")
    REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, env="REFRESH_TOKEN_EXPIRE_DAYS", description="刷新令牌过期时间(天)")

    # CORS 配置 - 限制来源
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:5173", "http://127.0.0.1:3000", "http://127.0.0.1:5173"],
        env="CORS_ORIGINS",
        description="允许的CORS来源"
    )
    cors_methods: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
        description="允许的HTTP方法"
    )
    cors_headers: List[str] = Field(
        default=["*"],
        description="允许的请求头"
    )

    # 时区配置
    TIMEZONE: str = Field(default="Asia/Shanghai", env="TIMEZONE", description="应用时区")

    # 日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL", description="日志级别")

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "allow"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


# 全局配置实例
settings = Settings()