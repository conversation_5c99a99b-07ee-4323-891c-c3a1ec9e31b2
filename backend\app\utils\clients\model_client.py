"""
简化的统一模型客户端
使用 OpenAI 库提供统一的模型调用接口
"""
import asyncio
import time
import socket
import httpx
from urllib.parse import urlparse
from typing import Dict, Any, List, Optional, AsyncGenerator
from openai import AsyncOpenAI
from app.models.model.model_config import ModelConfig

from app.utils.logger import setup_logger

logger = setup_logger()


class ModelClient:
    """
    统一模型客户端
    使用 OpenAI 库统一调用各种模型平台
    """
    
    def __init__(self, model_config: ModelConfig, skip_network_test: bool = False):
        """初始化客户端"""
        self.model_config = model_config
        self.timeout = model_config.timeout_seconds
        self.skip_network_test = skip_network_test
        self._client: Optional[AsyncOpenAI] = None
    
    def _get_client(self) -> AsyncOpenAI:
        """获取或创建 AsyncOpenAI 客户端"""
        if self._client is None:
            # 直接使用明文 API 密钥
            api_key = self.model_config.api_key if self.model_config.api_key else "dummy-key"
            logger.debug(f"模型 {self.model_config.name} API Key长度: {len(api_key)}")
            logger.debug(f"模型 {self.model_config.name} API Key前缀: {api_key[:10] if len(api_key) > 10 else api_key}")
            logger.debug(f"模型 {self.model_config.name} API URL: {self.model_config.api_url}")
            logger.debug(f"模型 {self.model_config.name} 模型名称: {self.model_config.model_name}")

            self._client = AsyncOpenAI(
                api_key=api_key,
                base_url=self.model_config.api_url.rstrip('/'),
                timeout=self.timeout,
                max_retries=2,  # 添加重试机制
                # 添加HTTP客户端配置
                http_client=None  # 使用默认的httpx客户端
            )

        return self._client

    def _format_messages(self, prompt: str) -> List[Dict[str, str]]:
        """格式化消息"""
        messages = []
        
        # 添加系统提示词（如果有）
        if self.model_config.prompt:
            messages.append({
                "role": "system",
                "content": self.model_config.prompt
            })
        
        # 添加用户消息
        messages.append({
            "role": "user", 
            "content": prompt
        })
        
        return messages

    def _test_dns_resolution(self) -> bool:
        """测试DNS解析"""
        try:
            parsed_url = urlparse(self.model_config.api_url)
            hostname = parsed_url.hostname
            if not hostname:
                return False

            # 尝试解析域名
            socket.gethostbyname(hostname)
            logger.debug(f"DNS解析成功: {hostname}")
            return True
        except Exception as e:
            logger.error(f"DNS解析失败 {hostname}: {str(e)}")
            return False

    async def _test_network_connectivity(self) -> bool:
        """测试网络连通性"""
        # 先测试DNS解析
        if not self._test_dns_resolution():
            return False

        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(self.model_config.api_url.rstrip('/'))
                logger.debug(f"网络连通性测试成功: {response.status_code}")
                return response.status_code < 500  # 只要不是服务器错误就认为连通
        except Exception as e:
            logger.error(f"网络连通性测试失败: {str(e)}")
            return False

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        start_time = time.time()

        # 先测试网络连通性（可选）
        if not self.skip_network_test:
            logger.debug(f"开始测试模型 {self.model_config.name} 的网络连通性")
            if not await self._test_network_connectivity():
                response_time = int((time.time() - start_time) * 1000)
                return {
                    'is_healthy': False,
                    'response_time_ms': response_time,
                    'error_message': 'Network connectivity test failed',
                    'details': f"无法连接到 {self.model_config.api_url} (DNS解析失败或网络不通)"
                }
        else:
            logger.debug(f"跳过模型 {self.model_config.name} 的网络连通性测试")

        try:
            client = self._get_client()
            
            # 使用简单的测试消息
            response = await client.chat.completions.create(
                model=self.model_config.model_name,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10,
                timeout=self.timeout
            )
            
            response_time = int((time.time() - start_time) * 1000)
            
            return {
                'is_healthy': True,
                'response_time_ms': response_time,
                'error_message': None,
                'details': f"模型 {self.model_config.model_name} 响应正常"
            }
            
        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            error_msg = str(e)
            error_type = type(e).__name__

            # 更详细的错误日志
            logger.error(f"模型 {self.model_config.name} 健康检查失败:")
            logger.error(f"  API URL: {self.model_config.api_url}")
            logger.error(f"  Model Name: {self.model_config.model_name}")
            logger.error(f"  API Key: {self.model_config.api_key[:10] if self.model_config.api_key else 'None'}...")
            logger.error(f"  Timeout: {self.timeout}")
            logger.error(f"  Error Type: {error_type}")
            logger.error(f"  Error Message: {error_msg}")

            # 检查具体的错误类型
            if "Connection" in error_type or "connection" in error_msg.lower():
                details = f"网络连接失败: 无法连接到 {self.model_config.api_url}"
            elif "timeout" in error_msg.lower():
                details = f"请求超时: 超过 {self.timeout} 秒"
            elif "401" in error_msg or "unauthorized" in error_msg.lower():
                details = f"API Key 认证失败"
            elif "404" in error_msg:
                details = f"API 端点不存在: {self.model_config.api_url}"
            else:
                details = f"未知错误: {error_type} - {error_msg}"

            return {
                'is_healthy': False,
                'response_time_ms': response_time,
                'error_message': error_msg,
                'details': details
            }
    
    async def call_model(self, prompt: str, parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """调用模型（非流式）"""
        start_time = time.time()
        
        try:
            client = self._get_client()
            messages = self._format_messages(prompt)
            
            # 合并参数
            call_params = {
                'model': self.model_config.model_name,
                'messages': messages,
                'max_tokens': parameters.get('max_tokens', self.model_config.max_tokens) if parameters else self.model_config.max_tokens,
                'timeout': self.timeout
            }
            
            # 添加其他参数
            if parameters:
                for key, value in parameters.items():
                    if key not in ['max_tokens'] and value is not None:
                        call_params[key] = value
            
            response = await client.chat.completions.create(**call_params)
            
            response_time = int((time.time() - start_time) * 1000)
            
            return {
                'success': True,
                'response': response.choices[0].message.content,
                'response_time_ms': response_time,
                'error_message': None,
                'usage': {
                    'prompt_tokens': response.usage.prompt_tokens if response.usage else 0,
                    'completion_tokens': response.usage.completion_tokens if response.usage else 0,
                    'total_tokens': response.usage.total_tokens if response.usage else 0
                }
            }
            
        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            error_msg = str(e)
            
            logger.error(f"模型 {self.model_config.name} 调用失败: {error_msg}")
            
            return {
                'success': False,
                'response': None,
                'response_time_ms': response_time,
                'error_message': error_msg,
                'usage': None
            }
    
    async def call_model_stream(self, prompt: str, parameters: Optional[Dict[str, Any]] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """调用模型（流式）"""
        start_time = time.time()

        logger.info(f"ModelClient开始流式调用: {self.model_config.name}")
        logger.debug(f"ModelClient参数: prompt长度={len(prompt)}, parameters={parameters}")

        try:
            client = self._get_client()
            messages = self._format_messages(prompt)
            logger.debug(f"格式化消息: {messages}")

            # 合并参数
            call_params = {
                'model': self.model_config.model_name,
                'messages': messages,
                'max_tokens': parameters.get('max_tokens', self.model_config.max_tokens) if parameters else self.model_config.max_tokens,
                'stream': True,
                'timeout': self.timeout
            }

            logger.info(f"调用参数: model={call_params['model']}, max_tokens={call_params['max_tokens']}, timeout={call_params['timeout']}")
            logger.debug(f"完整调用参数: {call_params}")
            
            # 添加其他参数
            if parameters:
                logger.debug(f"添加额外参数: {parameters}")
                for key, value in parameters.items():
                    if key not in ['max_tokens'] and value is not None:
                        call_params[key] = value
                        logger.debug(f"添加参数: {key}={value}")

            logger.info(f"开始调用OpenAI API: {self.model_config.api_url}")
            stream = await client.chat.completions.create(**call_params)
            logger.info("成功创建流式响应")
            
            # 处理流式响应
            chunk_count = 0
            logger.info("开始处理流式响应")
            async for chunk in stream:
                chunk_count += 1
                if chunk_count <= 3:
                    logger.debug(f"收到原始chunk #{chunk_count}: {chunk}")

                if chunk.choices and chunk.choices[0].delta.content:
                    content_chunk = {
                        'type': 'content',
                        'content': chunk.choices[0].delta.content,
                        'finish_reason': chunk.choices[0].finish_reason
                    }
                    if chunk_count <= 3:
                        logger.debug(f"生成内容chunk: {content_chunk}")
                    yield content_chunk

                # 处理结束
                if chunk.choices and chunk.choices[0].finish_reason:
                    response_time = int((time.time() - start_time) * 1000)
                    done_chunk = {
                        'type': 'done',
                        'response_time_ms': response_time,
                        'finish_reason': chunk.choices[0].finish_reason
                    }
                    logger.info(f"流式调用完成: {done_chunk}")
                    yield done_chunk
                    break

            logger.info(f"流式响应处理完成，总共处理了 {chunk_count} 个chunks")
            
        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            error_msg = str(e)
            error_type = type(e).__name__

            logger.error(f"模型 {self.model_config.name} 流式调用失败:")
            logger.error(f"  错误类型: {error_type}")
            logger.error(f"  错误信息: {error_msg}")
            logger.error(f"  API URL: {self.model_config.api_url}")
            logger.error(f"  模型名称: {self.model_config.model_name}")
            import traceback
            logger.error(f"  错误堆栈: {traceback.format_exc()}")

            error_chunk = {
                'type': 'error',
                'error_message': error_msg,
                'response_time_ms': response_time
            }
            logger.debug(f"返回错误chunk: {error_chunk}")
            yield error_chunk

    async def close(self):
        """关闭客户端连接"""
        if self._client:
            await self._client.close()
            self._client = None
    
    async def close(self):
        """关闭客户端"""
        if self._client:
            await self._client.close()
            self._client = None
