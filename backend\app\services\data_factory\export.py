"""
数据导出服务
提供数据导出的业务逻辑处理
"""
import os
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService
from app.repositories.data_factory.generation_task import GenerationTaskRepository
from app.models.data_factory.generation_task import GenerationTask
from app.schemas.data_factory.generation_task import GenerationTaskResponse
from app.core.exceptions import raise_not_found, raise_validation_error
from app.utils.data_factory.exporters import DataExporterEngine


class DataExportService:
    """
    数据导出服务类
    提供数据导出相关的业务逻辑处理
    """

    def __init__(self, db: AsyncSession):
        self.db = db
        self.task_repository = GenerationTaskRepository(db)
        self.exporter_engine = DataExporterEngine()

    async def download_task_result(self, task_id: int) -> Dict[str, Any]:
        """
        下载任务结果文件
        
        Args:
            task_id: 任务ID
            
        Returns:
            文件信息字典
        """
        # 获取任务信息
        task = await self.task_repository.get(task_id)
        if not task:
            raise_not_found(f"任务ID {task_id} 不存在")
        
        # 检查任务状态
        if task.status != "completed":
            raise_validation_error("只能下载已完成任务的结果文件")
        
        # 检查文件是否存在
        if not task.result_file_path or not os.path.exists(task.result_file_path):
            raise_not_found("结果文件不存在或已被删除")
        
        return {
            'file_path': task.result_file_path,
            'file_name': os.path.basename(task.result_file_path),
            'file_size': task.result_file_size,
            'export_format': task.export_format,
            'task_name': task.name
        }

    async def get_supported_formats(self) -> List[Dict[str, Any]]:
        """
        获取支持的导出格式列表
        
        Returns:
            支持的格式列表
        """
        return [
            {
                'format': 'json',
                'name': 'JSON格式',
                'description': '标准JSON格式，适合程序处理',
                'extension': '.json',
                'mime_type': 'application/json'
            },
            {
                'format': 'csv',
                'name': 'CSV格式',
                'description': '逗号分隔值格式，适合Excel打开',
                'extension': '.csv',
                'mime_type': 'text/csv'
            },
            {
                'format': 'excel',
                'name': 'Excel格式',
                'description': 'Excel工作簿格式，支持复杂格式',
                'extension': '.xlsx',
                'mime_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            }
        ]

    async def validate_export_config(self, export_format: str, config: Optional[Dict[str, Any]]) -> bool:
        """
        验证导出配置
        
        Args:
            export_format: 导出格式
            config: 导出配置
            
        Returns:
            是否有效
        """
        return self.exporter_engine.validate_export_config(export_format, config)

    async def preview_export_sample(self, data: List[Dict[str, Any]], export_format: str, 
                                   config: Optional[Dict[str, Any]] = None) -> str:
        """
        预览导出样例
        
        Args:
            data: 数据列表
            export_format: 导出格式
            config: 导出配置
            
        Returns:
            样例文件路径
        """
        # 只取前5条数据作为样例
        sample_data = data[:5] if len(data) > 5 else data
        
        # 生成样例文件
        filename = f"sample_{export_format}"
        file_path = await self.exporter_engine.export_data(
            sample_data, export_format, filename, config
        )
        
        return file_path

    async def cleanup_expired_files(self, days: int = 7) -> Dict[str, Any]:
        """
        清理过期的导出文件
        
        Args:
            days: 保留天数
            
        Returns:
            清理结果统计
        """
        from datetime import datetime, timedelta
        
        # 获取过期任务
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # 这里应该查询过期的任务，但为了简化，我们先返回统计信息
        # 实际实现中需要查询数据库并删除文件
        
        return {
            'cleaned_files': 0,
            'freed_space': 0,
            'cutoff_date': cutoff_date.isoformat()
        }

    async def get_export_statistics(self) -> Dict[str, Any]:
        """
        获取导出统计信息
        
        Returns:
            导出统计信息
        """
        # 获取各格式的使用统计
        # 这里需要实现具体的统计查询逻辑
        
        return {
            'total_exports': 0,
            'format_distribution': {
                'json': 0,
                'csv': 0,
                'excel': 0
            },
            'total_file_size': 0,
            'avg_file_size': 0
        }
