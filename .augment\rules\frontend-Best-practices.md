---
type: "always_apply"
---


# 开发工作流程和最佳实践

## 核心原则

1. **一致性优先**: 保持代码风格、命名规范、组件使用的一致性
2. **类型安全**: 充分利用TypeScript的类型系统，减少运行时错误
3. **组件化思维**: 合理拆分组件，提高代码复用性和可维护性
4. **性能意识**: 关注应用性能，合理使用懒加载、虚拟滚动等优化技术
5. **用户体验**: 重视响应式设计、主题适配、加载状态等用户体验细节

## 开发检查清单

### 页面开发检查
- [ ] 使用标准页面模板结构 -如列表页参考环境管理页面，使用自定义组件ArtTable
- [ ] 实现搜索、表格、对话框组件
- [ ] 配置正确的路由和权限
- [ ] 添加适当的加载状态和错误处理
- [ ] 实现响应式设计
- [ ] 适配暗黑/白天模式

### 组件开发检查
- [ ] 定义清晰的Props和Emits接口
- [ ] 使用CSS变量确保主题适配
- [ ] 添加适当的TypeScript类型定义
- [ ] 实现响应式布局
- [ ] 添加必要的无障碍属性

### 代码质量检查
- [ ] 通过ESLint和Prettier检查
- [ ] 添加适当的注释和文档
- [ ] 遵循命名规范
- [ ] 处理边界情况和错误状态
- [ ] 优化性能和用户体验


