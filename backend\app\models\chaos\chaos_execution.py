"""
混沌测试执行记录数据模型
"""
from datetime import datetime
from typing import Dict, Any, Optional

from sqlalchemy import Column, String, Integer, Text, JSON, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class ChaosExecution(BaseModel):
    """
    任务执行记录模型
    记录每次故障注入的详细执行信息
    """
    __tablename__ = "chaos_executions"

    # 关联信息
    task_id = Column(Integer, ForeignKey("chaos_tasks.id"), nullable=True, comment="单次任务ID")
    batch_task_id = Column(Integer, ForeignKey("chaos_batch_tasks.id"), nullable=True, comment="批次任务ID")
    batch_task_item_id = Column(Integer, ForeignKey("chaos_batch_task_items.id"), nullable=True, comment="批次任务子项ID")
    host_id = Column(Integer, nullable=False, comment="执行主机ID")
    host_info = Column(JSON, nullable=True, comment="主机信息快照")
    
    # 执行信息
    start_time = Column(DateTime, nullable=True, comment="开始时间")
    end_time = Column(DateTime, nullable=True, comment="结束时间")
    status = Column(String(20), default="pending", comment="执行状态：pending/running/success/failed/cancelled")
    
    # ChaosBlade相关
    chaos_uid = Column(String(100), nullable=True, index=True, comment="ChaosBlade执行UID")
    command = Column(Text, nullable=True, comment="执行的ChaosBlade命令")
    blade_version = Column(String(50), nullable=True, comment="ChaosBlade版本")
    
    # 执行结果
    output = Column(Text, nullable=True, comment="执行输出")
    error_message = Column(Text, nullable=True, comment="错误信息")
    exit_code = Column(Integer, nullable=True, comment="命令退出码")
    
    # 故障配置快照
    fault_config = Column(JSON, nullable=True, comment="故障配置快照")

    # 监控配置
    monitor_config = Column(JSON, nullable=True, comment="监控配置")

    # 执行统计
    duration_seconds = Column(Integer, nullable=True, comment="执行时长(秒)")
    retry_count = Column(Integer, default=0, comment="重试次数")
    
    # 标记信息
    is_auto_destroyed = Column(Boolean, default=False, comment="是否已自动销毁")
    destroy_time = Column(DateTime, nullable=True, comment="销毁时间")
    destroy_output = Column(Text, nullable=True, comment="销毁命令输出")

    # 关系映射
    task = relationship("ChaosTask", back_populates="executions")
    batch_task = relationship("ChaosBatchTask", back_populates="executions")
    batch_task_item = relationship("ChaosBatchTaskItem")

    def __repr__(self) -> str:
        return f"<ChaosExecution(id={self.id}, task_id={self.task_id}, host_id={self.host_id}, status={self.status})>"

    @property
    def is_running(self) -> bool:
        """检查执行是否正在运行"""
        return self.status == "running"

    @property
    def is_completed(self) -> bool:
        """检查执行是否已完成"""
        return self.status in ["success", "failed", "cancelled"]

    @property
    def is_successful(self) -> bool:
        """检查执行是否成功"""
        return self.status == "success"

    @property
    def has_chaos_uid(self) -> bool:
        """检查是否有ChaosBlade UID"""
        return bool(self.chaos_uid)

    @property
    def execution_duration(self) -> Optional[int]:
        """计算执行时长"""
        if self.start_time and self.end_time:
            delta = self.end_time - self.start_time
            return int(delta.total_seconds())
        return self.duration_seconds

    def start_execution(self, command: str, chaos_uid: str = None) -> None:
        """开始执行"""
        from app.utils.timezone import now
        self.start_time = now()
        self.status = "running"
        self.command = command
        if chaos_uid:
            self.chaos_uid = chaos_uid

    def complete_execution(self, success: bool, output: str = None, error: str = None, exit_code: int = None) -> None:
        """完成执行"""
        self.end_time = datetime.now()
        self.status = "success" if success else "failed"
        if output:
            self.output = output
        if error:
            self.error_message = error
        if exit_code is not None:
            self.exit_code = exit_code
        
        # 计算执行时长
        if self.start_time:
            delta = self.end_time - self.start_time
            self.duration_seconds = int(delta.total_seconds())

    def cancel_execution(self, reason: str = None) -> None:
        """取消执行"""
        self.end_time = datetime.now()
        self.status = "cancelled"
        if reason:
            self.error_message = reason

    def mark_destroyed(self, destroy_output: str = None) -> None:
        """标记为已销毁"""
        self.is_auto_destroyed = True
        self.destroy_time = datetime.now()
        if destroy_output:
            self.destroy_output = destroy_output

    def increment_retry(self) -> None:
        """增加重试次数"""
        self.retry_count = (self.retry_count or 0) + 1

    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要信息"""
        return {
            "id": self.id,
            "task_id": self.task_id,
            "host_id": self.host_id,
            "status": self.status,
            "chaos_uid": self.chaos_uid,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration_seconds": self.execution_duration,
            "retry_count": self.retry_count,
            "is_auto_destroyed": self.is_auto_destroyed,
            "exit_code": self.exit_code,
            "has_error": bool(self.error_message)
        }

    def get_detailed_info(self) -> Dict[str, Any]:
        """获取详细执行信息"""
        summary = self.get_execution_summary()
        summary.update({
            "command": self.command,
            "output": self.output,
            "error_message": self.error_message,
            "fault_config": self.fault_config,
            "host_info": self.host_info,
            "blade_version": self.blade_version,
            "destroy_time": self.destroy_time.isoformat() if self.destroy_time else None,
            "destroy_output": self.destroy_output
        })
        return summary
