<template>
  <ArtSearchBar
    :filter="searchForm"
    :items="searchItems"
    @search="handleSearch"
    @reset="handleReset"
  />
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed } from 'vue'
import ArtSearchBar from '@/components/core/forms/art-search-bar/index.vue'
import { DataFactoryService } from '@/api/dataFactoryApi'

interface Props {
  filter: {
    status: string
    model_id: string
    dateRange: any[]
  }
}

interface Emits {
  (e: 'update:filter', value: any): void
  (e: 'search'): void
  (e: 'reset'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const models = ref<Api.DataFactory.ModelInfo[]>([])

// 搜索表单
const searchForm = reactive({
  status: '',
  model_id: '',
  dateRange: []
})

// 搜索项配置
const searchItems = computed(() => [
  {
    prop: 'status',
    label: '任务状态',
    type: 'select',
    placeholder: '选择状态',
    clearable: true,
    options: [
      { label: '等待执行', value: 'pending' },
      { label: '正在执行', value: 'running' },
      { label: '执行完成', value: 'completed' },
      { label: '执行失败', value: 'failed' },
      { label: '已取消', value: 'cancelled' }
    ]
  },
  {
    prop: 'model_id',
    label: '数据模型',
    type: 'select',
    placeholder: '选择模型',
    clearable: true,
    filterable: true,
    options: models.value.map(model => ({ label: model.name, value: model.id.toString() }))
  },
  {
    prop: 'dateRange',
    label: '创建时间',
    type: 'daterange',
    placeholder: ['开始日期', '结束日期'],
    clearable: true
  }
])

// 监听props变化
watch(
  () => props.filter,
  (newFilter) => {
    Object.assign(searchForm, newFilter)
  },
  { immediate: true, deep: true }
)

// 监听表单变化
watch(
  searchForm,
  (newForm) => {
    emit('update:filter', { ...newForm })
  },
  { deep: true }
)

// 页面初始化
onMounted(() => {
  loadModels()
})

// 加载模型列表
const loadModels = async () => {
  try {
    const response = await DataFactoryService.getDataModels({ current: 1, size: 100 })
    models.value = response.records || []
  } catch (error) {
    console.error('加载模型列表失败:', error)
    models.value = []
  }
}

// 事件处理
const handleSearch = () => {
  emit('search')
}

const handleReset = () => {
  Object.assign(searchForm, {
    status: '',
    model_id: '',
    dateRange: []
  })
  emit('reset')
}
</script>

<style scoped lang="scss">
// 使用项目标准样式，无需额外样式
</style>
