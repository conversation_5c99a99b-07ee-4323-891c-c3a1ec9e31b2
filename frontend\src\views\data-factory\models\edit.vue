<template>
  <div class="model-edit-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="handleBack" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div class="title-section">
          <h2 class="page-title">编辑数据模型</h2>
          <p class="page-description">修改测试数据的结构和生成规则</p>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="handlePreview" :disabled="!canPreview">
          <el-icon><View /></el-icon>
          预览数据
        </el-button>
        <el-button type="primary" :loading="saving" @click="handleSave">
          <el-icon><Check /></el-icon>
          保存修改
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 表单内容 -->
    <div v-else-if="modelData" class="form-content">
      <el-card class="form-card">
        <el-tabs v-model="activeTab" class="model-tabs">
          <!-- 基本信息标签页 -->
          <el-tab-pane label="基本信息" name="basic">
            <div class="tab-content">
              <el-form
                ref="basicFormRef"
                :model="modelForm"
                :rules="basicRules"
                label-width="120px"
                class="basic-form"
              >
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="模型名称" prop="name" required>
                      <el-input
                        v-model="modelForm.name"
                        placeholder="请输入模型名称"
                        clearable
                        maxlength="100"
                        show-word-limit
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="版本号" prop="version">
                      <el-input
                        v-model="modelForm.version"
                        placeholder="版本号"
                        clearable
                        maxlength="20"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="模型分类" prop="category">
                      <el-select
                        v-model="modelForm.category"
                        placeholder="选择或输入分类"
                        filterable
                        allow-create
                        clearable
                        style="width: 100%"
                      >
                        <el-option
                          v-for="category in categories"
                          :key="category"
                          :label="category"
                          :value="category"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="状态" prop="status">
                      <el-radio-group v-model="modelForm.status">
                        <el-radio label="1">启用</el-radio>
                        <el-radio label="2">禁用</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-form-item label="模型描述" prop="description">
                  <el-input
                    v-model="modelForm.description"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入模型描述"
                    maxlength="1000"
                    show-word-limit
                  />
                </el-form-item>

                <el-form-item label="标签" prop="tags">
                  <el-select
                    v-model="modelForm.tags"
                    multiple
                    filterable
                    allow-create
                    placeholder="添加标签"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="tag in commonTags"
                      :key="tag"
                      :label="tag"
                      :value="tag"
                    />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>

          <!-- 字段配置标签页 -->
          <el-tab-pane name="fields">
            <template #label>
              <span>字段配置</span>
              <el-badge :value="modelForm.fields_config.length" class="field-badge" />
            </template>
            
            <div class="tab-content">
              <div class="fields-header">
                <div class="header-info">
                  <h3>字段配置管理</h3>
                  <p>当前共有 {{ modelForm.fields_config.length }} 个字段</p>
                </div>
                <el-button type="primary" @click="handleAddField">
                  <el-icon><Plus /></el-icon>
                  添加字段
                </el-button>
              </div>

              <div class="fields-section">
                <div v-if="modelForm.fields_config.length === 0" class="empty-fields">
                  <el-empty description="暂无字段配置">
                    <el-button type="primary" @click="handleAddField">添加第一个字段</el-button>
                  </el-empty>
                </div>

                <div v-else class="fields-list">
                  <div
                    v-for="(field, index) in modelForm.fields_config"
                    :key="index"
                    class="field-item"
                  >
                    <FieldConfigCard
                      :field="field"
                      :index="index"
                      :is-last="index === modelForm.fields_config.length - 1"
                      @update="handleUpdateField"
                      @delete="handleDeleteField"
                      @move-up="handleMoveFieldUp"
                      @move-down="handleMoveFieldDown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 使用统计标签页 -->
          <el-tab-pane label="使用统计" name="stats">
            <div class="tab-content">
              <div class="stats-section">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-card class="stat-card">
                      <div class="stat-content">
                        <div class="stat-icon usage">
                          <el-icon><TrendCharts /></el-icon>
                        </div>
                        <div class="stat-info">
                          <div class="stat-value">{{ modelData.usage_count || 0 }}</div>
                          <div class="stat-label">总使用次数</div>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="8">
                    <el-card class="stat-card">
                      <div class="stat-content">
                        <div class="stat-icon fields">
                          <el-icon><Grid /></el-icon>
                        </div>
                        <div class="stat-info">
                          <div class="stat-value">{{ modelForm.fields_config.length }}</div>
                          <div class="stat-label">字段数量</div>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="8">
                    <el-card class="stat-card">
                      <div class="stat-content">
                        <div class="stat-icon time">
                          <el-icon><Clock /></el-icon>
                        </div>
                        <div class="stat-info">
                          <div class="stat-value">{{ formatDate(modelData.updated_at) }}</div>
                          <div class="stat-label">最后更新</div>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>

                <!-- 模型信息详情 -->
                <div class="model-details">
                  <h4>模型详细信息</h4>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="创建时间">
                      {{ formatDateTime(modelData.created_at) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="创建者">
                      {{ modelData.created_by || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="最后更新">
                      {{ formatDateTime(modelData.updated_at) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="更新者">
                      {{ modelData.updated_by || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="模型ID">
                      {{ modelData.id }}
                    </el-descriptions-item>
                    <el-descriptions-item label="当前状态">
                      <el-tag :type="modelData.status === '1' ? 'success' : 'danger'">
                        {{ modelData.status === '1' ? '启用' : '禁用' }}
                      </el-tag>
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-result
        icon="error"
        title="加载失败"
        sub-title="无法加载模型数据，请检查网络连接或稍后重试"
      >
        <template #extra>
          <el-button type="primary" @click="loadModelData">重新加载</el-button>
          <el-button @click="handleBack">返回列表</el-button>
        </template>
      </el-result>
    </div>

    <!-- 字段配置对话框 -->
    <FieldConfigDialog
      v-model:visible="fieldDialog.visible"
      :field="fieldDialog.field"
      :mode="fieldDialog.mode"
      @confirm="handleConfirmField"
    />

    <!-- 数据预览对话框 -->
    <DataPreviewDialog
      v-model:visible="previewDialog.visible"
      :model-info="previewModelInfo"
      :preview-data="previewData"
      :loading="previewLoading"
      @refresh="handleRefreshPreview"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { 
  ArrowLeft, 
  View, 
  Check, 
  Plus, 
  TrendCharts, 
  Grid, 
  Clock 
} from '@element-plus/icons-vue'
import { DataFactoryService } from '@/api/dataFactoryApi'
import { formatDateTime } from '@/utils'
import FieldConfigCard from './components/FieldConfigCard.vue'
import FieldConfigDialog from './components/FieldConfigDialog.vue'
import DataPreviewDialog from './components/DataPreviewDialog.vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(true)
const saving = ref(false)
const activeTab = ref('basic')
const basicFormRef = ref<FormInstance>()
const modelData = ref<Api.DataFactory.ModelInfo | null>(null)
const categories = ref<string[]>([])
const commonTags = ref<string[]>(['测试数据', '用户数据', '订单数据', '商品数据', '日志数据'])
const previewData = ref<any[]>([])
const previewLoading = ref(false)

// 表单数据
const modelForm = reactive<Api.DataFactory.ModelForm>({
  name: '',
  description: '',
  version: '1.0.0',
  category: '',
  tags: [],
  fields_config: [],
  status: '1'
})

// 字段配置对话框
const fieldDialog = reactive({
  visible: false,
  field: null as Api.DataFactory.FieldConfig | null,
  mode: 'create' as 'create' | 'edit',
  index: -1
})

// 预览对话框
const previewDialog = reactive({
  visible: false
})

// 表单验证规则
const basicRules: FormRules = {
  name: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    { min: 1, max: 100, message: '名称长度应在 1 到 100 个字符', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/,
      message: '名称只能包含字母、数字、下划线和中文字符',
      trigger: 'blur'
    }
  ],
  version: [
    {
      pattern: /^\d+\.\d+\.\d+$/,
      message: '版本号格式应为 x.y.z（如：1.0.0）',
      trigger: 'blur'
    }
  ],
  description: [
    { max: 1000, message: '描述长度不能超过 1000 个字符', trigger: 'blur' }
  ],
  category: [
    { max: 50, message: '分类长度不能超过 50 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const canPreview = computed(() => {
  return modelForm.name && modelForm.fields_config.length > 0
})

const previewModelInfo = computed(() => {
  if (!canPreview.value || !modelData.value) return null
  
  return {
    ...modelData.value,
    ...modelForm
  } as Api.DataFactory.ModelInfo
})

// 页面初始化
onMounted(() => {
  loadModelData()
  loadCategories()
})

// 加载模型数据
const loadModelData = async () => {
  const modelId = Number(route.params.id)
  if (!modelId) {
    ElMessage.error('无效的模型ID')
    router.push('/data-factory/models')
    return
  }

  try {
    loading.value = true
    const response = await DataFactoryService.getDataModel(modelId)
    if (response.success) {
      modelData.value = response.data
      // 初始化表单数据
      Object.assign(modelForm, {
        name: response.data.name,
        description: response.data.description,
        version: response.data.version,
        category: response.data.category,
        tags: response.data.tags || [],
        fields_config: response.data.fields_config || [],
        status: response.data.status
      })
    }
  } catch (error) {
    ElMessage.error('加载模型数据失败')
    modelData.value = null
  } finally {
    loading.value = false
  }
}

// 加载分类
const loadCategories = async () => {
  try {
    const response = await DataFactoryService.getModelCategories()
    if (response.success) {
      categories.value = response.data
    }
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

// 事件处理
const handleBack = () => {
  router.push('/data-factory/models')
}

const handleAddField = () => {
  fieldDialog.field = null
  fieldDialog.mode = 'create'
  fieldDialog.index = -1
  fieldDialog.visible = true
}

const handleUpdateField = (index: number, field: Api.DataFactory.FieldConfig) => {
  fieldDialog.field = { ...field }
  fieldDialog.mode = 'edit'
  fieldDialog.index = index
  fieldDialog.visible = true
}

const handleDeleteField = (index: number) => {
  modelForm.fields_config.splice(index, 1)
}

const handleMoveFieldUp = (index: number) => {
  if (index > 0) {
    const field = modelForm.fields_config.splice(index, 1)[0]
    modelForm.fields_config.splice(index - 1, 0, field)
  }
}

const handleMoveFieldDown = (index: number) => {
  if (index < modelForm.fields_config.length - 1) {
    const field = modelForm.fields_config.splice(index, 1)[0]
    modelForm.fields_config.splice(index + 1, 0, field)
  }
}

const handleConfirmField = (field: Api.DataFactory.FieldConfig) => {
  if (fieldDialog.mode === 'create') {
    modelForm.fields_config.push(field)
  } else {
    modelForm.fields_config[fieldDialog.index] = field
  }
  fieldDialog.visible = false
}

const handlePreview = () => {
  if (!canPreview.value) return
  
  previewDialog.visible = true
  generatePreviewData()
}

const handleRefreshPreview = () => {
  generatePreviewData()
}

const generatePreviewData = async () => {
  if (!canPreview.value || !modelData.value) return
  
  try {
    previewLoading.value = true
    const response = await DataFactoryService.previewDataModel(modelData.value.id, 5)
    if (response.success) {
      previewData.value = response.data.preview_data
    }
  } catch (error) {
    ElMessage.error('生成预览数据失败')
  } finally {
    previewLoading.value = false
  }
}

const handleSave = async () => {
  if (!modelData.value) return

  try {
    // 验证基本信息
    if (!basicFormRef.value) return
    await basicFormRef.value.validate()
    
    if (modelForm.fields_config.length === 0) {
      ElMessage.error('请至少添加一个字段')
      activeTab.value = 'fields'
      return
    }
    
    saving.value = true
    
    const response = await DataFactoryService.updateDataModel(modelData.value.id, modelForm)
    if (response.success) {
      ElMessage.success('数据模型更新成功')
      // 更新本地数据
      modelData.value = response.data
    }
  } catch (error) {
    ElMessage.error('更新失败，请检查输入信息')
  } finally {
    saving.value = false
  }
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString()
}
</script>

<style scoped lang="scss">
.model-edit-page {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .back-button {
        padding: 8px 12px;
      }

      .title-section {
        .page-title {
          margin: 0 0 4px 0;
          font-size: 24px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .page-description {
          margin: 0;
          color: var(--el-text-color-regular);
          font-size: 14px;
        }
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .loading-container,
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }

  .form-content {
    .form-card {
      .model-tabs {
        .field-badge {
          margin-left: 8px;
        }

        .tab-content {
          padding: 20px 0;

          .basic-form {
            max-width: 800px;
          }

          .fields-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;

            .header-info {
              h3 {
                margin: 0 0 4px 0;
                font-size: 18px;
                font-weight: 600;
                color: var(--el-text-color-primary);
              }

              p {
                margin: 0;
                color: var(--el-text-color-regular);
                font-size: 14px;
              }
            }
          }

          .fields-section {
            .empty-fields {
              text-align: center;
              padding: 40px 0;
            }

            .fields-list {
              .field-item {
                margin-bottom: 16px;

                &:last-child {
                  margin-bottom: 0;
                }
              }
            }
          }

          .stats-section {
            .stat-card {
              .stat-content {
                display: flex;
                align-items: center;

                .stat-icon {
                  width: 48px;
                  height: 48px;
                  border-radius: 8px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-right: 16px;
                  font-size: 24px;

                  &.usage {
                    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                    color: white;
                  }

                  &.fields {
                    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
                    color: white;
                  }

                  &.time {
                    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
                    color: white;
                  }
                }

                .stat-info {
                  .stat-value {
                    font-size: 20px;
                    font-weight: 600;
                    color: var(--el-text-color-primary);
                    line-height: 1;
                  }

                  .stat-label {
                    font-size: 14px;
                    color: var(--el-text-color-regular);
                    margin-top: 4px;
                  }
                }
              }
            }

            .model-details {
              margin-top: 30px;

              h4 {
                margin: 0 0 16px 0;
                font-size: 16px;
                font-weight: 600;
                color: var(--el-text-color-primary);
              }
            }
          }
        }
      }
    }
  }
}
</style>
