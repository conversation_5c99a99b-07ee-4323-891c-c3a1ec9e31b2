<template>
  <div class="basic-info-step">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="step-form"
    >
      <el-form-item label="任务名称" prop="name">
        <el-input
          :model-value="formData.name"
          @update:model-value="(val) => formData.name = val"
          placeholder="请输入任务名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="任务描述" prop="description">
        <el-input
          :model-value="formData.description"
          @update:model-value="(val) => formData.description = val"
          type="textarea"
          :rows="3"
          placeholder="请输入任务描述（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <!-- 单次任务：多环境选择 -->
      <el-form-item v-if="!isBatch" label="目标环境" prop="env_ids">
        <el-select
          :model-value="formData.env_ids"
          @update:model-value="(val) => formData.env_ids = val"
          multiple
          placeholder="请选择目标环境"
          style="width: 100%"
          :loading="environmentsLoading"
        >
          <el-option
            v-for="env in environments"
            :key="env.id"
            :label="env.name"
            :value="env.id"
          >
            <span>{{ env.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ env.description }}
            </span>
          </el-option>
        </el-select>
        <div class="form-tip">
          选择故障注入的目标环境，可以选择多个环境
        </div>
      </el-form-item>

      <!-- 批次任务：单环境选择 -->
      <el-form-item v-if="isBatch" label="目标环境" prop="env_id">
        <el-select
          :model-value="formData.env_id"
          @update:model-value="(val) => formData.env_id = val"
          placeholder="请选择目标环境"
          style="width: 100%"
          :loading="environmentsLoading"
        >
          <el-option
            v-for="env in environments"
            :key="env.id"
            :label="env.name"
            :value="env.id"
          >
            <span>{{ env.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ env.description }}
            </span>
          </el-option>
        </el-select>
        <div class="form-help-text">
          <p>
            <el-icon><InfoFilled /></el-icon> 批次任务只能选择一个环境，多个子任务将在此环境中按配置的模式执行
          </p>
        </div>
      </el-form-item>

      <!-- 批次任务：执行模式 -->
      <el-form-item v-if="isBatch" label="执行模式" prop="batch_execution_mode">
        <el-radio-group
          :model-value="formData.batch_execution_mode"
          @update:model-value="(val) => formData.batch_execution_mode = val as string"
        >
          <el-radio value="sequential">间隔执行</el-radio>
          <el-radio value="order">连续执行</el-radio>
        </el-radio-group>
        <div class="form-help-text">
          <p v-if="formData.batch_execution_mode === 'sequential'">
            <el-icon><InfoFilled /></el-icon> 子任务之间有等待间隔，可设置等待时间
          </p>
          <p v-if="formData.batch_execution_mode === 'order'">
            <el-icon><InfoFilled /></el-icon> 子任务连续执行，前一个执行后立即执行下一个
          </p>
        </div>
      </el-form-item>

      <!-- 批次任务：等待时间配置 -->
      <el-form-item
        v-if="isBatch && formData.batch_execution_mode === 'sequential'"
        label="等待时间"
        prop="wait_time"
      >
        <div class="wait-time-config">
          <el-input-number
            :model-value="formData.wait_time"
            @update:model-value="(val) => formData.wait_time = val"
            :min="0"
            :max="3600"
            placeholder="秒"
            style="width: 120px"
          />
          <span>秒</span>
        </div>
        <div class="form-help-text">
          <p>
            <el-icon><InfoFilled /></el-icon> 每个子任务完成后等待的时间，0表示立即执行下一个
          </p>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import type { EnvironmentResponse } from '@/types/api/environment'

interface Props {
  formData: {
    name: string
    description?: string
    env_ids?: number[]
    env_id?: number
    batch_execution_mode?: string
    wait_time?: number
  }
  environments: EnvironmentResponse[]
  environmentsLoading?: boolean
  isBatch?: boolean
}

const props = defineProps<Props>()

// 计算属性
const isBatch = computed(() => props.isBatch || false)

const formRef = ref<FormInstance>()

const rules = computed((): FormRules => {
  const baseRules: FormRules = {
    name: [
      { required: true, message: '请输入任务名称', trigger: 'blur' },
      { min: 1, max: 100, message: '任务名称长度在 1 到 100 个字符', trigger: 'blur' }
    ]
  }

  if (isBatch.value) {
    // 批次任务验证规则
    baseRules.env_id = [
      { required: true, message: '请选择目标环境', trigger: 'change' }
    ]
    baseRules.batch_execution_mode = [
      { required: true, message: '请选择执行模式', trigger: 'change' }
    ]
  } else {
    // 单次任务验证规则
    baseRules.env_ids = [
      { required: true, message: '请选择目标环境', trigger: 'change' },
      { type: 'array', min: 1, message: '至少选择一个环境', trigger: 'change' }
    ]
  }

  return baseRules
})

// 暴露验证方法
const validate = () => {
  return formRef.value?.validate()
}

defineExpose({
  validate
})
</script>

<style scoped>
.basic-info-step {
  padding: 20px 0;
}

.step-form {
  max-width: 600px;
  margin: 0 auto;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

.form-help-text {
  margin-left: 5px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.form-help-text p {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.form-help-text .el-icon {
  color: var(--el-color-info);
}

.wait-time-config {
  display: flex;
  align-items: center;
  gap: 8px;
}

.wait-time-config span {
  font-size: 14px;
  color: var(--el-text-color-regular);
}
</style>
