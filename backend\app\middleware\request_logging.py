"""
统一请求监控中间件
集成性能监控和日志记录功能
优化版本：减少不必要的日志记录，提升性能
"""
import time
import asyncio
from typing import Callable, Set
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.utils.logger import operation_logger, setup_logger

logger = setup_logger()

# 跳过日志记录的路径（静态资源、健康检查等）
SKIP_LOG_PATHS: Set[str] = {
    "/docs", "/redoc", "/openapi.json", "/favicon.ico",
    "/health", "/ping", "/metrics"
}

# 跳过日志记录的路径前缀
SKIP_LOG_PREFIXES: Set[str] = {
    "/uploads/", "/static/", "/assets/"
}


class UnifiedRequestMiddleware(BaseHTTPMiddleware):
    """
    统一请求监控中间件（性能优化版本）
    - 减少不必要的日志记录
    - 异步日志写入，避免阻塞
    - 智能过滤静态资源请求
    """

    def __init__(self, app, log_slow_requests: bool = True, slow_threshold: float = 1.0,
                 log_threshold: float = 0.2):  # 提高日志记录阈值到200ms
        super().__init__(app)
        self.log_slow_requests = log_slow_requests
        self.slow_threshold = slow_threshold
        self.log_threshold = log_threshold  # 新增：只记录超过此阈值的请求

    def _should_skip_logging(self, path: str) -> bool:
        """判断是否应该跳过日志记录"""
        # 检查完全匹配的路径
        if path in SKIP_LOG_PATHS:
            return True

        # 检查路径前缀
        for prefix in SKIP_LOG_PREFIXES:
            if path.startswith(prefix):
                return True

        return False

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求并记录日志和性能指标（优化版本）

        Args:
            request: HTTP请求
            call_next: 下一个处理器

        Returns:
            HTTP响应
        """
        start_time = time.time()
        path = request.url.path
        method = request.method

        # 检查是否需要跳过日志记录
        skip_logging = self._should_skip_logging(path)

        try:
            # 处理请求
            response = await call_next(request)

            # 计算处理时间
            process_time = time.time() - start_time

            # 添加性能监控响应头
            response.headers["X-Process-Time"] = f"{process_time:.3f}"

            # 性能警告日志（始终记录慢请求）
            if self.log_slow_requests and process_time > self.slow_threshold:
                logger.warning(
                    f"慢请求警告 - {method} {path} - "
                    f"处理时间: {process_time:.3f}s"
                )
            elif process_time > 0.5:  # 中等慢请求提醒
                logger.info(
                    f"性能提醒 - {method} {path} - "
                    f"处理时间: {process_time:.3f}s"
                )

            # 优化的日志记录策略
            should_log = (
                not skip_logging and  # 不在跳过列表中
                (process_time > self.log_threshold or  # 超过日志阈值
                 response.status_code >= 400)  # 或者是错误请求
            )

            if should_log:
                # 异步记录日志，避免阻塞请求处理
                asyncio.create_task(self._log_request_async(
                    method, str(request.url), request, response, process_time
                ))

            return response

        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time

            # 异常请求始终记录日志
            if not skip_logging:
                asyncio.create_task(self._log_error_async(
                    method, str(request.url), request, process_time, str(e)
                ))

            # 重新抛出异常
            raise

    async def _log_request_async(self, method: str, url: str, request: Request,
                                response: Response, process_time: float):
        """异步记录请求日志"""
        try:
            client_ip = request.client.host if request.client else "unknown"
            headers = dict(request.headers)

            await operation_logger.log_operation(
                operation="http_request",
                request_data={
                    "method": method,
                    "url": url,
                    "client_ip": client_ip,
                    "user_agent": headers.get("user-agent", "unknown"),
                    "status_code": response.status_code,
                    "process_time": f"{process_time:.3f}s"
                },
                result="success" if response.status_code < 400 else "warning"
            )
        except Exception as e:
            # 日志记录失败不应影响主流程
            logger.error(f"记录请求日志失败: {e}")

    async def _log_error_async(self, method: str, url: str, request: Request,
                              process_time: float, error_msg: str):
        """异步记录错误日志"""
        try:
            client_ip = request.client.host if request.client else "unknown"
            headers = dict(request.headers)

            await operation_logger.log_operation(
                operation="http_request",
                request_data={
                    "method": method,
                    "url": url,
                    "client_ip": client_ip,
                    "user_agent": headers.get("user-agent", "unknown"),
                    "process_time": f"{process_time:.3f}s"
                },
                result="failed",
                error_msg=error_msg
            )
        except Exception as e:
            # 日志记录失败不应影响主流程
            logger.error(f"记录错误日志失败: {e}")