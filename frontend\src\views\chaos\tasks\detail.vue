<template>
  <div class="task-detail" v-loading="loading">
    <div v-if="task">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <el-button @click="handleBack" :icon="ArrowLeft">返回</el-button>
          <div class="header-info">
            <h2>{{ task.name }}</h2>
            <div class="header-meta">
              <el-tag :type="getStatusTagType(task.status)">
                {{ getStatusLabel(task.status) }}
              </el-tag>
              <span class="meta-item">创建时间：{{ formatDateTime(task.created_at) }}</span>
              <span class="meta-item">创建者：{{ task.created_by }}</span>
            </div>
          </div>
        </div>
        <div class="header-right">
          <el-button
            v-if="task.can_execute"
            type="primary"
            @click="handleExecuteTask"
            :loading="executing"
          >
            执行任务
          </el-button>
          <el-button
            v-if="task.can_pause"
            type="warning"
            @click="handlePauseTask"
          >
            暂停任务
          </el-button>
          <el-button
            v-if="task.can_terminate"
            type="danger"
            @click="handleTerminateTask"
          >
            终止任务
          </el-button>
          <el-dropdown @command="handleDropdownCommand">
            <el-button>
              更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="edit">编辑任务</el-dropdown-item>
                <el-dropdown-item command="copy">复制任务</el-dropdown-item>
                <el-dropdown-item command="monitor" divided>查看监控</el-dropdown-item>
                <el-dropdown-item command="export">导出配置</el-dropdown-item>
                <el-dropdown-item command="delete" divided>删除任务</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 任务信息卡片 -->
      <div class="content-section">
        <el-row :gutter="20">
          <!-- 基本信息 -->
          <el-col :span="12">
            <el-card title="基本信息" class="info-card">
              <template #header>
                <span>基本信息</span>
              </template>
              <div class="info-list">
                <div class="info-item">
                  <span class="label">任务名称：</span>
                  <span class="value">{{ task.name }}</span>
                </div>
                <div class="info-item">
                  <span class="label">任务描述：</span>
                  <span class="value">{{ task.description || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">目标环境：</span>
                  <span class="value">
                    <template v-if="task.environment_names && task.environment_names.length > 0">
                      <el-tag
                        v-for="(envName, index) in task.environment_names"
                        :key="index"
                        class="env-tag"
                        type="info"
                      >
                        {{ envName }}
                      </el-tag>
                    </template>
                    <span v-else>-</span>
                  </span>
                </div>
                <div class="info-item">
                  <span class="label">环境数量：</span>
                  <span class="value">{{ task.env_count || 0 }} 个</span>
                </div>
                <div class="info-item">
                  <span class="label">故障类型：</span>
                  <el-tag :type="getFaultTypeTagType(task.fault_type)">
                    {{ getFaultTypeLabel(task.fault_type) }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <span class="label">执行类型：</span>
                  <span class="value">{{ getExecutionTypeLabel(task.execution_type) }}</span>
                </div>
                <div v-if="task.scheduled_time" class="info-item">
                  <span class="label">执行时间：</span>
                  <span class="value">{{ formatDateTime(task.scheduled_time) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">自动销毁：</span>
                  <span class="value">{{ task.auto_destroy ? '是' : '否' }}</span>
                </div>
                <div v-if="task.max_duration" class="info-item">
                  <span class="label">最大时长：</span>
                  <span class="value">{{ task.max_duration }} 秒</span>
                </div>
              </div>
            </el-card>
          </el-col>

          <!-- 故障参数 -->
          <el-col :span="12">
            <el-card title="故障参数" class="info-card">
              <template #header>
                <span>故障参数</span>
              </template>
              <div class="fault-params">
                <pre>{{ JSON.stringify(task.fault_params, null, 2) }}</pre>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 执行结果 -->
        <el-card v-if="task.execution_result" title="执行结果" class="result-card">
          <template #header>
            <span>执行结果</span>
          </template>
          <div class="execution-result">
            <pre>{{ JSON.stringify(task.execution_result, null, 2) }}</pre>
          </div>
        </el-card>

        <!-- 执行记录 -->
        <el-card title="执行记录" class="execution-card">
          <template #header>
            <div class="card-header">
              <span>执行记录</span>
              <el-button size="small" @click="refreshExecutions">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <el-table
            v-loading="executionLoading"
            :data="executions"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="host_name" label="主机" width="120">
              <template #default="{ row }">
                {{ row.host_name || `Host-${row.host_id}` }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getExecutionStatusTagType(row.status)">
                  {{ getExecutionStatusLabel(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="chaos_uid" label="ChaosBlade UID" width="120">
              <template #default="{ row }">
                <span v-if="row.chaos_uid">{{ row.chaos_uid }}</span>
                <span v-else class="text-muted">-</span>
              </template>
            </el-table-column>
            <el-table-column prop="start_time" label="开始时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.start_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="duration_seconds" label="执行时长" width="100">
              <template #default="{ row }">
                <span v-if="row.duration_seconds">{{ row.duration_seconds }}s</span>
                <span v-else class="text-muted">-</span>
              </template>
            </el-table-column>
            <el-table-column prop="retry_count" label="重试次数" width="80" />
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="handleViewExecution(row)">详情</el-button>
                <el-button
                  v-if="row.status === 'failed'"
                  size="small"
                  type="primary"
                  @click="handleRetryExecution(row)"
                >
                  重试
                </el-button>
                <el-button
                  v-if="row.is_running"
                  size="small"
                  type="warning"
                  @click="handleCancelExecution(row)"
                >
                  取消
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-section">
            <el-pagination
              v-model:current-page="executionPagination.current"
              v-model:page-size="executionPagination.size"
              :total="executionPagination.total"
              :page-sizes="[10, 20, 50]"
              layout="total, sizes, prev, pager, next"
              @size-change="handleExecutionSizeChange"
              @current-change="handleExecutionCurrentChange"
            />
          </div>
        </el-card>
      </div>
    </div>

    <!-- 执行确认对话框 -->
    <el-dialog
      v-model="executeDialogVisible"
      title="执行任务确认"
      width="500px"
    >
      <div class="execute-dialog">
        <el-alert
          title="注意"
          type="warning"
          description="执行故障注入可能会影响目标系统的正常运行，请确认在合适的环境中进行测试。"
          show-icon
          :closable="false"
        />
        <div class="execute-options">
          <el-form :model="executeForm" label-width="100px">
            <el-form-item label="强制执行">
              <el-switch v-model="executeForm.force" />
              <div class="form-tip">忽略任务状态检查，强制执行</div>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <el-button @click="executeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmExecute" :loading="executing">
          确认执行
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, ArrowDown, Refresh } from '@element-plus/icons-vue'
import { useChaosTasksStore } from '@/store/business/chaos/tasks'
import { useChaosExecutionsStore } from '@/store/business/chaos/executions'
import type { ChaosTask, ChaosExecution } from '@/types/api/chaos'

const router = useRouter()
const route = useRoute()
const chaosTasksStore = useChaosTasksStore()
const chaosExecutionsStore = useChaosExecutionsStore()

// 响应式数据
const loading = ref(false)
const executing = ref(false)
const executionLoading = ref(false)
const task = ref<ChaosTask | null>(null)
const executions = ref<ChaosExecution[]>([])
const executeDialogVisible = ref(false)

// 执行表单
const executeForm = reactive({
  force: false
})

// 分页数据
const executionPagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 计算属性
const taskId = computed(() => Number(route.params.id))

// 生命周期
onMounted(() => {
  loadTaskDetail()
  loadExecutions()
})

// 方法
const loadTaskDetail = async () => {
  loading.value = true
  try {
    task.value = await chaosTasksStore.fetchTask(taskId.value)
  } catch (error) {
    ElMessage.error('加载任务详情失败')
    router.push('/chaos/tasks')
  } finally {
    loading.value = false
  }
}

const loadExecutions = async () => {
  executionLoading.value = true
  try {
    const result = await chaosExecutionsStore.fetchTaskExecutions(
      taskId.value,
      executionPagination.current,
      executionPagination.size
    )
    executions.value = result.records
    executionPagination.total = result.total
  } catch (error) {
    ElMessage.error('加载执行记录失败')
  } finally {
    executionLoading.value = false
  }
}

const refreshExecutions = () => {
  loadExecutions()
}

const handleBack = () => {
  router.push('/chaos/tasks')
}

const handleExecuteTask = () => {
  executeDialogVisible.value = true
}

const confirmExecute = async () => {
  executing.value = true
  try {
    await chaosTasksStore.executeTask(taskId.value, executeForm)
    ElMessage.success('任务执行成功')
    executeDialogVisible.value = false
    
    // 刷新任务详情和执行记录
    await loadTaskDetail()
    await loadExecutions()
  } catch (error) {
    ElMessage.error('任务执行失败')
  } finally {
    executing.value = false
  }
}

const handlePauseTask = async () => {
  try {
    await chaosTasksStore.pauseTask(taskId.value)
    ElMessage.success('任务已暂停')
    await loadTaskDetail()
  } catch (error) {
    ElMessage.error('暂停任务失败')
  }
}

const handleTerminateTask = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要终止任务吗？正在运行的故障注入将被停止。',
      '确认终止',
      { type: 'warning' }
    )
    
    await chaosTasksStore.terminateTask(taskId.value)
    ElMessage.success('任务已终止')
    await loadTaskDetail()
    await loadExecutions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('终止任务失败')
    }
  }
}

const handleDropdownCommand = (command: string) => {
  switch (command) {
    case 'edit':
      router.push(`/chaos/tasks/${taskId.value}/edit`)
      break
    case 'copy':
      router.push(`/chaos/tasks/create?copy=${taskId.value}`)
      break
    case 'monitor':
      handleViewMonitor()
      break
    case 'export':
      handleExportTask()
      break
    case 'delete':
      handleDeleteTask()
      break
  }
}

const handleViewMonitor = () => {
  // 跳转到监控页面，传递任务关联的环境信息
  if (task.value && task.value.env_ids && task.value.env_ids.length > 0) {
    // 使用任务关联的第一个环境
    const environmentId = task.value.env_ids[0]
    router.push({
      path: '/chaos/monitor',
      query: {
        environmentId: environmentId
      }
    })
  } else {
    // 没有环境信息，直接跳转到监控页面
    router.push('/chaos/monitor')
  }
}

const handleExportTask = () => {
  if (!task.value) return
  
  const exportData = {
    name: task.value.name,
    description: task.value.description,
    fault_type: task.value.fault_type,
    fault_params: task.value.fault_params,
    execution_type: task.value.execution_type,
    auto_destroy: task.value.auto_destroy,
    max_duration: task.value.max_duration
  }
  
  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `chaos-task-${task.value.name}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('任务配置已导出')
}

const handleDeleteTask = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务 "${task.value?.name}" 吗？此操作不可恢复。`,
      '确认删除',
      { type: 'warning' }
    )
    
    await chaosTasksStore.deleteTask(taskId.value)
    ElMessage.success('任务删除成功')
    router.push('/chaos/tasks')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除任务失败')
    }
  }
}

const handleViewExecution = (execution: ChaosExecution) => {
  router.push(`/chaos/executions/${execution.id}`)
}

const handleRetryExecution = async (execution: ChaosExecution) => {
  try {
    await chaosExecutionsStore.retryExecution(execution.id, {})
    ElMessage.success('重试执行成功')
    await loadExecutions()
  } catch (error) {
    ElMessage.error('重试执行失败')
  }
}

const handleCancelExecution = async (execution: ChaosExecution) => {
  try {
    await chaosExecutionsStore.cancelExecution(execution.id)
    ElMessage.success('执行已取消')
    await loadExecutions()
  } catch (error) {
    ElMessage.error('取消执行失败')
  }
}

const handleExecutionSizeChange = (size: number) => {
  executionPagination.size = size
  executionPagination.current = 1
  loadExecutions()
}

const handleExecutionCurrentChange = (current: number) => {
  executionPagination.current = current
  loadExecutions()
}

// 工具方法
const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待执行',
    running: '运行中',
    paused: '已暂停',
    completed: '已完成',
    failed: '已失败',
    cancelled: '已取消'
  }
  return labels[status] || status
}

const getStatusTagType = (status: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' | undefined => {
  const types: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    pending: 'info',
    running: 'primary',
    paused: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return types[status]
}

const getFaultTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    cpu: 'CPU故障',
    memory: '内存故障',
    network: '网络故障',
    disk: '磁盘故障',
    process: '进程故障',
    k8s: 'K8s故障',
  }
  return labels[type] || type
}

const getFaultTypeTagType = (type: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' | undefined => {
  const types: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    cpu: 'danger',
    memory: 'warning',
    network: 'info',
    disk: 'success',
    process: 'primary'
  }
  return types[type]
}

const getExecutionTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    immediate: '立即执行',
    scheduled: '定时执行',
    periodic: '周期执行'
  }
  return labels[type] || type
}

const getExecutionStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待执行',
    running: '运行中',
    success: '成功',
    failed: '失败',
    cancelled: '已取消',
    paused:'暂停',
    completed: '已完成'
  }
  return labels[status] || status
}

const getExecutionStatusTagType = (status: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' | undefined => {
  const types: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    pending: 'info',
    running: 'primary',
    success: 'success',
    failed: 'danger'
  }
  return types[status]
}

const formatDateTime = (dateTime: string | undefined) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.header-info h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.header-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
  color: #666;
}

.meta-item {
  display: flex;
  align-items: center;
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card {
  height: 100%;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: flex-start;
}

.info-item .label {
  width: 100px;
  color: #666;
  flex-shrink: 0;
}

.info-item .value {
  color: #333;
  flex: 1;
  word-break: break-all;
}

.env-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.fault-params {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 15px;
}

.fault-params pre {
  margin: 0;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
}

.result-card,
.execution-card {
  margin-top: 20px;
}

.execution-result {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 15px;
}

.execution-result pre {
  margin: 0;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.pagination-section {
  padding: 20px 0;
  text-align: right;
}

.text-muted {
  color: #999;
}

.execute-dialog {
  padding: 10px 0;
}

.execute-options {
  margin-top: 20px;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>
