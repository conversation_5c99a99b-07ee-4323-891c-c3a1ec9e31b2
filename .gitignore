### Python ###
# 虚拟环境
venv/

.venv/
venv.bak/

# 编译文件
__pycache__/
*.py[cod]
*.egg-info/
*.egg
dist/
build/

# 日志和缓存
*.log
.cache/
.pytest_cache/

# 数据库文件
*.sqlite
*.db
*.sqlite3

# 环境变量

.env.local
.env.*.local

### Vue3 ###
# 依赖目录
node_modules/
pnpm-lock.yaml
package-lock.json
yarn.lock

# 构建输出
dist/
dist-ssr/


# 编译缓存
.cache/
*.cache

# 测试覆盖
coverage/

# IDE
.idea/
.vscode/
*.code-workspace

# 系统文件
.DS_Store
Thumbs.db

### 通用 ###
# 操作系统文件
ehthumbs.db
[Ll]og/
[Tt]emp/

# 用户生成文件
*.suo
*.ntvs*
*.njsproj
*.sln
*.swp
*.swo

# 文档生成
docs/_build/

# 缓存和临时文件
.temp/
.tmp/

# 配置文件（视情况选择性取消注释）
# config/development.env
# config/production.env

chaosblade-1.7.4-linux-amd64.tar.gz
chaosblade-1.7.4-linux-arm64.tar.gz
chaosblade-operator-1.7.4.tar.gz