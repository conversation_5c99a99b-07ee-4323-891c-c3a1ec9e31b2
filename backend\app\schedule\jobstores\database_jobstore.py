"""
数据库作业存储器
基于APScheduler的JobStore接口实现
"""
import asyncio
import logging
import pickle
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any

from apscheduler.jobstores.base import BaseJobStore, JobLookupError, ConflictingIdError
from apscheduler.job import Job
from sqlalchemy import select, delete, update, and_, create_engine, text
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import sessionmaker, Session

from app.core.config import settings
from app.models.schedule.schedule_job import ScheduleJob

logger = logging.getLogger(__name__)


class DatabaseJobStore(BaseJobStore):
    """
    数据库作业存储器
    将APScheduler的作业信息存储到数据库中
    """

    def __init__(self):
        super().__init__()
        # 创建同步数据库连接（优化连接池配置）
        self._engine = create_engine(
            settings.DATABASE_URL.replace('aiomysql', 'pymysql'),
            pool_pre_ping=True,
            pool_recycle=3600,
            pool_size=5,  # 调度器使用较小的连接池
            max_overflow=10,  # 减少最大溢出连接
            pool_timeout=10,  # 减少连接超时时间
            echo=False  # 关闭SQL日志以提升性能
        )
        self._session_factory = sessionmaker(bind=self._engine)

    def _normalize_datetime(self, dt: Optional[datetime]) -> Optional[datetime]:
        """
        标准化datetime对象，将带时区的datetime转换为UTC并移除时区信息

        Args:
            dt: 可能包含时区信息的datetime对象

        Returns:
            标准化后的datetime对象（UTC时间，无时区信息）
        """
        if dt is None:
            return None

        # 如果datetime对象包含时区信息，转换为UTC并移除时区
        if dt.tzinfo is not None:
            # 转换为UTC时间
            utc_dt = dt.astimezone(timezone.utc)
            # 移除时区信息，返回naive datetime
            return utc_dt.replace(tzinfo=None)

        # 如果没有时区信息，直接返回
        return dt

    def _normalize_job_for_serialization(self, job: Job) -> bytes:
        """
        标准化Job对象并直接序列化为bytes，避免ZoneInfo序列化问题

        Args:
            job: 原始Job对象

        Returns:
            序列化后的bytes数据
        """
        try:
            # 先尝试直接序列化，如果失败则进行清理
            return pickle.dumps(job, pickle.HIGHEST_PROTOCOL)
        except (TypeError, AttributeError) as e:
            if 'ZoneInfo' in str(e) or 'JSON serializable' in str(e):
                # 如果是ZoneInfo序列化问题，创建一个清理后的副本
                import copy

                # 浅拷贝Job对象
                job_copy = copy.copy(job)

                # 标准化next_run_time
                if hasattr(job_copy, 'next_run_time') and job_copy.next_run_time:
                    job_copy.next_run_time = self._normalize_datetime(job_copy.next_run_time)

                # 处理trigger - 创建一个新的trigger副本
                if hasattr(job_copy, 'trigger') and job_copy.trigger:
                    original_trigger = job_copy.trigger

                    # 根据trigger类型重新创建
                    if hasattr(original_trigger, '__class__'):
                        trigger_class = original_trigger.__class__

                        # 获取trigger的基本参数，但排除ZoneInfo
                        trigger_kwargs = {}

                        # 复制基本属性，但处理时区相关的
                        for attr in ['seconds', 'minutes', 'hours', 'days', 'weeks', 'interval',
                                   'year', 'month', 'day', 'week', 'day_of_week', 'hour', 'minute', 'second',
                                   'run_date', 'start_date', 'end_date', 'jitter']:
                            if hasattr(original_trigger, attr):
                                value = getattr(original_trigger, attr)
                                if value is not None:
                                    if hasattr(value, 'tzinfo'):
                                        # datetime对象，标准化时区
                                        trigger_kwargs[attr] = self._normalize_datetime(value)
                                    else:
                                        trigger_kwargs[attr] = value

                        # 不包含timezone参数，让trigger使用默认时区
                        try:
                            new_trigger = trigger_class(**trigger_kwargs)
                            job_copy.trigger = new_trigger
                        except Exception:
                            # 如果重建失败，直接设置timezone为None
                            job_copy.trigger = copy.copy(original_trigger)
                            if hasattr(job_copy.trigger, 'timezone'):
                                job_copy.trigger.timezone = None

                # 尝试序列化清理后的对象
                return pickle.dumps(job_copy, pickle.HIGHEST_PROTOCOL)
            else:
                # 其他类型的错误，重新抛出
                raise

    def _clean_zoneinfo_from_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        递归清理字典中的ZoneInfo对象

        Args:
            data: 包含可能的ZoneInfo对象的字典

        Returns:
            清理后的字典
        """
        if not isinstance(data, dict):
            return data

        cleaned_data = {}
        for key, value in data.items():
            if value is None:
                cleaned_data[key] = None
            elif hasattr(value, 'key') and str(type(value).__name__) == 'ZoneInfo':  # ZoneInfo对象
                # 将ZoneInfo对象转换为字符串
                cleaned_data[key] = str(value.key)
            elif hasattr(value, 'name') and 'Field' in str(type(value).__name__):  # BaseField及其子类
                # 将BaseField对象转换为字典格式
                cleaned_data[key] = {
                    'name': value.name,
                    'expression': str(value),
                    'type': type(value).__name__
                }
            elif str(type(value).__name__) == 'timedelta':  # timedelta对象
                # 将timedelta对象转换为总秒数
                cleaned_data[key] = value.total_seconds()
            elif hasattr(value, 'isoformat'):  # datetime对象
                # 标准化datetime对象
                normalized_dt = self._normalize_datetime(value)
                cleaned_data[key] = normalized_dt.isoformat() if normalized_dt else None
            elif isinstance(value, dict):
                # 递归处理嵌套字典
                cleaned_data[key] = self._clean_zoneinfo_from_dict(value)
            elif isinstance(value, (list, tuple)):
                # 处理列表或元组
                cleaned_items = []
                for item in value:
                    if isinstance(item, dict):
                        cleaned_items.append(self._clean_zoneinfo_from_dict(item))
                    elif hasattr(item, 'key') and str(type(item).__name__) == 'ZoneInfo':  # ZoneInfo对象
                        cleaned_items.append(str(item.key))
                    elif hasattr(item, 'name') and 'Field' in str(type(item).__name__):  # BaseField及其子类
                        cleaned_items.append({
                            'name': item.name,
                            'expression': str(item),
                            'type': type(item).__name__
                        })
                    elif str(type(item).__name__) == 'timedelta':  # timedelta对象
                        cleaned_items.append(item.total_seconds())
                    elif hasattr(item, 'isoformat'):  # datetime对象
                        normalized_dt = self._normalize_datetime(item)
                        cleaned_items.append(normalized_dt.isoformat() if normalized_dt else None)
                    else:
                        cleaned_items.append(item)
                cleaned_data[key] = cleaned_items
            else:
                cleaned_data[key] = value

        return cleaned_data

    def _get_db_session(self) -> Session:
        """获取数据库会话"""
        return self._session_factory()
    
    def start(self, scheduler, alias):
        """启动作业存储器"""
        super().start(scheduler, alias)
        logger.info(f"数据库作业存储器已启动: {alias}")
    
    def shutdown(self):
        """关闭作业存储器"""
        if hasattr(self, '_engine'):
            self._engine.dispose()
        logger.info("数据库作业存储器已关闭")

    def lookup_job(self, job_id: str) -> Optional[Job]:
        """查找作业"""
        try:
            with self._get_db_session() as db:
                query = select(ScheduleJob).where(ScheduleJob.id == job_id)
                result = db.execute(query)
                schedule_job = result.scalar_one_or_none()

                if not schedule_job:
                    return None

                # 反序列化作业状态
                if schedule_job.job_state:
                    job = pickle.loads(schedule_job.job_state)
                    job._scheduler = self._scheduler
                    job._jobstore_alias = self._alias
                    return job

                return None

        except Exception as e:
            logger.error(f"查找作业失败: {job_id}, 错误: {str(e)}")
            return None
    
    def get_due_jobs(self, now: datetime) -> List[Job]:
        """获取到期的作业"""
        try:
            with self._get_db_session() as db:
                query = select(ScheduleJob).where(
                    and_(
                        ScheduleJob.next_run_time.isnot(None),
                        ScheduleJob.next_run_time <= now
                    )
                )
                result = db.execute(query)
                schedule_jobs = result.scalars().all()

                jobs = []
                for schedule_job in schedule_jobs:
                    if schedule_job.job_state:
                        try:
                            job = pickle.loads(schedule_job.job_state)
                            job._scheduler = self._scheduler
                            job._jobstore_alias = self._alias
                            jobs.append(job)
                        except Exception as e:
                            logger.error(f"反序列化作业失败: {schedule_job.id}, 错误: {str(e)}")

                return jobs

        except Exception as e:
            logger.error(f"获取到期作业失败: {str(e)}")
            return []
    
    def get_next_run_time(self) -> Optional[datetime]:
        """获取下次运行时间"""
        try:
            with self._get_db_session() as db:
                query = select(ScheduleJob.next_run_time).where(
                    ScheduleJob.next_run_time.isnot(None)
                ).order_by(ScheduleJob.next_run_time.asc()).limit(1)

                result = db.execute(query)
                next_run_time = result.scalar_one_or_none()

                return next_run_time

        except Exception as e:
            logger.error(f"获取下次运行时间失败: {str(e)}")
            return None

    def get_all_jobs(self) -> List[Job]:
        """获取所有作业"""
        try:
            with self._get_db_session() as db:
                query = select(ScheduleJob)
                result = db.execute(query)
                schedule_jobs = result.scalars().all()

                jobs = []
                for schedule_job in schedule_jobs:
                    if schedule_job.job_state:
                        try:
                            job = pickle.loads(schedule_job.job_state)
                            job._scheduler = self._scheduler
                            job._jobstore_alias = self._alias
                            jobs.append(job)
                        except Exception as e:
                            logger.error(f"反序列化作业失败: {schedule_job.id}, 错误: {str(e)}")

                return jobs

        except Exception as e:
            logger.error(f"获取所有作业失败: {str(e)}")
            return []

    def add_job(self, job: Job) -> None:
        """添加作业"""
        try:
            with self._get_db_session() as db:
                # 检查作业是否已存在
                existing_query = select(ScheduleJob).where(ScheduleJob.id == job.id)
                existing_result = db.execute(existing_query)
                if existing_result.scalar_one_or_none():
                    raise ConflictingIdError(job.id)

                # 标准化Job对象并序列化作业状态
                job_state = self._normalize_job_for_serialization(job)

                # 创建数据库记录
                schedule_job = ScheduleJob(
                    id=job.id,
                    name=job.name or job.id,
                    func=f"{job.func.__module__}.{job.func.__name__}" if hasattr(job.func, '__module__') else str(job.func),
                    args=list(job.args) if job.args else None,
                    kwargs=dict(job.kwargs) if job.kwargs else None,
                    trigger_type=job.trigger.__class__.__name__.lower().replace('trigger', ''),
                    trigger_args=self._serialize_trigger_args(job.trigger),
                    next_run_time=self._normalize_datetime(job.next_run_time),  # 标准化时区处理
                    job_state=job_state
                )

                db.add(schedule_job)
                db.commit()

                logger.info(f"作业已添加到数据库: {job.id}")

        except ConflictingIdError:
            raise
        except Exception as e:
            logger.error(f"添加作业失败: {job.id}, 错误: {str(e)}")
            raise

    def update_job(self, job: Job) -> None:
        """更新作业"""
        try:
            with self._get_db_session() as db:
                # 标准化Job对象并序列化作业状态
                job_state = self._normalize_job_for_serialization(job)

                # 更新数据库记录
                query = update(ScheduleJob).where(ScheduleJob.id == job.id).values(
                    name=job.name or job.id,
                    func=f"{job.func.__module__}.{job.func.__name__}" if hasattr(job.func, '__module__') else str(job.func),
                    args=list(job.args) if job.args else None,
                    kwargs=dict(job.kwargs) if job.kwargs else None,
                    trigger_type=job.trigger.__class__.__name__.lower().replace('trigger', ''),
                    trigger_args=self._serialize_trigger_args(job.trigger),
                    next_run_time=self._normalize_datetime(job.next_run_time),  # 标准化时区处理
                    job_state=job_state,
                    updated_at=datetime.now()
                )

                result = db.execute(query)
                if result.rowcount == 0:
                    raise JobLookupError(job.id)

                db.commit()

                logger.debug(f"作业已更新: {job.id}")

        except JobLookupError:
            raise
        except Exception as e:
            logger.error(f"更新作业失败: {job.id}, 错误: {str(e)}")
            raise

    def remove_job(self, job_id: str) -> None:
        """删除作业"""
        try:
            with self._get_db_session() as db:
                query = delete(ScheduleJob).where(ScheduleJob.id == job_id)
                result = db.execute(query)

                if result.rowcount == 0:
                    raise JobLookupError(job_id)

                db.commit()

                logger.info(f"作业已从数据库删除: {job_id}")

        except JobLookupError:
            raise
        except Exception as e:
            logger.error(f"删除作业失败: {job_id}, 错误: {str(e)}")
            raise

    def remove_all_jobs(self) -> None:
        """删除所有作业"""
        try:
            with self._get_db_session() as db:
                query = delete(ScheduleJob)
                result = db.execute(query)
                db.commit()

                logger.info(f"删除了 {result.rowcount} 个作业")

        except Exception as e:
            logger.error(f"删除所有作业失败: {str(e)}")
            raise

    def _serialize_trigger_args(self, trigger) -> Dict[str, Any]:
        """序列化触发器参数"""
        try:
            if hasattr(trigger, '__getstate__'):
                # 获取trigger的状态字典
                state_dict = trigger.__getstate__()
                # 清理状态字典中的ZoneInfo对象
                return self._clean_zoneinfo_from_dict(state_dict)
            else:
                # 基本触发器参数提取
                args = {}
                for attr in ['start_date', 'end_date', 'timezone']:
                    if hasattr(trigger, attr):
                        value = getattr(trigger, attr)
                        if value is not None:
                            if hasattr(value, 'isoformat'):
                                # 对于datetime对象，标准化后转换为ISO格式
                                normalized_value = self._normalize_datetime(value)
                                args[attr] = normalized_value.isoformat() if normalized_value else None
                            elif hasattr(value, 'key') and str(type(value).__name__) == 'ZoneInfo':
                                # 对于时区对象，使用key属性
                                args[attr] = str(value.key)
                            else:
                                args[attr] = str(value)

                # 特定触发器参数
                trigger_name = trigger.__class__.__name__.lower()
                if 'interval' in trigger_name:
                    for attr in ['seconds', 'minutes', 'hours', 'days', 'weeks']:
                        if hasattr(trigger, attr):
                            value = getattr(trigger, attr)
                            if value is not None:
                                args[attr] = value
                elif 'cron' in trigger_name:
                    for attr in ['year', 'month', 'day', 'week', 'day_of_week', 'hour', 'minute', 'second']:
                        if hasattr(trigger, attr):
                            value = getattr(trigger, attr)
                            if value is not None:
                                args[attr] = str(value)
                elif 'date' in trigger_name:
                    if hasattr(trigger, 'run_date'):
                        run_date = getattr(trigger, 'run_date')
                        if run_date:
                            # 标准化run_date的时区处理
                            normalized_run_date = self._normalize_datetime(run_date)
                            args['run_date'] = normalized_run_date.isoformat() if normalized_run_date else None

                return args
        except Exception as e:
            logger.warning(f"序列化触发器参数失败: {str(e)}")
            return {}
