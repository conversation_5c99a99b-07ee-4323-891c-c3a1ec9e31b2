"""
模型配置业务服务层
处理模型管理相关的业务逻辑
"""
import time
from typing import List, Tuple, Optional, Dict, Any, Type
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime

from app.services.base import BaseService
from app.repositories.model.model_config import ModelConfigRepository

from app.schemas.model.model_config import (
    ModelConfigCreate, ModelConfigUpdate, ModelConfigResponse, ModelHealthCheckResponse,
    ModelCallRequest, ModelCallResponse, ModelConfigQuery, ModelConfigPageResponse
)
from app.schemas.common import SearchParams
from app.models.model.model_config import ModelConfig
from app.core.exceptions import raise_business_error, raise_model_not_found
from app.core.error_codes import ErrorCode
from app.utils.clients.model_client import ModelClient
from app.utils.logger import setup_logger, operation_logger


logger = setup_logger()


class ModelConfigService(BaseService[ModelConfig, ModelConfigCreate, ModelConfigUpdate, ModelConfigResponse]):
    """模型配置业务服务"""

    def __init__(self, db: AsyncSession):
        repository = ModelConfigRepository(db)
        super().__init__(db, repository)

    @property
    def model_class(self) -> Type[ModelConfig]:
        """返回模型类"""
        return ModelConfig

    @property
    def response_schema_class(self) -> Type[ModelConfigResponse]:
        """返回响应Schema类"""
        return ModelConfigResponse

    # ==================== BaseService钩子方法重写 ====================

    async def _validate_before_create(self, create_data: ModelConfigCreate, **kwargs) -> None:
        """创建模型配置前的验证"""
        # 检查名称是否重复
        existing = await self.repository.get_by_name(create_data.name)
        if existing:
            raise_business_error(ErrorCode.MODEL_NAME_ALREADY_EXISTS, f"模型名称 '{create_data.name}' 已存在")

    async def _validate_before_update(self, model: ModelConfig, update_data: ModelConfigUpdate, **kwargs) -> None:
        """更新模型配置前的验证"""
        # 如果要更新名称，检查是否重复
        if update_data.name and update_data.name != model.name:
            existing = await self.repository.get_by_name(update_data.name)
            if existing:
                raise_business_error(ErrorCode.MODEL_NAME_ALREADY_EXISTS, f"模型名称 '{update_data.name}' 已存在")

    async def _build_list_filters(self, params: SearchParams) -> Dict[str, Any]:
        """构建模型列表查询过滤条件"""
        filters = {}
        if params.keyword:
            filters['keyword'] = params.keyword
        return filters

    # ==================== 重写基类钩子方法实现特殊业务逻辑 ====================

    async def _process_before_create(self, create_dict: Dict[str, Any]) -> Dict[str, Any]:
        """创建前处理"""
        # 移除模型中不存在的config字段
        if 'config' in create_dict:
            create_dict.pop('config')

        # 设置默认状态
        create_dict.update({
            'status': 'enabled',
            'health_status': 'unknown'
        })

        return create_dict

    # ==================== 模型配置特定的业务方法 ====================

    async def create_model_config(self,model_data: ModelConfigCreate,current_user: str) -> ModelConfigResponse:
        """创建模型配置 - 使用基类通用方法"""
        return await self.create(model_data, current_user)

    async def test_model_connection(
        self,
        model_data: ModelConfigCreate
    ) -> dict:
        """测试模型配置连接"""
        try:
            # 创建临时模型配置对象用于测试
            temp_config = ModelConfig(
                name=model_data.name,
                platform=model_data.platform,
                description=model_data.description,
                api_url=model_data.api_url,
                api_key=model_data.api_key,  # 直接使用明文API Key
                timeout_seconds=model_data.timeout_seconds,
                model_name=model_data.model_name,
                max_tokens=model_data.max_tokens,
                prompt=model_data.prompt,
                status='enabled',
                health_status='unknown'
            )

            # 创建客户端并测试连接（跳过网络连通性测试，直接测试API调用）
            client = ModelClient(temp_config, skip_network_test=True)

            # 执行健康检查
            test_result = await client.health_check()

            return {
                "success": test_result.get('is_healthy', False),
                "message": "连接测试成功" if test_result.get('is_healthy') else "连接测试失败",
                "details": test_result
            }

        except Exception as e:
            logger.error(f"模型连接测试失败: {str(e)}")
            return {
                "success": False,
                "message": f"连接测试失败: {str(e)}",
                "details": None
            }

    async def _process_before_update(self, obj: ModelConfig, update_dict: Dict[str, Any]) -> Dict[str, Any]:
        """更新前处理"""
        # API Key直接保存，无需加密
        return update_dict

    async def update_model_config(
        self,
        model_id: int,
        model_data: ModelConfigUpdate,
        current_user: str
    ) -> ModelConfigResponse:
        """更新模型配置 - 使用基类通用方法"""
        return await self.update(model_id, model_data, current_user)

    async def list_model_configs(self, query: 'ModelConfigQuery') -> 'ModelConfigPageResponse':
        """查询模型配置列表，支持关键词搜索、平台筛选和分页"""
        from app.schemas.model.model_config import ModelConfigPageResponse

        models, total = await self.repository.search_models_with_query(query)

        # 批量转换为响应格式（优化性能）
        model_responses = await self._batch_convert_to_response(models)

        # 计算总页数
        pages = (total + query.size - 1) // query.size

        return ModelConfigPageResponse(
            items=model_responses,
            total=total,
            page=query.page,
            size=query.size,
            pages=pages
        )

    async def get_model_config(self, model_id: int) -> ModelConfigResponse:
        """获取模型配置详情 - 使用基类通用方法"""
        return await self.get_by_id(model_id)

    async def delete_model_config(self, model_id: int) -> None:
        """删除模型配置 - 使用基类通用方法"""
        await self.delete(model_id)

    async def enable_model(self, model_id: int) -> ModelConfigResponse:
        """启用模型"""
        model = await self.repository.get(model_id)
        if not model:
            raise_model_not_found(model_id)

        model.enable()
        updated_model = await self.repository.update(db_obj=model, obj_in={})
        return self._convert_to_response(updated_model)

    async def disable_model(self, model_id: int) -> ModelConfigResponse:
        """停用模型"""
        model = await self.repository.get(model_id)
        if not model:
            raise_model_not_found(model_id)

        model.disable()
        updated_model = await self.repository.update(db_obj=model, obj_in={})
        return self._convert_to_response(updated_model)

    async def get_available_models(self) -> List[ModelConfigResponse]:
        """获取所有可用的模型配置"""
        models = await self.repository.get_available_models()
        return [self._convert_to_response(model) for model in models]

    async def get_platform_stats(self) -> Dict[str, Any]:
        """获取平台统计信息"""
        stats = await self.repository.get_platform_stats()
        
        # 计算总体统计
        total_models = sum(stat['total'] for stat in stats)
        total_enabled = sum(stat['enabled'] for stat in stats)
        total_healthy = sum(stat['healthy'] for stat in stats)
        
        return {
            'total_models': total_models,
            'total_enabled': total_enabled,
            'total_healthy': total_healthy,
            'platform_stats': stats
        }

    def _convert_to_response(self, model: ModelConfig) -> ModelConfigResponse:
        """转换模型为响应格式"""
        model_dict = {
            'id': model.id,
            'name': model.name,
            'platform': model.platform,
            'description': model.description,
            'api_url': model.api_url,
            'api_key': model.api_key,  # 将在Response中自动脱敏
            'timeout_seconds': model.timeout_seconds,
            'status': model.status,
            'health_status': model.health_status,
            'last_health_check': model.last_health_check,
            'model_name': model.model_name,
            'max_tokens': model.max_tokens,
            'prompt': model.prompt,
            'created_at': model.created_at,
            'updated_at': model.updated_at,
            'created_by': model.created_by,
            'updated_by': model.updated_by
        }

        return ModelConfigResponse(**model_dict)

    # ==================== 健康检查相关方法 ====================

    async def health_check_model(
        self,
        model_id: int,
        timeout_seconds: Optional[int] = None
    ) -> ModelHealthCheckResponse:
        """单个模型健康检查"""
        model = await self.repository.get(model_id)
        if not model:
            raise_model_not_found(model_id)

        # 创建客户端并执行健康检查
        client = ModelClient(model)

        check_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        try:
            if timeout_seconds:
                # 使用自定义超时
                original_timeout = client.timeout
                client.timeout = timeout_seconds

            result = await client.health_check()

            if timeout_seconds:
                # 恢复原始超时
                client.timeout = original_timeout

            # 更新模型健康状态
            health_status = 'healthy' if result['is_healthy'] else 'unhealthy'
            await self.repository.update_health_status(model_id, health_status, check_time)

            return ModelHealthCheckResponse(
                model_id=model_id,
                model_name=model.name,
                is_healthy=result['is_healthy'],
                response_time_ms=result.get('response_time_ms'),
                error_message=result.get('error_message'),
                check_time=check_time
            )

        except Exception as e:
            # 更新为不健康状态
            await self.repository.update_health_status(model_id, 'unhealthy', check_time)

            return ModelHealthCheckResponse(
                model_id=model_id,
                model_name=model.name,
                is_healthy=False,
                response_time_ms=None,
                error_message=str(e),
                check_time=check_time
            )

    async def batch_health_check(
        self,
        model_ids: Optional[List[int]] = None,
        timeout_seconds: Optional[int] = None
    ) -> List[ModelHealthCheckResponse]:
        """批量健康检查"""
        if model_ids:
            # 检查指定的模型
            models = []
            for model_id in model_ids:
                model = await self.repository.get(model_id)
                if model:
                    models.append(model)
        else:
            # 检查所有启用的模型
            models = await self.repository.get_enabled_models()

        results = []
        for model in models:
            try:
                result = await self.health_check_model(model.id, timeout_seconds)
                results.append(result)
            except Exception as e:
                logger.error(f"健康检查失败 - 模型ID: {model.id}, 错误: {str(e)}")
                results.append(ModelHealthCheckResponse(
                    model_id=model.id,
                    model_name=model.name,
                    is_healthy=False,
                    response_time_ms=None,
                    error_message=str(e),
                    check_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                ))

        return results

    # ==================== 模型调用相关方法 ====================

    async def call_model(
        self,
        request: ModelCallRequest,
        current_user_id: Optional[int] = None
    ) -> ModelCallResponse:
        """调用模型 - 带日志记录"""
        start_time = time.time()

        try:
            # 根据模型名称获取配置
            model = await self.repository.get_by_name(request.model_name)
            if not model:
                raise_business_error(ErrorCode.MODEL_NOT_FOUND, f"模型不存在: {request.model_name}")

            if not model.is_enabled:
                raise_business_error(ErrorCode.MODEL_DISABLED, f"模型已停用: {request.model_name}")

            # 创建客户端并调用模型
            client = ModelClient(model)

            try:
                result = await client.call_model(request.prompt, request.parameters)

                # 记录成功调用日志
                duration_ms = (time.time() - start_time) * 1000
                await operation_logger.log_operation(
                    operation="model_call",
                    user_id=current_user_id,
                    username=f"user_{current_user_id}" if current_user_id else "system",
                    request_data={
                        "model_name": request.model_name,
                        "prompt_length": len(request.prompt) if request.prompt else 0,
                        "parameters": request.parameters,
                        "response_time_ms": result.get('response_time_ms', 0),
                        "success": result.get('success', False)
                    },
                    result="success" if result.get('success', False) else "failed",
                    error_msg=result.get('error_message') if not result.get('success', False) else None,
                    duration_ms=duration_ms
                )

                return ModelCallResponse(
                    model_name=request.model_name,
                    response=result.get('response'),
                    response_time_ms=result.get('response_time_ms', 0),
                    success=result.get('success', False),
                    error_message=result.get('error_message')
                )

            except Exception as e:
                logger.error(f"模型调用失败 - 模型: {request.model_name}, 错误: {str(e)}")

                # 记录调用失败日志
                duration_ms = (time.time() - start_time) * 1000
                await operation_logger.log_operation(
                    operation="model_call",
                    user_id=current_user_id,
                    username=f"user_{current_user_id}" if current_user_id else "system",
                    request_data={
                        "model_name": request.model_name,
                        "prompt_length": len(request.prompt) if request.prompt else 0,
                        "parameters": request.parameters
                    },
                    result="failed",
                    error_msg=str(e),
                    duration_ms=duration_ms
                )

                return ModelCallResponse(
                    model_name=request.model_name,
                    response=None,
                    response_time_ms=0,
                    success=False,
                    error_message=str(e)
                )

            finally:
                await client.close()

        except Exception as e:
            # 记录系统级别错误日志
            duration_ms = (time.time() - start_time) * 1000
            await operation_logger.log_operation(
                operation="model_call",
                user_id=current_user_id,
                username=f"user_{current_user_id}" if current_user_id else "system",
                request_data={"model_name": request.model_name},
                result="failed",
                error_msg=str(e),
                duration_ms=duration_ms
            )
            raise

    async def call_model_stream(
        self,
        request: ModelCallRequest
    ):
        """调用模型（流式）"""
        logger.info(f"开始流式调用模型: {request.model_name}")
        logger.debug(f"请求参数: prompt='{request.prompt[:50]}...', parameters={request.parameters}")

        # 根据模型名称获取配置
        model = await self.repository.get_by_name(request.model_name)
        if not model:
            logger.error(f"模型不存在: {request.model_name}")
            raise_business_error(ErrorCode.MODEL_NOT_FOUND, f"模型不存在: {request.model_name}")

        logger.info(f"找到模型配置: ID={model.id}, 平台={model.platform}, API_URL={model.api_url}")

        if not model.is_enabled:
            logger.error(f"模型已停用: {request.model_name}")
            raise_business_error(ErrorCode.MODEL_DISABLED, f"模型已停用: {request.model_name}")

        # 创建客户端并调用模型
        logger.debug(f"创建ModelClient，跳过网络测试")
        client = ModelClient(model, skip_network_test=True)

        try:
            logger.info(f"开始流式生成，模型: {model.model_name}")
            chunk_count = 0
            async for chunk in client.call_model_stream(request.prompt, request.parameters):
                chunk_count += 1
                if chunk_count <= 3:  # 只记录前3个chunk的详细信息
                    logger.debug(f"收到chunk #{chunk_count}: {chunk}")
                elif chunk_count % 10 == 0:  # 每10个chunk记录一次进度
                    logger.debug(f"已处理 {chunk_count} 个chunks")
                yield chunk

            logger.info(f"流式生成完成，总共处理了 {chunk_count} 个chunks")

        except Exception as e:
            logger.error(f"流式调用模型失败: {str(e)}")
            logger.error(f"错误类型: {type(e).__name__}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            raise
        finally:
            await client.close()

    async def call_model_stream_response(self, request: ModelCallRequest):
        """调用模型（流式）- 返回格式化的SSE流"""
        import json

        logger.info(f"开始SSE流式调用模型: {request.model_name}")

        # 根据模型名称获取配置
        model = await self.repository.get_by_name(request.model_name)
        if not model:
            logger.error(f"SSE调用 - 模型不存在: {request.model_name}")
            raise_business_error(ErrorCode.MODEL_NOT_FOUND, f"模型不存在: {request.model_name}")

        logger.info(f"SSE调用 - 找到模型配置: ID={model.id}, 平台={model.platform}")

        if not model.is_enabled:
            logger.error(f"SSE调用 - 模型已停用: {request.model_name}")
            raise_business_error(ErrorCode.MODEL_DISABLED, f"模型已停用: {request.model_name}")

        # 创建客户端并调用模型
        client = ModelClient(model, skip_network_test=True)

        try:
            logger.info(f"SSE调用 - 开始流式生成")
            chunk_count = 0
            async for chunk in client.call_model_stream(request.prompt, request.parameters):
                chunk_count += 1
                # 将每个数据块转换为 SSE 格式
                sse_data = f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                # if chunk_count <= 3:  # 只记录前3个SSE数据的详细信息
                    # logger.debug(f"SSE chunk #{chunk_count}: {sse_data[:100]}...")
                yield sse_data

            # logger.info(f"SSE流式生成完成，总共发送了 {chunk_count} 个SSE数据块")

        except Exception as e:
            logger.error(f"SSE流式调用失败: {str(e)}")
            logger.error(f"SSE错误类型: {type(e).__name__}")
            # 发送错误信息
            error_chunk = {
                'type': 'error',
                'error_message': str(e)
            }
            error_sse = f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"
            logger.debug(f"发送SSE错误信息: {error_sse}")
            yield error_sse
        finally:
            # 发送结束信号
            # logger.debug("发送SSE结束信号")
            yield "data: [DONE]\n\n"
            # logger.debug("SSE调用 - 关闭ModelClient连接")
            await client.close()

    async def _batch_convert_to_response(self, models: List[ModelConfig]) -> List[ModelConfigResponse]:
        """批量转换模型配置对象为响应模型（优化性能，避免N+1查询）"""
        if not models:
            return []

        # 使用基类的通用方法获取用户信息映射
        try:
            user_map = await self._get_user_info_map(models)
        except AttributeError as e:
            # 如果方法不存在，使用临时的回退方案
            print(f"方法不存在错误: {e}")
            user_map = {}
            # 收集所有需要查询的用户ID
            user_ids = set()
            for model in models:
                if model.created_by and model.created_by.isdigit():
                    user_ids.add(int(model.created_by))
                if model.updated_by and model.updated_by.isdigit():
                    user_ids.add(int(model.updated_by))
            # 批量查询用户信息
            try:
                if user_ids:
                    users = await self.user_repository.get_by_ids(list(user_ids))
                    user_map = {user.id: user.nickname or user.username for user in users}
            except Exception:
                pass

        # 转换所有模型配置
        model_responses = []
        for model in models:
            # 使用基类通用方法获取用户名称（带回退方案）
            try:
                created_by_name = self._get_user_display_name(model.created_by, user_map)
                updated_by_name = self._get_user_display_name(model.updated_by, user_map)
            except AttributeError:
                # 回退方案
                created_by_name = model.created_by
                updated_by_name = model.updated_by
                if model.created_by and model.created_by.isdigit():
                    created_by_name = user_map.get(int(model.created_by), model.created_by)
                if model.updated_by and model.updated_by.isdigit():
                    updated_by_name = user_map.get(int(model.updated_by), model.updated_by)

            # 手动构建模型字典，确保包含正确的字段映射
            model_dict = {
                'id': model.id,
                'name': model.name,
                'platform': model.platform,
                'description': model.description,
                'api_url': model.api_url,
                'api_key': model.api_key,  # 将在Response中自动脱敏
                'timeout_seconds': model.timeout_seconds,
                'status': model.status,
                'health_status': model.health_status,
                'last_health_check': model.last_health_check,
                'model_name': model.model_name,
                'max_tokens': model.max_tokens,
                'prompt': model.prompt,
                'created_at': model.created_at,
                'updated_at': model.updated_at,
                'created_by': created_by_name,
                'updated_by': updated_by_name
            }

            # 使用字典创建响应对象，让ModelConfigResponse自动处理api_key脱敏
            response = ModelConfigResponse(**model_dict)
            model_responses.append(response)

        return model_responses
