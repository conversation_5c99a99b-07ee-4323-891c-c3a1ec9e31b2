<template>
  <ArtSearchBar
    :filter="searchForm"
    :items="searchItems"
    @search="handleSearch"
    @reset="handleReset"
  />
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed } from 'vue'
import ArtSearchBar from '@/components/core/forms/art-search-bar/index.vue'
import { DataFactoryService } from '@/api/dataFactoryApi'

interface Props {
  filter: {
    keyword: string
    category: string
    status: string
  }
}

interface Emits {
  (e: 'update:filter', value: any): void
  (e: 'search'): void
  (e: 'reset'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const categories = ref<string[]>([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: '',
  status: ''
})

// 搜索项配置
const searchItems = computed(() => [
  {
    prop: 'keyword',
    label: '关键词',
    type: 'input',
    placeholder: '搜索模型名称或描述',
    clearable: true
  },
  {
    prop: 'category',
    label: '分类',
    type: 'select',
    placeholder: '选择分类',
    clearable: true,
    options: categories.value.map(cat => ({ label: cat, value: cat }))
  },
  {
    prop: 'status',
    label: '状态',
    type: 'select',
    placeholder: '选择状态',
    clearable: true,
    options: [
      { label: '启用', value: '1' },
      { label: '禁用', value: '2' }
    ]
  }
])

// 监听props变化
watch(
  () => props.filter,
  (newFilter) => {
    Object.assign(searchForm, newFilter)
  },
  { immediate: true, deep: true }
)

// 监听表单变化
watch(
  searchForm,
  (newForm) => {
    emit('update:filter', { ...newForm })
  },
  { deep: true }
)

// 页面初始化
onMounted(() => {
  loadCategories()
})

// 加载分类
const loadCategories = async () => {
  try {
    const response = await DataFactoryService.getModelCategories()
    categories.value = response.data || []
  } catch (error) {
    console.error('加载分类失败:', error)
    categories.value = []
  }
}

// 事件处理
const handleSearch = () => {
  emit('search')
}

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    category: '',
    status: ''
  })
  emit('reset')
}
</script>

<style scoped lang="scss">
// 使用项目标准样式，无需额外样式
</style>
