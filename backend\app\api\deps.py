"""
API 依赖注入模块
包含认证、权限检查、数据库会话等依赖
"""
from typing import Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db
from app.core.security import verify_token, create_credentials_exception
from app.core.exceptions import raise_business_error
from app.core.error_codes import ErrorCode
from app.repositories.user.user import UserRepository
from app.models.user.user import User

# 创建安全依赖
security = HTTPBearer()


async def get_current_user(
    db: AsyncSession = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """获取当前用户依赖 """
    # 提取token（去除Bearer前缀）
    token = credentials.credentials
    if token.startswith("Bearer "):
        token = token[7:]
    
    # 验证token
    payload = verify_token(token)
    if payload is None:
        raise create_credentials_exception("无效的认证令牌")
    
    username: str = payload.get("sub")
    if username is None:
        raise create_credentials_exception("无效的认证令牌")
    
    # 获取用户
    user_repo = UserRepository(db)
    user = await user_repo.get_by_username(username)
    if user is None:
        raise create_credentials_exception("用户不存在")
    
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    获取当前活跃用户依赖
    """
    if not current_user.is_active:
        raise_business_error(ErrorCode.USER_ACCOUNT_DISABLED)
    
    return current_user


async def get_current_superuser(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    获取当前超级用户依赖
    """
    if not current_user.is_superuser:
        raise_business_error(ErrorCode.PERMISSION_DENIED, "需要超级用户权限")
    
    return current_user


    """
    角色检查装饰器工厂
    """
    async def role_checker(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        """
        角色检查依赖
        """
        # 超级用户拥有所有角色
        if current_user.is_superuser:
            return current_user
        
        # 检查用户是否具有所需角色
        user_roles = current_user.role_names
        for role in required_roles:
            if role not in user_roles:
                raise_business_error(ErrorCode.PERMISSION_DENIED, f"缺少角色: {role}")
        
        return current_user
    
    return role_checker 