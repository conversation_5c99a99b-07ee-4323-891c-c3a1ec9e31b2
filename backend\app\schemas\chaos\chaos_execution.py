"""
混沌测试执行记录数据传输对象
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator

from app.schemas.base import BaseCreateSchema, BaseUpdateSchema, BaseResponseSchema, BaseQuery, BasePageResponse


class ChaosExecutionCreate(BaseCreateSchema):
    """
    创建执行记录请求模式
    """
    task_id: int = Field(..., gt=0, description="任务ID")
    host_id: int = Field(..., gt=0, description="执行主机ID")
    host_info: Optional[Dict[str, Any]] = Field(None, description="主机信息快照")
    fault_config: Optional[Dict[str, Any]] = Field(None, description="故障配置快照")


class ChaosExecutionUpdate(BaseUpdateSchema):
    """
    更新执行记录请求模式
    """
    status: Optional[str] = Field(None, description="执行状态")
    chaos_uid: Optional[str] = Field(None, description="ChaosBlade执行UID")
    command: Optional[str] = Field(None, description="执行的ChaosBlade命令")
    blade_version: Optional[str] = Field(None, description="ChaosBlade版本")
    output: Optional[str] = Field(None, description="执行输出")
    error_message: Optional[str] = Field(None, description="错误信息")
    exit_code: Optional[int] = Field(None, description="命令退出码")
    duration_seconds: Optional[int] = Field(None, description="执行时长(秒)")
    is_auto_destroyed: Optional[bool] = Field(None, description="是否已自动销毁")
    destroy_output: Optional[str] = Field(None, description="销毁命令输出")

    @validator('status')
    def validate_status(cls, v):
        """验证执行状态"""
        if v is not None:
            allowed_statuses = ['pending', 'running', 'success', 'failed', 'cancelled']
            if v not in allowed_statuses:
                raise ValueError(f'执行状态必须为: {", ".join(allowed_statuses)}')
        return v


class ChaosExecutionResponse(BaseResponseSchema):
    """
    执行记录响应模式
    """
    task_id: int = Field(description="任务ID")
    host_id: int = Field(description="执行主机ID")
    start_time: Optional[datetime] = Field(description="开始时间")
    end_time: Optional[datetime] = Field(description="结束时间")
    status: str = Field(description="执行状态")
    chaos_uid: Optional[str] = Field(description="ChaosBlade执行UID")
    command: Optional[str] = Field(description="执行的ChaosBlade命令")
    blade_version: Optional[str] = Field(description="ChaosBlade版本")
    output: Optional[str] = Field(description="执行输出")
    error_message: Optional[str] = Field(description="错误信息")
    exit_code: Optional[int] = Field(description="命令退出码")
    fault_config: Optional[Dict[str, Any]] = Field(description="故障配置快照")
    duration_seconds: Optional[int] = Field(description="执行时长(秒)")
    retry_count: int = Field(description="重试次数")
    is_auto_destroyed: bool = Field(description="是否已自动销毁")
    destroy_time: Optional[datetime] = Field(description="销毁时间")
    destroy_output: Optional[str] = Field(description="销毁命令输出")
    
    # 关联信息
    task_name: Optional[str] = Field(None, description="任务名称")
    host_name: Optional[str] = Field(None, description="主机名称")
    environment_name: Optional[str] = Field(None, description="环境名称")

    # 状态信息
    is_running: bool = Field(description="是否正在运行")
    is_completed: bool = Field(description="是否已完成")
    is_successful: bool = Field(description="是否成功")
    has_chaos_uid: bool = Field(description="是否有ChaosBlade UID")


class ChaosExecutionListResponse(BaseModel):
    """
    执行记录列表项响应模式
    """
    id: int = Field(description="执行记录ID")
    task_id: int = Field(description="任务ID")
    task_name: Optional[str] = Field(description="任务名称")
    host_id: int = Field(description="执行主机ID")
    host_name: Optional[str] = Field(description="主机名称")
    status: str = Field(description="执行状态")
    chaos_uid: Optional[str] = Field(description="ChaosBlade执行UID")
    start_time: Optional[datetime] = Field(description="开始时间")
    end_time: Optional[datetime] = Field(description="结束时间")
    duration_seconds: Optional[int] = Field(description="执行时长(秒)")
    retry_count: int = Field(description="重试次数")
    is_auto_destroyed: bool = Field(description="是否已自动销毁")
    exit_code: Optional[int] = Field(description="退出码")
    has_error: bool = Field(description="是否有错误")
    created_at: Optional[datetime] = Field(description="创建时间")

    class Config:
        from_attributes = True


class ChaosExecutionQuery(BaseQuery):
    """混沌测试执行记录查询参数模型"""
    task_id: Optional[int] = Field(None, description="任务ID")
    task_name: Optional[str] = Field(None, description="任务名称")
    status: Optional[str] = Field(None, description="执行状态")

    # 排序参数
    order_by: str = Field(default="created_at", description="排序字段")
    desc: bool = Field(default=True, description="是否降序")


class ChaosExecutionPageResponse(BasePageResponse[ChaosExecutionListResponse]):
    """混沌测试执行记录列表分页响应模型"""
    pass



class ChaosExecutionDetailResponse(ChaosExecutionResponse):
    """
    执行记录详情响应模式（包含完整信息）
    """
    pass


class ChaosExecutionStatistics(BaseModel):
    """
    执行记录统计信息
    """
    total_count: int = Field(description="总执行数")
    status_stats: Dict[str, int] = Field(description="状态统计")
    avg_duration_seconds: float = Field(description="平均执行时长(秒)")
    success_rate: float = Field(description="成功率")
    recent_executions: List[ChaosExecutionListResponse] = Field(description="最近执行记录")


class ChaosExecutionLogRequest(BaseModel):
    """
    执行日志请求
    """
    execution_id: int = Field(..., gt=0, description="执行记录ID")
    log_type: str = Field(default="output", description="日志类型")
    
    @validator('log_type')
    def validate_log_type(cls, v):
        """验证日志类型"""
        allowed_types = ['output', 'error', 'command', 'destroy']
        if v not in allowed_types:
            raise ValueError(f'日志类型必须为: {", ".join(allowed_types)}')
        return v


class ChaosExecutionLogResponse(BaseModel):
    """
    执行日志响应
    """
    execution_id: int = Field(description="执行记录ID")
    log_type: str = Field(description="日志类型")
    content: str = Field(description="日志内容")
    timestamp: datetime = Field(description="时间戳")


class ChaosExecutionRetryRequest(BaseModel):
    """
    重试执行请求
    """
    execution_id: Optional[int] = Field(None, gt=0, description="执行记录ID")
    override_params: Optional[Dict[str, Any]] = Field(None, description="覆盖参数")


class ChaosExecutionBatchRequest(BaseModel):
    """
    批量操作执行记录请求
    """
    execution_ids: List[int] = Field(..., min_items=1, description="执行记录ID列表")
    action: str = Field(..., description="操作类型")
    
    @validator('action')
    def validate_action(cls, v):
        """验证操作类型"""
        allowed_actions = ['cancel', 'destroy', 'retry', 'delete']
        if v not in allowed_actions:
            raise ValueError(f'操作类型必须为: {", ".join(allowed_actions)}')
        return v


class ChaosExecutionMonitorResponse(BaseModel):
    """
    执行监控响应
    """
    execution_id: int = Field(description="执行记录ID")
    status: str = Field(description="当前状态")
    progress: float = Field(description="执行进度(0-100)")
    current_step: str = Field(description="当前步骤")
    elapsed_seconds: int = Field(description="已执行时长(秒)")
    estimated_remaining_seconds: Optional[int] = Field(description="预计剩余时长(秒)")
    last_update_time: datetime = Field(description="最后更新时间")
    metrics: Optional[Dict[str, Any]] = Field(description="实时指标数据")
