import axios, { InternalAxiosRequestConfig, AxiosRequestConfig, AxiosResponse } from 'axios'
import { useUserStore } from '@/store/modules/user'
import { ApiStatus } from './status'
import { HttpError, handleError, showError } from './error'
import { $t } from '@/locales'
import type { ErrorResponse, RestfulResponse, RestfulPaginationResponse } from '@/types/api/common'

// 常量定义
const REQUEST_TIMEOUT = 15000 // 请求超时时间(毫秒)
const LOGOUT_DELAY = 1000 // 退出登录延迟时间(毫秒)
const MAX_RETRIES = 2 // 最大重试次数
const RETRY_DELAY = 1000 // 重试延迟时间(毫秒)

// 扩展 AxiosRequestConfig 类型
interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
  showErrorMessage?: boolean
  silentError?: boolean  // 静默错误，不显示任何错误消息
  useRestfulFormat?: boolean  // 是否使用RESTful格式响应
}

const { VITE_API_URL, VITE_WITH_CREDENTIALS } = import.meta.env

const axiosInstance = axios.create({
  timeout: REQUEST_TIMEOUT, // 请求超时时间(毫秒)
  baseURL: VITE_API_URL, // API地址
  withCredentials: VITE_WITH_CREDENTIALS === 'true', // 是否携带cookie，默认关闭
  transformRequest: [
    // 智能处理不同数据类型：FormData 保持原样，其他数据转换为 JSON
    (data, headers) => {
      // 如果是 FormData，直接返回不做任何处理
      if (data instanceof FormData) {
        return data
      }
      // 如果是字符串、数字、null、undefined，直接返回
      if (typeof data === 'string' || typeof data === 'number' || data == null) {
        return data
      }
      // 其他对象类型转换为 JSON 字符串
      return JSON.stringify(data)
    }
  ],
  validateStatus: (status) => status >= 200 && status < 300, // 只接受 2xx 的状态码
  headers: {
    get: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' },
    post: { 'Content-Type': 'application/json;charset=utf-8' }
  },
  transformResponse: [
    (data, headers) => {
      const contentType = headers['content-type']
      if (contentType && contentType.includes('application/json')) {
        try {
          return JSON.parse(data)
        } catch {
          return data
        }
      }
      return data
    }
  ]
})

// 请求拦截器
axiosInstance.interceptors.request.use(
  (request: InternalAxiosRequestConfig) => {
    const { accessToken } = useUserStore()

    // 设置 token
    if (accessToken) {
      request.headers.set('Authorization', `Bearer ${accessToken}`)
    }

    // 根据数据类型设置正确的 Content-Type
    if (request.data instanceof FormData) {
      // FormData 请求：删除 Content-Type，让浏览器自动设置 multipart/form-data 和边界
      request.headers.delete('Content-Type')
    } else if (request.data && typeof request.data === 'object') {
      // JSON 数据请求：设置为 application/json
      request.headers.set('Content-Type', 'application/json')
    }

    return request
  },
  (error) => {
    showError(new HttpError($t('httpMsg.requestConfigError'), ApiStatus.error))
    return Promise.reject(error)
  }
)

// 请求重试状态管理
let isRefreshing = false
let failedRequestsQueue: Array<{
  resolve: (token: string) => void
  reject: (error: any) => void
}> = []

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    const responseData = response.data
    const config = response.config as ExtendedAxiosRequestConfig

    // 检查是否为错误响应格式
    if (responseData && typeof responseData === 'object' && 'error_code' in responseData) {
      // 新格式错误响应，抛出错误让错误拦截器处理
      const error = new Error('Business Error')
      error.response = response
      throw error
    }

    // 检查旧格式响应
    if (responseData && typeof responseData === 'object' && 'success' in responseData) {
      const success = responseData

      // 对于标准业务响应，直接返回（成功状态检查在request函数中处理）
      if (success !== undefined) {
        return response
      }
    }

    // RESTful格式响应，直接返回
    return response
  },
  async (error) => {
    const originalRequest = error.config

    // 处理401错误且不是登录接口
    if (error.response?.status === 401 && !originalRequest._retry && !originalRequest.url?.includes('/auth/login')) {
      return handleTokenRefresh(originalRequest)
    }

    return Promise.reject(handleError(error))
  }
)

// 处理token刷新逻辑
async function handleTokenRefresh(originalRequest: any): Promise<any> {
  const userStore = useUserStore()
  
  if (isRefreshing) {
    // 如果正在刷新，将请求放入队列
    return new Promise((resolve, reject) => {
      failedRequestsQueue.push({ resolve, reject })
    }).then((token) => {
      originalRequest.headers['Authorization'] = `Bearer ${token}`
      return axiosInstance(originalRequest)
    })
  }

  originalRequest._retry = true
  isRefreshing = true

  try {
    // 尝试使用refresh token获取新的access token
    const { refreshToken } = userStore
    
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await axiosInstance.post('/api/auth/refresh-token', {
      refresh_token: refreshToken
    })

    const { token, refreshToken: newRefreshToken } = response.data
    
    // 更新tokens
    userStore.setToken(token, newRefreshToken)

    // 处理队列中的请求
    failedRequestsQueue.forEach(({ resolve }) => {
      resolve(token)
    })
    failedRequestsQueue = []

    // 重试原始请求
    originalRequest.headers['Authorization'] = `Bearer ${token}`
    return axiosInstance(originalRequest)

  } catch (refreshError) {
    // 刷新失败，清空队列并登出
    failedRequestsQueue.forEach(({ reject }) => {
      reject(refreshError)
    })
    failedRequestsQueue = []
    
    logOut()
    throw new HttpError($t('httpMsg.unauthorized'), ApiStatus.unauthorized)
  } finally {
    isRefreshing = false
  }
}

// 请求重试函数
async function retryRequest<T>(
  config: ExtendedAxiosRequestConfig,
  retries: number = MAX_RETRIES
): Promise<T> {
  try {
    return await request<T>(config)
  } catch (error) {
    if (retries > 0 && error instanceof HttpError && shouldRetry(error.code)) {
      await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY))
      return retryRequest<T>(config, retries - 1)
    }
    throw error
  }
}

// 判断是否需要重试
function shouldRetry(statusCode: number): boolean {
  return [
    ApiStatus.requestTimeout,
    ApiStatus.internalServerError,
    ApiStatus.badGateway,
    ApiStatus.serviceUnavailable,
    ApiStatus.gatewayTimeout
  ].includes(statusCode)
}

// 请求函数 (支持RESTful和旧格式)
async function request<T = any>(config: ExtendedAxiosRequestConfig): Promise<T> {
  // 对 POST | PUT 请求特殊处理
  if (config.method?.toUpperCase() === 'POST' || config.method?.toUpperCase() === 'PUT') {
    if (config.params && !config.data) {
      config.data = config.params
      config.params = undefined
    }
  }

  try {
    const res = await axiosInstance.request(config)
    const responseData = res.data

    // 处理RESTful格式响应
    if (config.useRestfulFormat || !responseData || typeof responseData !== 'object' || !('success' in responseData)) {
      // 直接返回响应数据 (RESTful格式)
      return responseData as T
    }

    // 处理旧格式响应 (向后兼容)
    const { success, data, message, code } = responseData

    // 检查业务层面的成功状态
    if (success === false) {
      // 业务失败，抛出业务异常
      const businessError = new HttpError(
        message || '业务操作失败',
        code || 400
      )
      throw businessError
    }

    return data as T
  } catch (error) {
    if (error instanceof HttpError) {
      // 根据配置决定是否显示错误消息
      const shouldShowError = !config.silentError && config.showErrorMessage !== false
      showError(error, shouldShowError)
    }
    return Promise.reject(error)
  }
}

// API 方法集合
const api = {
  get<T>(config: ExtendedAxiosRequestConfig): Promise<T> {
    return retryRequest<T>({ ...config, method: 'GET', useRestfulFormat: true })
  },
  post<T>(config: ExtendedAxiosRequestConfig): Promise<T> {
    return retryRequest<T>({ ...config, method: 'POST', useRestfulFormat: true })
  },
  put<T>(config: ExtendedAxiosRequestConfig): Promise<T> {
    return retryRequest<T>({ ...config, method: 'PUT', useRestfulFormat: true })
  },
  del<T>(config: ExtendedAxiosRequestConfig): Promise<T> {
    return retryRequest<T>({ ...config, method: 'DELETE', useRestfulFormat: true })
  },
  delete<T>(config: ExtendedAxiosRequestConfig): Promise<T> {
    return retryRequest<T>({ ...config, method: 'DELETE', useRestfulFormat: true })
  },
  patch<T>(config: ExtendedAxiosRequestConfig): Promise<T> {
    return retryRequest<T>({ ...config, method: 'PATCH', useRestfulFormat: true })
  }
}

// 退出登录函数
const logOut = (): void => {
  setTimeout(() => {
    useUserStore().logOut()
  }, LOGOUT_DELAY)
}

export default api
