#!/usr/bin/env python3
"""
配置管理脚本
用于管理应用配置文件
"""
import os
import argparse
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent

def check_env_file():
    """检查配置文件状态"""
    print("📋 配置文件状态:")
    print("-" * 40)

    # 检查.env文件
    env_file = PROJECT_ROOT / ".env"
    status = "✅ 存在" if env_file.exists() else "❌ 不存在"
    print(f"  配置文件: .env -> {status}")

    if env_file.exists():
        print(f"  文件路径: {env_file}")
        # 显示文件大小
        size = env_file.stat().st_size
        print(f"  文件大小: {size} 字节")

def show_config_info():
    """显示配置信息"""
    env_file = PROJECT_ROOT / ".env"

    print(f"� 配置文件: .env")

    if env_file.exists():
        print(f"✅ 配置文件存在: {env_file}")

        # 读取并显示主要配置项（不显示敏感信息）
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            print("\n🔧 主要配置项:")
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    if '=' in line:
                        key, value = line.split('=', 1)
                        # 隐藏敏感信息
                        if any(sensitive in key.upper() for sensitive in ['PASSWORD', 'SECRET', 'KEY', 'TOKEN']):
                            value = '*' * 8
                        elif 'DATABASE_URL' in key:
                            # 隐藏数据库密码
                            if '@' in value:
                                parts = value.split('@')
                                if ':' in parts[0]:
                                    user_pass = parts[0].split(':')
                                    if len(user_pass) > 2:
                                        user_pass[-1] = '***'
                                        parts[0] = ':'.join(user_pass)
                                value = '@'.join(parts)
                        print(f"    {key}: {value}")
        except Exception as e:
            print(f"⚠️  读取配置文件失败: {e}")
    else:
        print(f"❌ 配置文件不存在: {env_file}")

def validate_config():
    """验证配置文件"""
    env_file = PROJECT_ROOT / ".env"

    if not env_file.exists():
        print(f"❌ 配置文件不存在: {env_file}")
        return False

    print(f"✅ 配置文件验证通过")
    return True

def create_env_template():
    """创建.env配置模板"""
    env_file = PROJECT_ROOT / ".env"

    if env_file.exists():
        print(f"⚠️  配置文件已存在: {env_file}")
        return False

    # 基础模板
    template = """# DpTestPlatform 环境配置

# 应用配置
APP_NAME=DpTestPlatform API
APP_VERSION=1.0.0
DEBUG=true
HOST=0.0.0.0
PORT=8000

# 数据库配置 - 请修改为实际的数据库连接
DATABASE_URL=mysql+aiomysql://username:password@localhost:3306/dp_test?charset=UTF8MB4

# 数据库连接池配置
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# JWT 配置 - 请修改为安全的密钥
SECRET_KEY=change-this-to-a-secure-secret-key-at-least-32-characters
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS 配置
CORS_ORIGINS=["http://localhost:3000","http://localhost:5173","http://127.0.0.1:3000","http://127.0.0.1:5173"]

# 时区配置
TIMEZONE=Asia/Shanghai

# 日志配置
LOG_LEVEL=INFO

# Redis 配置 (可选)
REDIS_URL=redis://localhost:6379/0

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads/

# 分页配置
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100
"""

    try:
        env_file.write_text(template, encoding='utf-8')
        print(f"✅ 创建配置文件模板: {env_file}")
        print(f"⚠️  请根据实际情况修改配置值")
        return True
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="DpTestPlatform 配置管理工具")
    parser.add_argument("command", choices=["check", "info", "validate", "create"],
                       help="执行的命令")

    args = parser.parse_args()

    print("🚀 DpTestPlatform 配置管理工具")
    print("=" * 50)

    if args.command == "check":
        check_env_file()
    elif args.command == "info":
        show_config_info()
    elif args.command == "validate":
        validate_config()
    elif args.command == "create":
        create_env_template()

    print("\n" + "=" * 50)
    print("💡 使用提示:")
    print("  检查配置: python scripts/env_manager.py check")
    print("  查看配置: python scripts/env_manager.py info")
    print("  验证配置: python scripts/env_manager.py validate")
    print("  创建模板: python scripts/env_manager.py create")
    print("  启动应用: python -m uvicorn app.main:app --reload")

if __name__ == "__main__":
    main()
