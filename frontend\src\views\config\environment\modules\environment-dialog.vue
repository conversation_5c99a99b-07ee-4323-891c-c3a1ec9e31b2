<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    align-center
    :close-on-click-modal="false"
    @closed="handleDialogClosed"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="right"
      v-loading="isLoading"
      element-loading-text="处理中..."
    >
      <!-- 基础信息 -->
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="环境名称" prop="name">
            <ElInput v-model="formData.name" placeholder="请输入环境名称" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="环境类型" prop="type">
            <ElSelect
              v-model="formData.type"
              placeholder="请选择环境类型"
              style="width: 100%"
              :loading="typeLoading"
              @change="handleTypeChange"
            >
              <ElOption
                v-for="type in supportedTypes"
                :key="type.type"
                :label="type.name"
                :value="type.type"
              >
                <div>
                  <div>{{ type.name }}</div>
                  <div style="font-size: 12px; color: #999">{{ type.description }}</div>
                </div>
              </ElOption>
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="主机地址" prop="host">
            <ElInput v-model="formData.host" placeholder="请输入主机地址" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="端口" prop="port">
            <ElInputNumber
              v-model="formData.port"
              :min="1"
              :max="65535"
              placeholder="端口号"
              style="width: 100%"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElFormItem label="描述">
        <ElInput
          v-model="formData.description"
          type="textarea"
          :rows="2"
          placeholder="请输入环境描述"
        />
      </ElFormItem>

      <ElFormItem label="标签">
        <ElInput
          v-model="formData.tags"
          placeholder="请输入标签，多个标签用逗号分隔"
        />
      </ElFormItem>

      <!-- 动态配置区域 -->
      <ElFormItem label="详细配置">
        <div class="config-container">
          <!-- 数据库配置 -->
          <template v-if="formData.type === 'database'">
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="数据库类型" prop="config.db_type">
                  <ElSelect v-model="formData.config.db_type" placeholder="请选择">
                    <ElOption label="MySQL" value="mysql" />
                    <ElOption label="PostgreSQL" value="postgresql" />
                    <ElOption label="SQLite" value="sqlite" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="数据库名" prop="config.database_name">
                  <ElAutocomplete
                    v-model="formData.config.database_name"
                    :fetch-suggestions="getDatabaseNameSuggestions"
                    placeholder="请输入数据库名"
                    style="width: 100%"
                    clearable
                    @select="handleDatabaseNameSelect"
                  >
                    <template #default="{ item }">
                      <div class="suggestion-item">
                        <div class="suggestion-name">{{ item.value }}</div>
                        <div class="suggestion-desc">{{ item.description }}</div>
                      </div>
                    </template>
                  </ElAutocomplete>
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="用户名" prop="config.username">
                  <ElInput v-model="formData.config.username" placeholder="用户名" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="密码">
                  <ElInput
                    v-model="formData.config.password"
                    type="password"
                    placeholder="密码"
                    show-password
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </template>

          <!-- Redis配置 -->
          <template v-else-if="formData.type === 'redis'">
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="密码">
                  <ElInput
                    v-model="formData.config.password"
                    type="password"
                    placeholder="Redis密码（可选）"
                    show-password
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="数据库">
                  <ElInputNumber
                    v-model="formData.config.db"
                    :min="0"
                    :max="15"
                    placeholder="数据库编号"
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </template>

          <!-- SSH配置 -->
          <template v-else-if="formData.type === 'ssh'">
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="用户名" prop="config.username">
                  <ElInput v-model="formData.config.username" placeholder="SSH用户名" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="认证方式">
                  <ElRadioGroup v-model="authMethod">
                    <ElRadio value="password">密码</ElRadio>
                    <ElRadio value="key">私钥</ElRadio>
                  </ElRadioGroup>
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElFormItem v-if="authMethod === 'password'" label="密码">
              <ElInput
                v-model="formData.config.password"
                type="password"
                placeholder="SSH密码"
                show-password
              />
            </ElFormItem>
            <ElFormItem v-else label="私钥路径">
              <ElInput
                v-model="formData.config.private_key"
                placeholder="私钥文件路径"
              />
            </ElFormItem>
          </template>

          <!-- Kubernetes配置 -->
          <template v-else-if="formData.type === 'k8s'">
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="认证方式">
                  <ElRadioGroup v-model="k8sAuthMethod">
                    <ElRadio value="kubeconfig">Kubeconfig文件</ElRadio>
                    <ElRadio value="token">Token认证</ElRadio>
                  </ElRadioGroup>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="上下文" v-if="k8sAuthMethod === 'kubeconfig'">
                  <ElInput v-model="formData.config.context" placeholder="K8s上下文名称（可选）" />
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElFormItem v-if="k8sAuthMethod === 'kubeconfig'" label="Kubeconfig路径">
              <ElInput v-model="formData.config.kubeconfig_path" placeholder="Kubeconfig文件路径" />
            </ElFormItem>
            <template v-else>
              <ElFormItem label="API服务器">
                <ElInput v-model="formData.config.host" placeholder="https://k8s-api-server:6443" />
              </ElFormItem>
              <ElFormItem label="访问令牌">
                <ElInput
                  v-model="formData.config.token"
                  type="password"
                  placeholder="Bearer Token"
                  show-password
                />
              </ElFormItem>
            </template>
          </template>

          <!-- API配置 -->
          <template v-else-if="formData.type === 'api'">
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="请求方法">
                  <ElSelect v-model="formData.config.method" placeholder="请求方法">
                    <ElOption label="GET" value="GET" />
                    <ElOption label="POST" value="POST" />
                    <ElOption label="PUT" value="PUT" />
                    <ElOption label="HEAD" value="HEAD" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="认证类型">
                  <ElSelect v-model="apiAuthType" placeholder="认证类型">
                    <ElOption label="无认证" value="none" />
                    <ElOption label="Basic认证" value="basic" />
                    <ElOption label="Bearer Token" value="bearer" />
                    <ElOption label="API Key" value="api_key" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
            </ElRow>
            <template v-if="apiAuthType === 'basic'">
              <ElRow :gutter="20">
                <ElCol :span="12">
                  <ElFormItem label="用户名">
                    <ElInput v-model="formData.config.username" placeholder="用户名" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem label="密码">
                    <ElInput
                      v-model="formData.config.password"
                      type="password"
                      placeholder="密码"
                      show-password
                    />
                  </ElFormItem>
                </ElCol>
              </ElRow>
            </template>
            <template v-else-if="apiAuthType === 'bearer'">
              <ElFormItem label="Bearer Token">
                <ElInput
                  v-model="formData.config.token"
                  type="password"
                  placeholder="Bearer Token"
                  show-password
                />
              </ElFormItem>
            </template>
            <template v-else-if="apiAuthType === 'api_key'">
              <ElRow :gutter="20">
                <ElCol :span="12">
                  <ElFormItem label="API Key">
                    <ElInput
                      v-model="formData.config.api_key"
                      type="password"
                      placeholder="API Key"
                      show-password
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem label="Header名称">
                    <ElInput v-model="formData.config.api_key_header" placeholder="X-API-Key" />
                  </ElFormItem>
                </ElCol>
              </ElRow>
            </template>
          </template>

          <!-- Kafka配置 -->
          <template v-else-if="formData.type === 'kafka'">
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="Bootstrap服务器">
                  <ElInput v-model="formData.config.bootstrap_servers" placeholder="localhost:9092" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="安全协议">
                  <ElSelect v-model="formData.config.security_protocol" placeholder="安全协议">
                    <ElOption label="PLAINTEXT" value="PLAINTEXT" />
                    <ElOption label="SSL" value="SSL" />
                    <ElOption label="SASL_PLAINTEXT" value="SASL_PLAINTEXT" />
                    <ElOption label="SASL_SSL" value="SASL_SSL" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
            </ElRow>
            <template v-if="formData.config.security_protocol && formData.config.security_protocol.includes('SASL')">
              <ElRow :gutter="20">
                <ElCol :span="12">
                  <ElFormItem label="SASL用户名">
                    <ElInput v-model="formData.config.sasl_plain_username" placeholder="SASL用户名" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem label="SASL密码">
                    <ElInput
                      v-model="formData.config.sasl_plain_password"
                      type="password"
                      placeholder="SASL密码"
                      show-password
                    />
                  </ElFormItem>
                </ElCol>
              </ElRow>
            </template>
          </template>

          <!-- MongoDB配置 -->
          <template v-else-if="formData.type === 'mongodb'">
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="数据库名">
                  <ElAutocomplete
                    v-model="formData.config.database"
                    :fetch-suggestions="getMongoDbNameSuggestions"
                    placeholder="请输入数据库名"
                    style="width: 100%"
                    clearable
                    @select="handleMongoDbNameSelect"
                  >
                    <template #default="{ item }">
                      <div class="suggestion-item">
                        <div class="suggestion-name">{{ item.value }}</div>
                        <div class="suggestion-desc">{{ item.description }}</div>
                      </div>
                    </template>
                  </ElAutocomplete>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="认证数据库">
                  <ElInput v-model="formData.config.auth_source" placeholder="admin" />
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="用户名">
                  <ElInput v-model="formData.config.username" placeholder="用户名（可选）" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="密码">
                  <ElInput
                    v-model="formData.config.password"
                    type="password"
                    placeholder="密码（可选）"
                    show-password
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElFormItem label="副本集">
              <ElInput v-model="formData.config.replica_set" placeholder="副本集名称（可选）" />
            </ElFormItem>
          </template>

          <!-- Elasticsearch配置 -->
          <template v-else-if="formData.type === 'elasticsearch' || formData.type === 'opensearch'">
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="协议">
                  <ElSelect v-model="formData.config.scheme" placeholder="协议">
                    <ElOption label="HTTP" value="http" />
                    <ElOption label="HTTPS" value="https" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="认证类型">
                  <ElSelect v-model="esAuthType" placeholder="认证类型">
                    <ElOption label="无认证" value="none" />
                    <ElOption label="Basic认证" value="basic" />
                    <ElOption label="API Key" value="api_key" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
            </ElRow>
            <template v-if="esAuthType === 'basic'">
              <ElRow :gutter="20">
                <ElCol :span="12">
                  <ElFormItem label="用户名">
                    <ElInput v-model="formData.config.username" placeholder="用户名" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem label="密码">
                    <ElInput
                      v-model="formData.config.password"
                      type="password"
                      placeholder="密码"
                      show-password
                    />
                  </ElFormItem>
                </ElCol>
              </ElRow>
            </template>
            <template v-else-if="esAuthType === 'api_key'">
              <ElFormItem label="API Key">
                <ElInput
                  v-model="formData.config.api_key"
                  type="password"
                  placeholder="API Key"
                  show-password
                />
              </ElFormItem>
            </template>
          </template>

          <!-- 其他类型的提示 -->
          <template v-else>
            <ElAlert
              title="该环境类型的详细配置功能正在开发中"
              type="info"
              show-icon
              :closable="false"
            />
          </template>
        </div>
      </ElFormItem>
    </ElForm>

    <template #footer>
      <div class="dialog-footer">
        <div class="test-section">
          <ElButton
            @click="handleTestConnection"
            :loading="testLoading"
            :disabled="!canTest"
            type="warning"
            icon="Connection"
          >
            {{ testLoading ? '测试中...' : '测试连接' }}
          </ElButton>
          <span v-if="testResult" class="test-result" :class="testResult.success ? 'success' : 'error'">
            {{ testResult.message }}
            <span v-if="testResult.success && testResult.duration" class="duration">
              ({{ testResult.duration.toFixed(2) }}s)
            </span>
          </span>
        </div>
        <div class="action-buttons">
          <ElButton @click="handleCancel" :disabled="isLoading">取消</ElButton>
          <ElButton
            type="primary"
            @click="handleSubmit"
            :loading="isLoading"
            :disabled="!canSave"
          >
            {{ dialogType === 'add' ? '新增' : '更新' }}
          </ElButton>
        </div>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick, onUnmounted } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { EnvironmentService } from '@/api/envApi'
import type { SupportedType } from '@/api/envApi'
import type {
  EnvironmentResponse,
  EnvironmentCreateRequest,
  EnvironmentUpdateRequest
} from '@/types/api/environment'

// 组件属性
interface Props {
  visible: boolean
  type: string
  environment?: EnvironmentResponse | null
}

// 组件事件
interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'submit'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 加载状态
const isLoading = ref(false)
const typeLoading = ref(false)

// 支持的环境类型列表
const supportedTypes = ref<SupportedType[]>([])

// 对话框显示控制
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const dialogType = computed(() => props.type)

// 表单实例
const formRef = ref<FormInstance>()
const authMethod = ref('password')

// 各种认证方式状态
const k8sAuthMethod = ref('kubeconfig')
const apiAuthType = ref('none')
const esAuthType = ref('none')

// 测试连接相关状态
const testLoading = ref(false)
const testResult = ref<any>(null)
const testPassed = ref(false)

// 表单数据
const formData = reactive({
  name: '',
  type: '',
  description: '',
  host: '',
  port: null as number | null,
  tags: '',
  config: {} as Record<string, any>
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入环境名称', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择环境类型', trigger: 'change' }
  ],
  host: [
    { pattern: /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?$/, message: '请输入正确的主机地址', trigger: 'blur' }
  ],
  port: [
    { type: 'number', min: 1, max: 65535, message: '端口范围 1-65535', trigger: 'blur' }
  ],
  'config.db_type': [
    { required: true, message: '请选择数据库类型', trigger: 'change' }
  ],
  'config.username': [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  return dialogType.value === 'add' ? '新增环境' : '编辑环境'
})

// 是否可以测试连接
const canTest = computed(() => {
  return formData.name && formData.type && formData.host && formData.port && !testLoading.value
})

// 是否可以保存（新增时需要测试通过，编辑时不强制要求）
const canSave = computed(() => {
  if (dialogType.value === 'add') {
    return testPassed.value && !isLoading.value
  }
  return !isLoading.value
})

// 获取支持的环境类型
const fetchSupportedTypes = async () => {
  try {
    typeLoading.value = true
    const types = await EnvironmentService.getSupportedTypes()
    supportedTypes.value = types || []
  } catch (error) {
    ElMessage.error('获取环境类型失败，请重试')
  } finally {
    typeLoading.value = false
  }
}

// 初始化表单数据
const initFormData = () => {
  const isEdit = props.type === 'edit' && props.environment
  const env = props.environment

  // 重置表单数据
  Object.assign(formData, {
    name: isEdit ? env?.name || '' : '',
    type: isEdit ? env?.type || '' : '',
    description: isEdit ? env?.description || '' : '',
    host: isEdit ? env?.host || '' : '',
    port: isEdit ? env?.port : null,
    tags: isEdit ? env?.tags || '' : '',
    config: isEdit ? (env?.config || {}) : {}
  })

  // 设置SSH认证方式
  if (isEdit && env?.type === 'ssh') {
    authMethod.value = env.config?.private_key ? 'key' : 'password'
  } else {
    authMethod.value = 'password'
  }
}

// 统一监听对话框状态变化
watch(
  () => [props.visible, props.type, props.environment],
  async ([visible]) => {
    if (visible) {
      // 获取支持的环境类型
      if (supportedTypes.value.length === 0) {
        await fetchSupportedTypes()
      }
      
      // 初始化表单数据
      initFormData()
      
      // 清除验证信息
      nextTick(() => {
        formRef.value?.clearValidate()
      })
    }
  },
  { immediate: true }
)

// 环境类型变化处理
const handleTypeChange = (type: string) => {
  formData.config = {}

  // 重置测试状态
  testResult.value = null
  testPassed.value = false

  // 根据类型设置默认配置
  switch (type) {
    case 'database':
      formData.config = {
        db_type: '',
        database_name: '',
        username: '',
        password: ''
      }
      formData.port = formData.port || 3306
      break
    case 'redis':
      formData.config = {
        password: '',
        db: 0
      }
      formData.port = formData.port || 6379
      break
    case 'ssh':
      formData.config = {
        username: '',
        password: ''
      }
      formData.port = formData.port || 22
      authMethod.value = 'password'
      break
    case 'k8s':
      formData.config = {
        kubeconfig_path: '',
        context: '',
        host: '',
        token: ''
      }
      formData.port = formData.port || 6443
      k8sAuthMethod.value = 'kubeconfig'
      break
    case 'api':
      formData.config = {
        method: 'GET',
        auth_type: 'none',
        username: '',
        password: '',
        token: '',
        api_key: '',
        api_key_header: 'X-API-Key'
      }
      formData.port = formData.port || 80
      apiAuthType.value = 'none'
      break
    case 'kafka':
      formData.config = {
        bootstrap_servers: '',
        security_protocol: 'PLAINTEXT',
        sasl_plain_username: '',
        sasl_plain_password: ''
      }
      formData.port = formData.port || 9092
      break
    case 'mongodb':
      formData.config = {
        database: '',
        username: '',
        password: '',
        auth_source: 'admin',
        replica_set: ''
      }
      formData.port = formData.port || 27017
      break
    case 'elasticsearch':
    case 'opensearch':
      formData.config = {
        scheme: 'http',
        username: '',
        password: '',
        api_key: ''
      }
      formData.port = formData.port || 9200
      esAuthType.value = 'none'
      break
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    isLoading.value = true

    // 处理不同类型的认证方式
    const submitData = { ...formData }

    // 处理SSH认证方式
    if (submitData.type === 'ssh') {
      if (authMethod.value === 'password') {
        delete submitData.config.private_key
      } else {
        delete submitData.config.password
      }
    }

    // 处理K8s认证方式
    if (submitData.type === 'k8s') {
      submitData.config.auth_type = k8sAuthMethod.value
      if (k8sAuthMethod.value === 'kubeconfig') {
        delete submitData.config.host
        delete submitData.config.token
      } else {
        delete submitData.config.kubeconfig_path
        delete submitData.config.context
      }
    }

    // 处理API认证方式
    if (submitData.type === 'api') {
      submitData.config.auth_type = apiAuthType.value
      if (apiAuthType.value === 'none') {
        delete submitData.config.username
        delete submitData.config.password
        delete submitData.config.token
        delete submitData.config.api_key
      } else if (apiAuthType.value === 'basic') {
        delete submitData.config.token
        delete submitData.config.api_key
      } else if (apiAuthType.value === 'bearer') {
        delete submitData.config.username
        delete submitData.config.password
        delete submitData.config.api_key
      } else if (apiAuthType.value === 'api_key') {
        delete submitData.config.username
        delete submitData.config.password
        delete submitData.config.token
      }
    }

    // 处理Elasticsearch认证方式
    if (submitData.type === 'elasticsearch' || submitData.type === 'opensearch') {
      submitData.config.auth_type = esAuthType.value
      if (esAuthType.value === 'none') {
        delete submitData.config.username
        delete submitData.config.password
        delete submitData.config.api_key
      } else if (esAuthType.value === 'basic') {
        delete submitData.config.api_key
      } else if (esAuthType.value === 'api_key') {
        delete submitData.config.username
        delete submitData.config.password
      }
    }

    let response: EnvironmentResponse

    if (dialogType.value === 'add') {
      // 新增环境
      response = await EnvironmentService.createEnvironment(submitData as EnvironmentCreateRequest)
      ElMessage.success('环境创建成功')
    } else {
      // 编辑环境
      const envId = props.environment?.id
      if (!envId) {
        ElMessage.error('环境ID不存在')
        return
      }
      response = await EnvironmentService.updateEnvironment(envId, submitData as EnvironmentUpdateRequest)
    }

    if (response) {
      dialogVisible.value = false
      emit('submit')
    }
  } catch (error: any) {
    // 处理特定错误信息
    let errorMessage = '操作失败，请重试'
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }
    
    ElMessage.error(errorMessage)
  } finally {
    isLoading.value = false
  }
}

/**
 * 测试连接
 */
const handleTestConnection = async () => {
  try {
    testLoading.value = true
    testResult.value = null

    // 构建测试配置
    const testConfig = {
      type: formData.type,
      host: formData.host,
      port: formData.port || undefined,
      config: { ...formData.config }
    }

    // 处理不同类型的认证配置
    if (formData.type === 'k8s') {
      testConfig.config.auth_type = k8sAuthMethod.value
    } else if (formData.type === 'api') {
      testConfig.config.auth_type = apiAuthType.value
    } else if (formData.type === 'elasticsearch' || formData.type === 'opensearch') {
      testConfig.config.auth_type = esAuthType.value
    }

    const result = await EnvironmentService.testConfigConnection(testConfig)
    testResult.value = result
    testPassed.value = result.success

    if (result.success) {
      ElMessage.success(`连接测试成功 (${result.duration?.toFixed(2)}s)`)
    } else {
      ElMessage.error(`连接测试失败: ${result.message}`)
    }

  } catch (error) {
    console.error('🔍 测试连接失败:', error)
    testResult.value = {
      success: false,
      message: '测试连接异常，请检查配置',
      duration: 0
    }
    testPassed.value = false
    ElMessage.error('测试连接异常，请检查配置')
  } finally {
    testLoading.value = false
  }
}

// 数据库名称建议配置
const databaseNameSuggestions = {
  mysql: [
    { value: 'test', description: '测试数据库' },
    { value: 'dev', description: '开发环境数据库' },
    { value: 'staging', description: '预发布环境数据库' },
    { value: 'production', description: '生产环境数据库' },
    { value: 'app_db', description: '应用数据库' },
    { value: 'user_db', description: '用户数据库' },
    { value: 'order_db', description: '订单数据库' },
    { value: 'log_db', description: '日志数据库' },
    { value: 'config_db', description: '配置数据库' },
    { value: 'analytics_db', description: '分析数据库' }
  ],
  postgresql: [
    { value: 'postgres', description: '默认数据库' },
    { value: 'test_db', description: '测试数据库' },
    { value: 'dev_db', description: '开发数据库' },
    { value: 'prod_db', description: '生产数据库' },
    { value: 'app_data', description: '应用数据' },
    { value: 'user_data', description: '用户数据' },
    { value: 'analytics', description: '分析数据' },
    { value: 'warehouse', description: '数据仓库' },
    { value: 'reporting', description: '报表数据库' },
    { value: 'backup_db', description: '备份数据库' }
  ],
  sqlite: [
    { value: 'app.db', description: '应用数据库文件' },
    { value: 'test.db', description: '测试数据库文件' },
    { value: 'data.db', description: '数据文件' },
    { value: 'cache.db', description: '缓存数据库' },
    { value: 'config.db', description: '配置数据库' },
    { value: 'logs.db', description: '日志数据库' },
    { value: 'temp.db', description: '临时数据库' },
    { value: 'backup.db', description: '备份数据库' }
  ]
}

// 获取数据库名称建议
const getDatabaseNameSuggestions = (queryString: string, callback: Function) => {
  const dbType = formData.config.db_type
  if (!dbType || !databaseNameSuggestions[dbType as keyof typeof databaseNameSuggestions]) {
    callback([])
    return
  }

  const suggestions = databaseNameSuggestions[dbType as keyof typeof databaseNameSuggestions]
  const results = queryString
    ? suggestions.filter(item =>
        item.value.toLowerCase().includes(queryString.toLowerCase()) ||
        item.description.toLowerCase().includes(queryString.toLowerCase())
      )
    : suggestions

  callback(results)
}

// 处理数据库名称选择
const handleDatabaseNameSelect = (item: Record<string, any>) => {
  formData.config.database_name = item.value
}

// MongoDB 数据库名称建议
const mongoDbNameSuggestions = [
  { value: 'test', description: '测试数据库' },
  { value: 'dev', description: '开发环境数据库' },
  { value: 'staging', description: '预发布环境数据库' },
  { value: 'production', description: '生产环境数据库' },
  { value: 'app_db', description: '应用数据库' },
  { value: 'user_db', description: '用户数据库' },
  { value: 'order_db', description: '订单数据库' },
  { value: 'log_db', description: '日志数据库' },
  { value: 'analytics_db', description: '分析数据库' },
  { value: 'cache_db', description: '缓存数据库' },
  { value: 'session_db', description: '会话数据库' },
  { value: 'config_db', description: '配置数据库' }
]

// 获取 MongoDB 数据库名称建议
const getMongoDbNameSuggestions = (queryString: string, callback: Function) => {
  const results = queryString
    ? mongoDbNameSuggestions.filter(item =>
        item.value.toLowerCase().includes(queryString.toLowerCase()) ||
        item.description.toLowerCase().includes(queryString.toLowerCase())
      )
    : mongoDbNameSuggestions

  callback(results)
}

// 处理 MongoDB 数据库名称选择
const handleMongoDbNameSelect = (item: Record<string, any>) => {
  formData.config.database = item.value
}

// 取消操作
const handleCancel = () => {
  if (isLoading.value) return
  
  // 检查表单是否有未保存的更改
  const hasChanges = dialogType.value === 'add' ? 
    formData.name || formData.type || formData.description || formData.host || 
    formData.port || formData.tags || Object.keys(formData.config).length > 0 :
    formData.name !== (props.environment?.name || '') ||
    formData.type !== (props.environment?.type || '') ||
    formData.description !== (props.environment?.description || '') ||
    formData.host !== (props.environment?.host || '') ||
    formData.port !== props.environment?.port ||
    formData.tags !== (props.environment?.tags || '') ||
    JSON.stringify(formData.config) !== JSON.stringify(props.environment?.config || {})

  if (hasChanges) {
    ElMessageBox.confirm(
      '确定要关闭吗？未保存的更改将会丢失。',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      dialogVisible.value = false
    }).catch(() => {
      // 用户取消关闭
    })
  } else {
    dialogVisible.value = false
  }
}

// 对话框关闭后的清理
const handleDialogClosed = () => {
  // 重置表单数据
  Object.assign(formData, {
    name: '',
    type: '',
    description: '',
    host: '',
    port: null,
    tags: '',
    config: {}
  })

  // 重置认证方式
  authMethod.value = 'password'
  k8sAuthMethod.value = 'kubeconfig'
  apiAuthType.value = 'none'
  esAuthType.value = 'none'

  // 重置测试状态
  testResult.value = null
  testPassed.value = false
  testLoading.value = false

  // 清除验证信息
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 页面卸载时清理
onUnmounted(() => {
  supportedTypes.value = []
})
</script>

<style scoped lang="scss">
.config-container {
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  padding: 16px;
  background: var(--el-bg-color-page);
  transition: all 0.3s ease;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.test-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.test-result {
  font-size: 13px;
  font-weight: 500;

  &.success {
    color: #67c23a;
  }

  &.error {
    color: #f56c6c;
  }

  .duration {
    color: #909399;
    font-weight: normal;
  }
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.suggestion-item {
  .suggestion-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .suggestion-desc {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 2px;
  }
}

/* 表单样式优化 - 使用原生样式 */
:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-regular);
  line-height: 32px;
  padding-right: 12px;
  text-align: right;
  vertical-align: middle;
  box-sizing: border-box;
}

:deep(.el-form-item__content) {
  line-height: 32px;
  position: relative;
  font-size: 14px;
}

/* 配置区域内的表单项样式调整 */
.config-container :deep(.el-form-item) {
  margin-bottom: 16px;
}

.config-container :deep(.el-form-item__label) {
  font-size: 13px;
  color: var(--el-text-color-primary);
}

/* 暗黑模式适配 */
html.dark .config-container {
  background: var(--el-bg-color);
  border-color: var(--el-border-color-darker);
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.el-form-item__label) {
    width: 100px !important;
    min-width: 100px !important;
  }

  .config-container :deep(.el-form-item__label) {
    font-size: 12px;
  }
}
</style>