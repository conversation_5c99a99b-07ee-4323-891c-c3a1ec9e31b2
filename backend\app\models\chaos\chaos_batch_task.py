"""
混沌测试批次任务数据模型
"""
from datetime import datetime
from typing import List, Dict, Any, Optional

from sqlalchemy import Column, String, Integer, Text, JSON, DateTime, ForeignKey, Boolean, Enum
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class ChaosBatchTask(BaseModel):
    """
    混沌测试批次任务模型
    管理多个子任务的批次执行
    """
    __tablename__ = "chaos_batch_tasks"

    # 基本信息
    name = Column(String(100), nullable=False, index=True, comment="批次任务名称")
    description = Column(Text, nullable=True, comment="批次任务描述")
    
    # 关联信息 - 批次任务只允许单个环境
    env_id = Column(Integer, nullable=False, comment="目标环境ID")
    
    # 批次执行配置
    batch_execution_mode = Column(
        Enum('sequential', 'order', name='batch_execution_mode_enum'),
        default='sequential',
        comment="批次执行模式：sequential(间隔执行)/order(连续执行)"
    )
    wait_time = Column(Integer, default=30, comment="间隔执行的等待时间(秒)")
    
    # 执行配置
    execution_type = Column(String(20), default="immediate", comment="执行类型：immediate/scheduled/periodic/cron")
    scheduled_time = Column(DateTime, nullable=True, comment="定时执行时间")
    periodic_config = Column(JSON, nullable=True, comment="周期性执行配置")
    cron_expression = Column(String(100), nullable=True, comment="Cron表达式")
    
    # 状态信息
    status = Column(String(20), default="pending", comment="运行状态：pending/running/completed/failed/cancelled")
    task_status = Column(String(20), default="enabled", comment="任务状态：enabled/disabled")
    execution_result = Column(JSON, nullable=True, comment="执行结果汇总")
    
    # 执行控制
    auto_destroy = Column(Boolean, default=True, comment="是否自动销毁故障")
    max_duration = Column(Integer, nullable=True, comment="最大执行时长(秒)")
    
    # 关系映射
    task_items = relationship("ChaosBatchTaskItem", back_populates="batch_task", cascade="all, delete-orphan")
    executions = relationship("ChaosExecution", back_populates="batch_task", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        return f"<ChaosBatchTask(id={self.id}, name={self.name}, status={self.status})>"

    @property
    def is_running(self) -> bool:
        """检查任务是否正在运行"""
        return self.status == "running"

    @property
    def is_completed(self) -> bool:
        """检查任务是否已完成"""
        return self.status in ["completed", "failed", "cancelled"]

    @property
    def can_execute(self) -> bool:
        """检查任务是否可以执行"""
        return (
            self.task_status == "enabled" and 
            self.status in ["pending", "failed"] and
            len(self.task_items) > 0
        )

    @property
    def can_stop(self) -> bool:
        """检查任务是否可以停止"""
        return self.status == "running"

    @property
    def can_edit(self) -> bool:
        """检查任务是否可以编辑"""
        return self.status in ["pending", "failed", "cancelled"]

    @property
    def can_delete(self) -> bool:
        """检查任务是否可以删除"""
        return self.status != "running"

    @property
    def task_count(self) -> int:
        """获取子任务数量"""
        return len(self.task_items) if self.task_items else 0

    @property
    def env_count(self) -> int:
        """获取环境数量（批次任务固定为1）"""
        return 1

    def update_status(self, new_status: str, result: Optional[Dict[str, Any]] = None) -> None:
        """更新任务状态"""
        self.status = new_status
        if result:
            if not self.execution_result:
                self.execution_result = {}
            self.execution_result.update(result)

    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要信息"""
        return {
            "task_id": self.id,
            "task_name": self.name,
            "task_type": "batch",
            "task_count": self.task_count,
            "env_id": self.env_id,
            "batch_execution_mode": self.batch_execution_mode,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "execution_result": self.execution_result or {}
        }


class ChaosBatchTaskItem(BaseModel):
    """
    批次任务子项模型
    定义批次任务中的单个故障注入配置
    """
    __tablename__ = "chaos_batch_task_items"

    # 关联信息
    batch_task_id = Column(Integer, ForeignKey("chaos_batch_tasks.id"), nullable=False, comment="批次任务ID")
    
    # 执行顺序
    task_order = Column(Integer, nullable=False, comment="任务执行顺序")
    
    # 基本信息
    name = Column(String(100), nullable=False, comment="子任务名称")
    description = Column(Text, nullable=True, comment="子任务描述")
    
    # 故障配置
    fault_type = Column(String(50), nullable=False, comment="故障类型：cpu/memory/network/disk/process/k8s")
    fault_params = Column(JSON, nullable=False, comment="故障参数配置")
    
    # 执行控制
    auto_destroy = Column(Boolean, default=True, comment="是否自动销毁故障")
    max_duration = Column(Integer, nullable=True, comment="最大执行时长(秒)")
    
    # 关系映射
    batch_task = relationship("ChaosBatchTask", back_populates="task_items")

    def __repr__(self) -> str:
        return f"<ChaosBatchTaskItem(id={self.id}, name={self.name}, order={self.task_order})>"

    @property
    def full_name(self) -> str:
        """获取完整名称（包含批次任务名称）"""
        if self.batch_task:
            return f"{self.batch_task.name} - {self.name}"
        return self.name

    def get_fault_summary(self) -> Dict[str, Any]:
        """获取故障配置摘要"""
        return {
            "item_id": self.id,
            "name": self.name,
            "fault_type": self.fault_type,
            "fault_params": self.fault_params,
            "task_order": self.task_order,
            "auto_destroy": self.auto_destroy,
            "max_duration": self.max_duration
        }
