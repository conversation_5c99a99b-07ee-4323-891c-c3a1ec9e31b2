/**
 * 混沌测试任务状态管理
 * 专门管理任务相关的状态和操作
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { usePagination } from '@/store/shared/pagination'
import { useLoading } from '@/store/shared/loading'
import { globalCache } from '@/store/shared/cache'
import ChaosService from '@/api/chaosApi'
import type {
  ChaosTask,
  ChaosTaskCreate,
  ChaosTaskUpdate,
  ChaosTaskSearchParams,
  ChaosTaskExecuteRequest,
  ChaosTaskBatchRequest,
  ChaosTaskStatistics
} from '@/types/api/chaos'

export const useChaosTasksStore = defineStore('chaos-tasks', () => {
  // ==================== 状态定义 ====================
  
  const tasks = ref<ChaosTask[]>([])
  const currentTask = ref<ChaosTask | null>(null)
  const taskStatistics = ref<ChaosTaskStatistics | null>(null)
  
  // 分页和加载状态
  const pagination = usePagination({ defaultSize: 20 })
  const loading = useLoading()
  const statisticsLoading = useLoading()

  // ==================== 计算属性 ====================
  
  const totalTasks = computed(() => pagination.pagination.total)
  
  const runningTasks = computed(() => 
    tasks.value.filter(task => task.status === 'running')
  )
  
  const pendingTasks = computed(() => 
    tasks.value.filter(task => task.status === 'pending')
  )
  
  const activeTasks = computed(() => 
    tasks.value.filter(task => ['pending', 'running', 'paused'].includes(task.status))
  )

  const completedTasks = computed(() =>
    tasks.value.filter(task => ['success', 'failed', 'cancelled'].includes(task.status))
  )

  // ==================== 任务管理方法 ====================
  
  /**
   * 获取任务列表
   */
  const fetchTasks = async (params: ChaosTaskSearchParams = {}) => {
    const queryParams = {
      ...params,
      ...pagination.params.value
    }

    return await loading.withLoading(async () => {
      const response = await ChaosService.getTaskList(queryParams)
      
      tasks.value = response.records
      pagination.updateFromResponse(response)
      
      return response
    })
  }
  
  /**
   * 创建任务
   */
  const createTask = async (data: ChaosTaskCreate) => {
    return await loading.withLoading(async () => {
      const response = await ChaosService.createTask(data)
      
      // 添加到列表开头
      tasks.value.unshift(response)
      pagination.setTotal(pagination.pagination.total + 1)
      
      // 清除相关缓存
      globalCache.remove('task_statistics')
      
      return response
    })
  }
  
  /**
   * 获取任务详情
   */
  const fetchTask = async (id: number) => {
    // 先尝试从缓存获取
    const cacheKey = `task_detail_${id}`
    const cached = globalCache.get(cacheKey)
    if (cached) {
      currentTask.value = cached
      return cached
    }

    return await loading.withLoading(async () => {
      const response = await ChaosService.getTaskDetail(id)
      
      currentTask.value = response
      
      // 缓存任务详情
      globalCache.set(cacheKey, response, 5 * 60 * 1000) // 5分钟缓存
      
      return response
    })
  }
  
  /**
   * 更新任务
   */
  const updateTask = async (id: number, data: ChaosTaskUpdate) => {
    return await loading.withLoading(async () => {
      const response = await ChaosService.updateTask(id, data)
      
      // 更新本地状态
      const index = tasks.value.findIndex(task => task.id === id)
      if (index !== -1) {
        tasks.value[index] = response
      }
      
      if (currentTask.value?.id === id) {
        currentTask.value = response
      }
      
      // 清除相关缓存
      globalCache.remove(`task_detail_${id}`)
      
      return response
    })
  }

  /**
   * 删除任务
   */
  const deleteTask = async (id: number) => {
    return await loading.withLoading(async () => {
      await ChaosService.deleteTask(id)
      
      // 从本地状态中移除
      const index = tasks.value.findIndex(task => task.id === id)
      if (index !== -1) {
        tasks.value.splice(index, 1)
        pagination.setTotal(pagination.pagination.total - 1)
      }
      
      if (currentTask.value?.id === id) {
        currentTask.value = null
      }
      
      // 清除相关缓存
      globalCache.remove(`task_detail_${id}`)
      globalCache.remove('task_statistics')
    })
  }
  
  /**
   * 执行任务
   */
  const executeTask = async (id: number, data: ChaosTaskExecuteRequest) => {
    return await loading.withLoading(async () => {
      const response = await ChaosService.executeTask(id, data)
      
      // 更新任务状态
      const task = tasks.value.find(t => t.id === id)
      if (task) {
        task.status = 'running'
      }
      
      if (currentTask.value?.id === id) {
        currentTask.value.status = 'running'
      }
      
      // 清除相关缓存
      globalCache.remove(`task_detail_${id}`)
      
      return response
    })
  }

  // 辅助函数：更新任务状态（启用/禁用）
  const updateTaskTaskStatus = (id: number, taskStatus: string) => {
    const task = tasks.value.find(t => t.id === id)
    if (task) {
      task.task_status = taskStatus
    }

    if (currentTask.value && currentTask.value.id === id) {
      currentTask.value.task_status = taskStatus
    }
  }

  /**
   * 停止任务
   */
  const stopTask = async (id: number) => {
    return await loading.withLoading(async () => {
      await ChaosService.stopTask(id)

      // 更新任务状态
      updateTaskStatus(id, 'cancelled')
    })
  }

  /**
   * 启用任务
   */
  const enableTask = async (id: number) => {
    return await loading.withLoading(async () => {
      await ChaosService.enableTask(id)

      // 重新获取任务详情以更新所有状态
      const updatedTask = await ChaosService.getTaskDetail(id)
      const index = tasks.value.findIndex(t => t.id === id)
      if (index !== -1) {
        tasks.value[index] = updatedTask
      }

      if (currentTask.value && currentTask.value.id === id) {
        currentTask.value = updatedTask
      }
    })
  }

  /**
   * 禁用任务
   */
  const disableTask = async (id: number) => {
    return await loading.withLoading(async () => {
      await ChaosService.disableTask(id)

      // 重新获取任务详情以更新所有状态
      const updatedTask = await ChaosService.getTaskDetail(id)
      const index = tasks.value.findIndex(t => t.id === id)
      if (index !== -1) {
        tasks.value[index] = updatedTask
      }

      if (currentTask.value && currentTask.value.id === id) {
        currentTask.value = updatedTask
      }
    })
  }

  /**
   * 重置任务
   */
  const resetTask = async (id: number) => {
    return await loading.withLoading(async () => {
      await ChaosService.resetTask(id)

      // 更新任务状态
      updateTaskStatus(id, 'pending')
    })
  }
  
  /**
   * 批量操作任务
   */
  const batchOperation = async (taskIds: number[], action: string) => {
    const data: ChaosTaskBatchRequest = { task_ids: taskIds, action }
    
    return await loading.withLoading(async () => {
      const response = await ChaosService.batchTaskOperation(data)
      
      // 根据操作类型更新本地状态
      if (action === 'delete') {
        tasks.value = tasks.value.filter(task => !taskIds.includes(task.id))
        pagination.setTotal(pagination.pagination.total - taskIds.length)
      }
      
      // 清除相关缓存
      taskIds.forEach(id => globalCache.remove(`task_detail_${id}`))
      globalCache.remove('task_statistics')
      
      return response
    })
  }

  /**
   * 获取任务统计
   */
  const fetchTaskStatistics = async () => {
    // 先尝试从缓存获取
    const cached = globalCache.get('task_statistics')
    if (cached) {
      taskStatistics.value = cached
      return cached
    }

    return await statisticsLoading.withLoading(async () => {
      const response = await ChaosService.getTaskStatistics()
      
      taskStatistics.value = response
      
      // 缓存统计信息
      globalCache.set('task_statistics', response, 2 * 60 * 1000) // 2分钟缓存
      
      return response
    })
  }

  // ==================== 辅助方法 ====================
  
  /**
   * 更新任务状态
   */
  const updateTaskStatus = (id: number, status: string) => {
    const task = tasks.value.find(t => t.id === id)
    if (task) {
      task.status = status
    }
    
    if (currentTask.value?.id === id) {
      currentTask.value.status = status
    }
    
    // 清除相关缓存
    globalCache.remove(`task_detail_${id}`)
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    tasks.value = []
    currentTask.value = null
    taskStatistics.value = null
    pagination.reset()
    loading.reset()
    statisticsLoading.reset()
  }

  return {
    // 状态
    tasks: readonly(tasks),
    currentTask: readonly(currentTask),
    taskStatistics: readonly(taskStatistics),
    pagination,
    loading,
    statisticsLoading,
    
    // 计算属性
    totalTasks,
    runningTasks,
    pendingTasks,
    activeTasks,
    completedTasks,
    
    // 方法
    fetchTasks,
    createTask,
    fetchTask,
    updateTask,
    deleteTask,
    executeTask,
    stopTask,
    enableTask,
    disableTask,
    resetTask,
    batchOperation,
    fetchTaskStatistics,
    resetState
  }
})
