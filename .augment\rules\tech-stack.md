---
type: "always_apply"
---

# 后端技术栈规范

## 项目概述

**项目名称**: DpTestPlatform Backend  
**技术栈**: FastAPI + SQLAlchemy + Pydantic V2 + Alembic  
**架构模式**: 分层架构 + 依赖注入  
**数据库**: Mysql 
**认证方式**: JWT Bearer Token  

## 1. 技术架构规范

### 1.1 核心技术栈

#### Web框架
- **FastAPI 0.104.1**: 现代化异步Web框架，自动API文档生成
- **Uvicorn**: ASGI服务器，支持异步请求处理
- **Pydantic V2**: 数据验证和序列化，类型安全

#### 数据层
- **SQLAlchemy 2.0**: 现代异步ORM，声明式模型定义
- **Alembic**: 数据库迁移管理
- **asyncpg**: PostgreSQL异步驱动
- **aiosqlite**: SQLite异步驱动

#### 认证授权
- **Python-Jose**: JWT令牌处理
- **Passlib**: 密码哈希和验证
- **Python-Multipart**: 文件上传支持

#前端技术栈规范
## 项目概述

**项目名称**: DpTestPlatform Frontend  
**技术栈**: Vue 3 + TypeScript + Vite + Element Plus + Pinia  
**开发模式**: 企业级中后台管理系统  
**构建工具**: Vite 6.1.0  

## 1. 技术架构概览

### 1.1 核心技术栈

#### 前端框架
- **Vue 3.5.12**: 采用 Composition API，支持 `<script setup>` 语法
- **TypeScript**: 强类型语言支持，提供更好的开发体验
- **Vite**: 现代化构建工具，快速的热更新和构建

#### UI 组件库
- **Element Plus 2.10.2**: 企业级 UI 组件库
- **@element-plus/icons-vue**: Element Plus 图标库
- **自定义组件系统**: 基于业务需求的组件封装

#### 状态管理
- **Pinia 3.0.2**: Vue 3 官方推荐的状态管理库
- **pinia-plugin-persistedstate**: 状态持久化插件

#### 路由管理
- **Vue Router 4.4.2**: 官方路由管理器
- **动态路由**: 支持基于权限的路由配置
- **路由守卫**: 完整的路由拦截机制