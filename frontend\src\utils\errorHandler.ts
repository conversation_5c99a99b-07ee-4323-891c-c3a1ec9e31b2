/**
 * 统一错误处理工具 (增强版)
 * 支持新的统一错误码体系
 */
import { ElMessage, ElNotification } from 'element-plus'
import { $t } from '@/locales'
import { ErrorCode, ErrorType, ErrorModule, getErrorType, getErrorModule, isAuthError, isSystemError } from '@/constants/errorCodes'
import { HttpError } from './http/error'
import type { ErrorResponse } from '@/types/api/common'

/**
 * 错误处理选项
 */
export interface ErrorHandlerOptions {
  /** 是否显示错误消息 */
  showMessage?: boolean
  /** 自定义错误消息 */
  customMessage?: string
  /** 是否记录错误日志 */
  logError?: boolean
  /** 错误回调函数 */
  onError?: (error: any) => void
  /** 通知类型 */
  notificationType?: 'message' | 'notification' | 'auto'
}

/**
 * 统一错误处理函数 (增强版)
 * @param error 错误对象
 * @param options 处理选项
 */
export function handleError(error: any, options: ErrorHandlerOptions = {}) {
  const {
    showMessage = true,
    customMessage,
    logError = true,
    onError,
    notificationType = 'auto'
  } = options

  let errorMessage = customMessage || '操作失败'
  let errorCode: number | undefined
  let errorType: ErrorType | null = null

  // 处理不同类型的错误
  if (error instanceof HttpError) {
    errorMessage = customMessage || error.message
    errorCode = error.errorCode
    errorType = error.getErrorType()
  } else if (error?.response?.data) {
    const responseData = error.response.data as ErrorResponse
    if ('error_code' in responseData) {
      // 新格式错误响应
      errorMessage = customMessage || responseData.message
      errorCode = responseData.error_code
      errorType = getErrorType(responseData.error_code)
    } else if ('message' in responseData) {
      // 旧格式错误响应
      errorMessage = customMessage || responseData.message
    }
  } else if (error?.message) {
    errorMessage = customMessage || error.message
  }

  // 显示错误消息
  if (showMessage) {
    showErrorMessage(errorMessage, errorCode, errorType, notificationType)
  }

  // 记录错误日志
  if (logError) {
    console.error('[Error Handler]', {
      error,
      message: errorMessage,
      errorCode,
      errorType,
      timestamp: new Date().toISOString()
    })
  }

  // 执行错误回调
  if (onError) {
    onError(error)
  }

  return {
    error,
    message: errorMessage,
    errorCode,
    errorType
  }
}

/**
 * 静默错误处理（不显示消息）
 * @param error 错误对象
 * @param onError 错误回调
 */
export function handleSilentError(error: any, onError?: (error: any) => void) {
  return handleError(error, {
    showMessage: false,
    onError
  })
}

/**
 * 业务操作错误处理
 * @param error 错误对象
 * @param operation 操作名称
 * @param onError 错误回调
 */
export function handleBusinessError(
  error: any, 
  operation: string, 
  onError?: (error: any) => void
) {
  return handleError(error, {
    customMessage: `${operation}失败`,
    onError
  })
}

/**
 * API调用错误处理装饰器
 * @param options 错误处理选项
 */
export function withErrorHandler(options: ErrorHandlerOptions = {}) {
  return function <T extends (...args: any[]) => Promise<any>>(
    target: any,
    propertyKey: string,
    descriptor: TypedPropertyDescriptor<T>
  ) {
    const originalMethod = descriptor.value!

    descriptor.value = async function (...args: any[]) {
      try {
        return await originalMethod.apply(this, args)
      } catch (error) {
        handleError(error, options)
        throw error
      }
    } as T

    return descriptor
  }
}

/**
 * 检查是否为取消错误
 * @param error 错误对象
 */
export function isCancelError(error: any): boolean {
  return error?.message === 'cancel' || error === 'cancel'
}

/**
 * 安全的异步操作执行器
 * @param asyncFn 异步函数
 * @param options 错误处理选项
 */
export async function safeExecute<T>(
  asyncFn: () => Promise<T>,
  options: ErrorHandlerOptions = {}
): Promise<{ data?: T; error?: any; success: boolean }> {
  try {
    const data = await asyncFn()
    return { data, success: true }
  } catch (error) {
    // 如果是取消错误，不处理
    if (isCancelError(error)) {
      return { error, success: false }
    }

    const result = handleError(error, options)
    return { error: result.error, success: false }
  }
}

/**
 * 智能显示错误消息
 */
function showErrorMessage(
  message: string,
  errorCode?: number,
  errorType?: ErrorType | null,
  notificationType: string = 'auto'
) {
  // 根据错误类型和配置选择显示方式
  if (notificationType === 'message') {
    ElMessage.error(message)
    return
  }

  if (notificationType === 'notification') {
    ElNotification.error({
      title: $t('error.operationFailed'),
      message: message,
      duration: 6000
    })
    return
  }

  // 自动选择显示方式
  if (errorCode && errorType) {
    switch (errorType) {
      case ErrorType.VALIDATION:
        ElMessage.error({
          message: message,
          duration: 5000,
          showClose: true
        })
        break

      case ErrorType.PERMISSION:
        ElNotification.warning({
          title: $t('error.permissionDenied'),
          message: message,
          duration: 8000
        })
        break

      case ErrorType.NOT_FOUND:
        ElMessage.info({
          message: message,
          duration: 4000
        })
        break

      case ErrorType.BUSINESS:
        ElNotification.error({
          title: $t('error.businessError'),
          message: message,
          duration: 6000
        })
        break

      case ErrorType.SYSTEM:
        ElNotification.error({
          title: $t('error.systemError'),
          message: message,
          duration: 10000
        })
        break

      default:
        ElMessage.error(message)
    }
  } else {
    // 默认使用消息提示
    ElMessage.error(message)
  }
}

/**
 * 根据错误码自动处理错误
 */
export function handleErrorByCode(errorCode: number, message?: string) {
  const errorType = getErrorType(errorCode)

  if (isAuthError(errorCode)) {
    ElNotification.warning({
      title: $t('error.authError'),
      message: message || $t('error.authenticationFailed'),
      duration: 8000
    })
  } else if (errorType === ErrorType.PERMISSION) {
    ElNotification.warning({
      title: $t('error.permissionDenied'),
      message: message || $t('error.insufficientPermissions'),
      duration: 8000
    })
  } else if (isSystemError(errorCode)) {
    ElNotification.error({
      title: $t('error.systemError'),
      message: message || $t('error.systemError'),
      duration: 10000
    })
  } else {
    showErrorMessage(message || $t('error.operationFailed'), errorCode, errorType, 'auto')
  }
}
