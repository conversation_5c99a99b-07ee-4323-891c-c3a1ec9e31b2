"""
认证 API 路由
包含登录、注册、token刷新等功能
"""
from typing import Any, Dict

from fastapi import APIRouter, Depends, Request
from app.core.dependencies import DatabaseDep
from app.core.responses import response_builder
from app.core.exceptions import BusinessException, exception_handler
from app.api.deps import get_current_user
from app.schemas.user.auth import LoginRequest, RefreshTokenRequest, RegisterRequest
from app.services.user.auth import AuthService

router = APIRouter()


# 服务依赖注入
def get_auth_service(db: DatabaseDep) -> AuthService:
    """获取认证服务实例"""
    return AuthService(db)


@router.post("/login", response_model=Dict[str, str], summary="用户登录")
async def login(
    user_credentials: LoginRequest,
    request: Request,
    auth_service: AuthService = Depends(get_auth_service)
) -> Dict[str, str]:
    """
    用户登录接口

    Args:
        user_credentials: 登录凭据
        request: HTTP请求对象
        auth_service: 认证服务

    Returns:
        登录响应包含access_token和refresh_token
    """
    try:
        login_data = await auth_service.login(user_credentials, request)
        return response_builder.success(login_data)
    except BusinessException as e:
        raise exception_handler.to_http_exception(e)


@router.post("/register", response_model=Dict[str, Any], status_code=201, summary="用户注册")
async def register(
    register_data: RegisterRequest,
    auth_service: AuthService = Depends(get_auth_service)
) -> Dict[str, Any]:
    """
    用户注册接口

    Args:
        register_data: 注册数据
        auth_service: 认证服务

    Returns:
        注册响应
    """
    try:
        result = await auth_service.register(register_data)
        return response_builder.created(result)
    except BusinessException as e:
        raise exception_handler.to_http_exception(e)


@router.post("/refresh", response_model=Dict[str, str], summary="刷新令牌")
async def refresh_token(
    token_data: RefreshTokenRequest,
    auth_service: AuthService = Depends(get_auth_service)
) -> Dict[str, str]:
    """
    刷新令牌接口

    Args:
        token_data: 刷新令牌数据
        auth_service: 认证服务

    Returns:
        新的访问令牌
    """
    try:
        tokens = await auth_service.refresh_token(token_data)
        return response_builder.success(tokens)
    except BusinessException as e:
        raise exception_handler.to_http_exception(e)


@router.post("/logout", response_model=Dict[str, str], summary="用户登出")
async def logout(
    current_user=Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
) -> Dict[str, str]:
    """
    用户登出接口

    Args:
        current_user: 当前用户
        auth_service: 认证服务

    Returns:
        登出响应
    """
    try:
        result = await auth_service.logout(current_user)
        return response_builder.success(result)
    except BusinessException as e:
        raise exception_handler.to_http_exception(e)