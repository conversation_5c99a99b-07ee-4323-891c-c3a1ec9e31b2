import { AxiosError } from 'axios'
import { ElMessage, ElNotification } from 'element-plus'
import { ApiStatus } from './status'
import { $t } from '@/locales'
import { ErrorCode, ErrorType, getErrorType, getErrorModule, isAuthError, isSystemError } from '@/constants/errorCodes'
import type { ErrorResponse as NewErrorResponse } from '@/types/api/common'

// 错误响应接口 (旧格式 - 向后兼容)
export interface ErrorResponse {
  code: number
  msg: string
  data?: unknown
}

// 统一错误响应接口 (支持新旧格式)
export interface UnifiedErrorResponse extends NewErrorResponse {
  // 兼容旧格式字段
  code?: number
  msg?: string
  data?: unknown
}

// 错误日志数据接口 (增强版)
export interface ErrorLogData {
  code: number
  message: string
  data?: unknown
  timestamp: string
  url?: string
  method?: string
  stack?: string
  errorCode?: number    // 业务错误码
  httpCode?: number     // HTTP状态码
  details?: unknown     // 错误详情
  requestId?: string    // 请求ID
}

// 自定义 HttpError 类 (增强版)
export class HttpError extends Error {
  public readonly code: number
  public readonly errorCode?: number  // 业务错误码
  public readonly httpCode?: number   // HTTP状态码
  public readonly data?: unknown
  public readonly details?: unknown
  public readonly timestamp: string
  public readonly url?: string
  public readonly method?: string
  public readonly requestId?: string

  constructor(
    message: string,
    code: number,
    options?: {
      errorCode?: number
      httpCode?: number
      data?: unknown
      details?: unknown
      url?: string
      method?: string
      requestId?: string
    }
  ) {
    super(message)
    this.name = 'HttpError'
    this.code = code
    this.errorCode = options?.errorCode
    this.httpCode = options?.httpCode
    this.data = options?.data
    this.details = options?.details
    this.timestamp = new Date().toISOString()
    this.url = options?.url
    this.method = options?.method
    this.requestId = options?.requestId
  }

  public toLogData(): ErrorLogData {
    return {
      code: this.code,
      message: this.message,
      data: this.data,
      timestamp: this.timestamp,
      url: this.url,
      method: this.method,
      stack: this.stack,
      errorCode: this.errorCode,
      httpCode: this.httpCode,
      details: this.details,
      requestId: this.requestId
    }
  }

  /**
   * 获取错误类型
   */
  public getErrorType(): ErrorType | null {
    return this.errorCode ? getErrorType(this.errorCode) : null
  }

  /**
   * 获取错误模块
   */
  public getErrorModule(): string | null {
    return this.errorCode ? getErrorModule(this.errorCode) : null
  }

  /**
   * 是否为认证错误
   */
  public isAuthError(): boolean {
    return this.errorCode ? isAuthError(this.errorCode) : false
  }

  /**
   * 是否为系统错误
   */
  public isSystemError(): boolean {
    return this.errorCode ? isSystemError(this.errorCode) : false
  }
}

/**
 * 获取错误消息
 * @param status 错误状态码
 * @returns 错误消息
 */
const getErrorMessage = (status: number): string => {
  const errorMap: Record<number, string> = {
    [ApiStatus.unauthorized]: 'httpMsg.unauthorized',
    [ApiStatus.forbidden]: 'httpMsg.forbidden',
    [ApiStatus.notFound]: 'httpMsg.notFound',
    [ApiStatus.methodNotAllowed]: 'httpMsg.methodNotAllowed',
    [ApiStatus.requestTimeout]: 'httpMsg.requestTimeout',
    [ApiStatus.internalServerError]: 'httpMsg.internalServerError',
    [ApiStatus.badGateway]: 'httpMsg.badGateway',
    [ApiStatus.serviceUnavailable]: 'httpMsg.serviceUnavailable',
    [ApiStatus.gatewayTimeout]: 'httpMsg.gatewayTimeout'
  }

  return $t(errorMap[status] || 'httpMsg.internalServerError')
}

/**
 * 处理错误 (支持新旧错误格式)
 * @param error 错误对象
 * @returns 错误对象
 */
export function handleError(error: AxiosError<UnifiedErrorResponse>): never {
  // 处理取消的请求
  if (error.code === 'ERR_CANCELED') {
    console.warn('Request cancelled:', error.message)
    throw new HttpError($t('httpMsg.requestCancelled'), ApiStatus.error)
  }

  const statusCode = error.response?.status
  const responseData = error.response?.data
  const requestConfig = error.config

  // 处理网络错误
  if (!error.response) {
    throw new HttpError($t('httpMsg.networkError'), ApiStatus.error, {
      url: requestConfig?.url,
      method: requestConfig?.method?.toUpperCase()
    })
  }

  // 处理新格式的错误响应
  if (responseData && 'error_code' in responseData) {
    const { message, error_code, http_code, details, timestamp, request_id } = responseData
    throw new HttpError(message, statusCode || http_code, {
      errorCode: error_code,
      httpCode: http_code,
      details: details,
      requestId: request_id,
      url: requestConfig?.url,
      method: requestConfig?.method?.toUpperCase()
    })
  }

  // 处理旧格式的错误响应 (向后兼容)
  const errorMessage = responseData?.msg || error.message
  const message = statusCode
    ? getErrorMessage(statusCode)
    : errorMessage || $t('httpMsg.requestFailed')

  throw new HttpError(message, statusCode || ApiStatus.error, {
    data: responseData,
    url: requestConfig?.url,
    method: requestConfig?.method?.toUpperCase()
  })
}

/**
 * 显示错误消息 (根据错误类型智能显示)
 * @param error 错误对象
 * @param showMessage 是否显示错误消息
 */
export function showError(error: HttpError, showMessage: boolean = true): void {
  if (!showMessage) {
    // 记录错误日志
    console.error('[HTTP Error]', error.toLogData())
    return
  }

  // 根据错误类型选择不同的显示方式
  if (error.errorCode) {
    const errorType = error.getErrorType()
    const errorModule = error.getErrorModule()

    switch (errorType) {
      case ErrorType.VALIDATION:
        // 验证错误 - 使用普通消息提示
        ElMessage.error({
          message: error.message,
          duration: 5000,
          showClose: true
        })
        break

      case ErrorType.PERMISSION:
        // 权限错误 - 使用警告通知
        ElNotification.warning({
          title: $t('error.permissionDenied'),
          message: error.message,
          duration: 8000
        })
        break

      case ErrorType.NOT_FOUND:
        // 资源不存在 - 使用信息提示
        ElMessage.info({
          message: error.message,
          duration: 4000
        })
        break

      case ErrorType.BUSINESS:
        // 业务错误 - 使用错误通知
        ElNotification.error({
          title: $t('error.businessError'),
          message: error.message,
          duration: 6000
        })
        break

      case ErrorType.SYSTEM:
        // 系统错误 - 使用错误通知，包含请求ID
        ElNotification.error({
          title: $t('error.systemError'),
          message: error.requestId
            ? `${error.message} (请求ID: ${error.requestId})`
            : error.message,
          duration: 10000
        })
        break

      default:
        // 默认错误提示
        ElMessage.error(error.message)
    }
  } else {
    // 旧格式错误或HTTP错误 - 使用默认提示
    if (error.isAuthError()) {
      ElNotification.warning({
        title: $t('error.authError'),
        message: error.message,
        duration: 8000
      })
    } else {
      ElMessage.error(error.message)
    }
  }

  // 记录错误日志
  console.error('[HTTP Error]', error.toLogData())
}

/**
 * 判断是否为 HttpError 类型
 * @param error 错误对象
 * @returns 是否为 HttpError 类型
 */
export const isHttpError = (error: unknown): error is HttpError => {
  return error instanceof HttpError
}
