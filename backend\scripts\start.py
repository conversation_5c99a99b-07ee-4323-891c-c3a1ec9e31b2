#!/usr/bin/env python3
"""
应用启动脚本
"""
import os
import sys
import argparse
import subprocess
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent

def validate_config():
    """验证配置文件"""
    env_file = PROJECT_ROOT / ".env"

    if not env_file.exists():
        print(f"❌ 配置文件不存在: {env_file}")
        print(f"💡 运行以下命令创建模板: python scripts/env_manager.py create")
        return False

    print(f"✅ 配置文件验证通过: {env_file}")
    return True

def start_app(mode: str = "dev"):
    """启动应用"""
    if mode == "dev":
        print("🚀 启动开发模式...")
        cmd = [
            sys.executable, "-m", "uvicorn",
            "app.main:app",
            "--reload",
            "--host", "0.0.0.0",
            "--port", "8000"
        ]
    else:  # production
        print("🚀 启动生产模式...")
        cmd = [
            sys.executable, "-m", "uvicorn",
            "app.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--workers", "4"
        ]

    subprocess.run(cmd, cwd=PROJECT_ROOT)

def main():
    parser = argparse.ArgumentParser(description="DpTestPlatform 启动工具")
    parser.add_argument("--mode", "-m",
                       choices=["dev", "prod"],
                       default="dev",
                       help="运行模式 (默认: dev)")
    parser.add_argument("--check", "-c", action="store_true",
                       help="只检查配置，不启动应用")

    args = parser.parse_args()

    print("🚀 DpTestPlatform 启动工具")
    print("=" * 50)

    # 验证配置
    if not validate_config():
        sys.exit(1)

    # 如果只是检查配置，则退出
    if args.check:
        print("✅ 配置检查完成")
        return

    # 启动应用
    try:
        start_app(args.mode)
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
