"""
性能监控工具
提供应用性能监控和优化建议
"""
import time
import psutil
import asyncio
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

from app.utils.logger import setup_logger

logger = setup_logger()


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    active_connections: int
    request_count: int
    avg_response_time: float
    slow_requests: int


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics_history = []
        self.request_times = []
        self.slow_request_threshold = 1.0  # 1秒
        self.monitoring = False
        
    async def start_monitoring(self, interval: int = 60):
        """开始性能监控"""
        self.monitoring = True
        logger.info("性能监控已启动")
        
        while self.monitoring:
            try:
                metrics = await self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # 保持最近24小时的数据
                cutoff_time = datetime.now() - timedelta(hours=24)
                self.metrics_history = [
                    m for m in self.metrics_history 
                    if m.timestamp > cutoff_time
                ]
                
                # 检查性能警告
                await self._check_performance_warnings(metrics)
                
            except Exception as e:
                logger.error(f"性能监控错误: {e}")
            
            await asyncio.sleep(interval)
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring = False
        logger.info("性能监控已停止")
    
    async def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        # 系统资源使用情况
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        # 计算平均响应时间
        avg_response_time = 0.0
        slow_requests = 0
        
        if self.request_times:
            avg_response_time = sum(self.request_times) / len(self.request_times)
            slow_requests = len([t for t in self.request_times if t > self.slow_request_threshold])
            # 清空请求时间列表，准备下一轮统计
            self.request_times.clear()
        
        return PerformanceMetrics(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_mb=memory.used / 1024 / 1024,
            active_connections=0,  # 需要从数据库连接池获取
            request_count=len(self.request_times),
            avg_response_time=avg_response_time,
            slow_requests=slow_requests
        )
    
    async def _check_performance_warnings(self, metrics: PerformanceMetrics):
        """检查性能警告"""
        warnings = []
        
        if metrics.cpu_percent > 80:
            warnings.append(f"CPU使用率过高: {metrics.cpu_percent:.1f}%")
        
        if metrics.memory_percent > 85:
            warnings.append(f"内存使用率过高: {metrics.memory_percent:.1f}%")
        
        if metrics.avg_response_time > 2.0:
            warnings.append(f"平均响应时间过长: {metrics.avg_response_time:.3f}s")
        
        if metrics.slow_requests > 10:
            warnings.append(f"慢请求数量过多: {metrics.slow_requests}")
        
        if warnings:
            logger.warning(f"性能警告: {'; '.join(warnings)}")
    
    def record_request_time(self, response_time: float):
        """记录请求响应时间"""
        self.request_times.append(response_time)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics_history:
            return {"status": "no_data"}
        
        latest = self.metrics_history[-1]
        
        # 计算最近1小时的平均值
        hour_ago = datetime.now() - timedelta(hours=1)
        recent_metrics = [m for m in self.metrics_history if m.timestamp > hour_ago]
        
        if recent_metrics:
            avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
            avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
            avg_response = sum(m.avg_response_time for m in recent_metrics) / len(recent_metrics)
            total_slow = sum(m.slow_requests for m in recent_metrics)
        else:
            avg_cpu = latest.cpu_percent
            avg_memory = latest.memory_percent
            avg_response = latest.avg_response_time
            total_slow = latest.slow_requests
        
        return {
            "status": "ok",
            "current": {
                "cpu_percent": latest.cpu_percent,
                "memory_percent": latest.memory_percent,
                "memory_used_mb": latest.memory_used_mb,
                "avg_response_time": latest.avg_response_time,
                "slow_requests": latest.slow_requests
            },
            "hourly_average": {
                "cpu_percent": avg_cpu,
                "memory_percent": avg_memory,
                "avg_response_time": avg_response,
                "total_slow_requests": total_slow
            },
            "recommendations": self._get_performance_recommendations(latest)
        }
    
    def _get_performance_recommendations(self, metrics: PerformanceMetrics) -> list:
        """获取性能优化建议"""
        recommendations = []
        
        if metrics.cpu_percent > 70:
            recommendations.append("考虑增加CPU资源或优化计算密集型操作")
        
        if metrics.memory_percent > 80:
            recommendations.append("考虑增加内存或检查内存泄漏")
        
        if metrics.avg_response_time > 1.0:
            recommendations.append("优化数据库查询或增加缓存")
        
        if metrics.slow_requests > 5:
            recommendations.append("检查慢请求日志，优化相关接口")
        
        if not recommendations:
            recommendations.append("系统性能良好")
        
        return recommendations


# 全局性能监控实例
performance_monitor = PerformanceMonitor()


async def start_performance_monitoring():
    """启动性能监控"""
    asyncio.create_task(performance_monitor.start_monitoring())


def stop_performance_monitoring():
    """停止性能监控"""
    performance_monitor.stop_monitoring()


def record_request_performance(response_time: float):
    """记录请求性能"""
    performance_monitor.record_request_time(response_time)


def get_performance_status() -> Dict[str, Any]:
    """获取性能状态"""
    return performance_monitor.get_performance_summary()
