/**
 * 前端错误码常量定义
 * 与后端统一错误码体系保持一致
 */

// ==================== 错误码枚举 ====================

/**
 * 统一错误码枚举
 * 格式: MTTSS (模块+类型+序号)
 */
export enum ErrorCode {
  // ==================== 系统通用错误 (9xxxx) ====================
  
  // 系统验证错误 (901xx)
  INVALID_REQUEST_PARAMS = 90101,
  INVALID_JSON_FORMAT = 90102,
  MISSING_REQUIRED_FIELD = 90103,
  FIELD_VALUE_INVALID = 90104,
  FIELD_LENGTH_INVALID = 90105,
  
  // 系统权限错误 (902xx)
  UNAUTHORIZED = 90201,
  TOKEN_INVALID = 90202,
  TOKEN_EXPIRED = 90203,
  PERMISSION_DENIED = 90204,
  ACCESS_FORBIDDEN = 90205,
  
  // 系统资源错误 (903xx)
  RESOURCE_NOT_FOUND = 90301,
  ENDPOINT_NOT_FOUND = 90302,
  METHOD_NOT_ALLOWED = 90303,
  
  // 系统业务错误 (904xx)
  OPERATION_FAILED = 90401,
  RESOURCE_CONFLICT = 90402,
  OPERATION_NOT_ALLOWED = 90403,
  RATE_LIMIT_EXCEEDED = 90404,
  
  // 系统内部错误 (905xx)
  INTERNAL_SERVER_ERROR = 90501,
  DATABASE_ERROR = 90502,
  EXTERNAL_SERVICE_ERROR = 90503,
  CONFIGURATION_ERROR = 90504,
  NETWORK_ERROR = 90505,
  
  // ==================== 用户模块错误 (1xxxx) ====================
  
  // 用户验证错误 (101xx)
  USER_USERNAME_INVALID = 10101,
  USER_PASSWORD_INVALID = 10102,
  USER_EMAIL_INVALID = 10103,
  USER_PHONE_INVALID = 10104,
  USER_NICKNAME_INVALID = 10105,
  
  // 用户权限错误 (102xx)
  USER_LOGIN_REQUIRED = 10201,
  USER_LOGIN_FAILED = 10202,
  USER_PASSWORD_WRONG = 10203,
  USER_ACCOUNT_DISABLED = 10204,
  USER_ACCOUNT_LOCKED = 10205,
  
  // 用户资源错误 (103xx)
  USER_NOT_FOUND = 10301,
  USER_ROLE_NOT_FOUND = 10302,
  
  // 用户业务错误 (104xx)
  USER_ALREADY_EXISTS = 10401,
  USER_EMAIL_ALREADY_EXISTS = 10402,
  USER_CANNOT_DELETE_SELF = 10403,
  USER_CANNOT_MODIFY_ADMIN = 10404,
  
  // ==================== 环境模块错误 (2xxxx) ====================
  
  // 环境验证错误 (201xx)
  ENV_NAME_INVALID = 20101,
  ENV_TYPE_INVALID = 20102,
  ENV_CONFIG_INVALID = 20103,
  ENV_HOST_INVALID = 20104,
  ENV_PORT_INVALID = 20105,
  
  // 环境权限错误 (202xx)
  ENV_ACCESS_DENIED = 20201,
  ENV_OPERATION_FORBIDDEN = 20202,
  
  // 环境资源错误 (203xx)
  ENV_NOT_FOUND = 20301,
  ENV_CONFIG_NOT_FOUND = 20302,
  
  // 环境业务错误 (204xx)
  ENV_NAME_ALREADY_EXISTS = 20401,
  ENV_CONNECTION_FAILED = 20402,
  ENV_IN_USE = 20403,
  ENV_TEST_FAILED = 20404,
  
  // ==================== 混沌测试模块错误 (3xxxx) ====================
  
  // 混沌测试验证错误 (301xx)
  CHAOS_TASK_NAME_INVALID = 30101,
  CHAOS_FAULT_TYPE_INVALID = 30102,
  CHAOS_PARAMS_INVALID = 30103,
  CHAOS_SCHEDULE_INVALID = 30104,
  
  // 混沌测试权限错误 (302xx)
  CHAOS_TASK_ACCESS_DENIED = 30201,
  CHAOS_EXECUTION_FORBIDDEN = 30202,
  
  // 混沌测试资源错误 (303xx)
  CHAOS_TASK_NOT_FOUND = 30301,
  CHAOS_SCENARIO_NOT_FOUND = 30302,
  CHAOS_EXECUTION_NOT_FOUND = 30303,
  
  // 混沌测试业务错误 (304xx)
  CHAOS_TASK_ALREADY_RUNNING = 30401,
  CHAOS_TASK_CANNOT_EXECUTE = 30402,
  CHAOS_BLADE_NOT_INSTALLED = 30403,
  CHAOS_EXECUTION_FAILED = 30404,
  
  // ==================== 数据工厂模块错误 (4xxxx) ====================
  
  // 数据工厂验证错误 (401xx)
  DATA_MODEL_NAME_INVALID = 40101,
  DATA_FIELD_CONFIG_INVALID = 40102,
  DATA_GENERATION_PARAMS_INVALID = 40103,
  
  // 数据工厂权限错误 (402xx)
  DATA_MODEL_ACCESS_DENIED = 40201,
  DATA_GENERATION_FORBIDDEN = 40202,
  
  // 数据工厂资源错误 (403xx)
  DATA_MODEL_NOT_FOUND = 40301,
  DATA_GENERATION_TASK_NOT_FOUND = 40302,
  
  // 数据工厂业务错误 (404xx)
  DATA_MODEL_NAME_EXISTS = 40401,
  DATA_GENERATION_FAILED = 40402,
  DATA_EXPORT_FAILED = 40403,
  
  // ==================== 模型配置模块错误 (5xxxx) ====================
  
  // 模型配置验证错误 (501xx)
  MODEL_NAME_INVALID = 50101,
  MODEL_PLATFORM_INVALID = 50102,
  MODEL_API_URL_INVALID = 50103,
  MODEL_API_KEY_INVALID = 50104,
  MODEL_CONFIG_INVALID = 50105,
  
  // 模型配置权限错误 (502xx)
  MODEL_ACCESS_DENIED = 50201,
  MODEL_CALL_FORBIDDEN = 50202,
  
  // 模型配置资源错误 (503xx)
  MODEL_NOT_FOUND = 50301,
  MODEL_CONFIG_NOT_FOUND = 50302,
  
  // 模型配置业务错误 (504xx)
  MODEL_NAME_ALREADY_EXISTS = 50401,
  MODEL_CONNECTION_FAILED = 50402,
  MODEL_CALL_FAILED = 50403,
  MODEL_HEALTH_CHECK_FAILED = 50404
}

// ==================== 错误码分类 ====================

/**
 * 错误码类型枚举
 */
export enum ErrorType {
  VALIDATION = 'validation',      // 验证错误
  PERMISSION = 'permission',      // 权限错误
  NOT_FOUND = 'not_found',       // 资源不存在
  BUSINESS = 'business',         // 业务逻辑错误
  SYSTEM = 'system'              // 系统内部错误
}

/**
 * 错误码模块枚举
 */
export enum ErrorModule {
  SYSTEM = 'system',             // 系统模块
  USER = 'user',                 // 用户模块
  ENVIRONMENT = 'environment',   // 环境模块
  CHAOS = 'chaos',              // 混沌测试模块
  DATA_FACTORY = 'data_factory', // 数据工厂模块
  MODEL = 'model'               // 模型配置模块
}

// ==================== 工具函数 ====================

/**
 * 获取错误码的模块
 */
export function getErrorModule(errorCode: number): ErrorModule {
  const moduleCode = Math.floor(errorCode / 10000)
  switch (moduleCode) {
    case 1: return ErrorModule.USER
    case 2: return ErrorModule.ENVIRONMENT
    case 3: return ErrorModule.CHAOS
    case 4: return ErrorModule.DATA_FACTORY
    case 5: return ErrorModule.MODEL
    case 9: return ErrorModule.SYSTEM
    default: return ErrorModule.SYSTEM
  }
}

/**
 * 获取错误码的类型
 */
export function getErrorType(errorCode: number): ErrorType {
  const typeCode = Math.floor((errorCode % 10000) / 100)
  switch (typeCode) {
    case 1: return ErrorType.VALIDATION
    case 2: return ErrorType.PERMISSION
    case 3: return ErrorType.NOT_FOUND
    case 4: return ErrorType.BUSINESS
    case 5: return ErrorType.SYSTEM
    default: return ErrorType.SYSTEM
  }
}

/**
 * 判断是否为认证相关错误
 */
export function isAuthError(errorCode: number): boolean {
  return [
    ErrorCode.UNAUTHORIZED,
    ErrorCode.TOKEN_INVALID,
    ErrorCode.TOKEN_EXPIRED,
    ErrorCode.USER_LOGIN_REQUIRED,
    ErrorCode.USER_LOGIN_FAILED,
    ErrorCode.USER_PASSWORD_WRONG
  ].includes(errorCode)
}

/**
 * 判断是否为权限相关错误
 */
export function isPermissionError(errorCode: number): boolean {
  return getErrorType(errorCode) === ErrorType.PERMISSION
}

/**
 * 判断是否为验证相关错误
 */
export function isValidationError(errorCode: number): boolean {
  return getErrorType(errorCode) === ErrorType.VALIDATION
}

/**
 * 判断是否为系统错误
 */
export function isSystemError(errorCode: number): boolean {
  return getErrorType(errorCode) === ErrorType.SYSTEM || getErrorModule(errorCode) === ErrorModule.SYSTEM
}
